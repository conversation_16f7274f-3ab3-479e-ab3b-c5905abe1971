FROM node:18-alpine

# Install system dependencies for document processing and legal analysis
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    poppler-utils \
    unoconv \
    libreoffice \
    git \
    bash

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs
RUN mkdir -p /app/temp
RUN mkdir -p /app/processed
RUN mkdir -p /app/legal-db
RUN mkdir -p /app/precedent-cache

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port for health checks
EXPOSE 8091

# Health check endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8091/health || exit 1

# Environment variables
ENV NODE_ENV=production
ENV HEALTH_PORT=8091
ENV LOG_LEVEL=info

# Start the server
CMD ["node", "server.js"]