/**
 * Test Runner Script
 * 
 * This script provides a more reliable way to run Jest tests with proper
 * ESM support and error handling.
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Get the directory of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Set NODE_OPTIONS to ensure proper ESM support
process.env.NODE_OPTIONS = '--experimental-vm-modules';

console.log('🧪 Running tests for n8n MCP Server...');

// Get command line arguments to pass to Jest
const args = process.argv.slice(2);
const jestArgs = ['--config', './jest.config.cjs', ...args];

// Spawn Jest process
const jestProcess = spawn('node_modules/.bin/jest', jestArgs, {
  stdio: 'inherit',
  cwd: __dirname,
  env: { ...process.env, NODE_ENV: 'test' }
});

// Handle process events
jestProcess.on('error', (error) => {
  console.error('Error running tests:', error);
  process.exit(1);
});

jestProcess.on('close', (code) => {
  if (code !== 0) {
    console.error(`Test process exited with code ${code}`);
    process.exit(code);
  }
  console.log('✅ Tests completed successfully');
});
