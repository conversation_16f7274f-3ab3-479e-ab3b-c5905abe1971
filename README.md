# Political Document Processing System - n8n Workflow Automation

## 🎯 Mission Statement

This revolutionary political document processing system embodies <PERSON>'s vision for economic justice and democratic renewal. Using cutting-edge AI technology and n8n workflow automation, it transforms political document creation to rival the output of top think tanks while maintaining authentic voice and manifesto alignment.

## ✨ Features and Capabilities

### 🤖 AI-Powered Document Processing
- **Multi-Agent AI System** - Specialized agents for research, content generation, and quality control
- **Dynamic Token Allocation** - 4-tier system (5K-50K tokens) for optimal quality/cost balance
- **Manifesto Integration** - Every output embodies <PERSON>'s political vision and voice
- **Professional Research** - Real-time web research, fact-checking, and international analysis

### 🔄 Workflow Automation
- **n8n Orchestration** - Complete document processing pipeline automation
- **Google Drive Integration** - Seamless file monitoring and output delivery
- **CloudConvert Processing** - Professional DOCX formatting and templates
- **Error Handling** - Comprehensive monitoring and recovery systems

### 💬 Conversational Interface
- **Interactive Chat** - Natural language interaction with document system
- **Memory & Context** - Cross-session conversation continuity and learning
- **Document Q&A** - Ask questions about your political documents and policies
- **Citation & References** - Complete source tracking and verification

### 🌐 Enhanced MCP Ecosystem
- **8 Specialized Servers** - Web research, economic analysis, legal review, and more
- **Real-time Research** - Access to government databases, academic sources, international policies
- **Fact-Checking** - Multi-source verification and accuracy validation
- **International Intelligence** - Translation services and global best practices

## 🏗️ Architecture Overview

```
Google Drive (political_in/) ←→ Conversational Chat Interface
    ↓                              ↓
n8n File Monitor & Classification  Session Management & Memory
    ↓                              ↓
AI Agent Analysis (via MCP) ←→ Document Q&A Agent
    ↓                              ↓
Task Routing (Edit/Combine/Generate/Create/Chat)
    ↓
Specialized AI Processing (with Manifesto Context)
    ↓
Quality Control Agent Review
    ↓
CloudConvert (MD → DOCX)
    ↓
Google Drive (political_out/)
    ↓
Email Notification + Chat Response
```

### Infrastructure Components
- **n8n Workflow Engine** - Orchestrates entire system
- **PostgreSQL** - Conversation memory and session management
- **ChromaDB** - Vector database for intelligent document retrieval
- **Redis** - Caching and session management
- **Docker** - Containerized deployment

## 🚀 Installation Instructions

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for chat interface)
- Python 3.9+ (for MCP servers)
- Git

### Quick Start
```bash
# Clone the repository
git clone https://github.com/Beaulewis1977/n8n-workflow.git
cd n8n-workflow

# Copy environment variables
cp .env.example .env
# Edit .env with your API keys and configuration

# Start the infrastructure
docker-compose up -d

# Install chat interface dependencies
cd chat-interface
npm install
npm start

# Access n8n at http://localhost:5678
# Access chat interface at http://localhost:3000
```

### Configuration Guide

#### Required API Keys
```bash
# Google Drive API
GOOGLE_DRIVE_CLIENT_ID=your_client_id
GOOGLE_DRIVE_CLIENT_SECRET=your_client_secret
GOOGLE_DRIVE_REFRESH_TOKEN=your_refresh_token

# CloudConvert API
CLOUDCONVERT_API_KEY=your_api_key

# AI Model APIs
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_AI_API_KEY=your_google_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
```

#### Manifesto System Setup
1. Place manifesto documents in `manifesto/` directory
2. Category supplements go in `manifesto/category-supplements/`
3. Voice guidelines and style guides in main manifesto folder
4. System automatically loads appropriate context based on task complexity

## 📖 Usage Examples

### Document Processing
1. **Drop File**: Place .md or .txt file in Google Drive `political_in/` folder
2. **Automatic Processing**: System classifies document and routes to appropriate AI agents
3. **Quality Control**: Multi-layer validation ensures manifesto alignment and accuracy
4. **Professional Output**: Receives DOCX file in `political_out/` folder with email notification

### Conversational Interface
```
User: "What does my healthcare white paper say about funding mechanisms?"

System: Based on your "Universal Healthcare Implementation Plan":
• ASTF provides 60% of funding through automation taxes and resource revenue
• Federal allocation covers 25% through progressive taxation
• State contributions handle 15% with federal support for lower-income states

The document specifically notes: "Following the Scandinavian model, funding comes from collective investment in our shared prosperity..."

Would you like me to explain the transition timeline or compare with international models?
```

### Policy Development
- Ask questions about document relationships and consistency
- Request policy gap analysis and recommendations
- Get messaging strategy support for different audiences
- Access real-time research and fact-checking

## 🔧 API Documentation

### MCP Tool Definitions

#### Document Intent Analyzer
```json
{
  "name": "document_intent_analyzer",
  "description": "Analyzes document content and determines processing strategy",
  "parameters": {
    "document_content": "string",
    "manifesto_context": "string", 
    "filename": "string"
  }
}
```

#### Content Generator
```json
{
  "name": "content_generator",
  "description": "Generates political content aligned with manifesto principles",
  "parameters": {
    "content_type": "white_paper|policy_brief|summary|implementation_plan",
    "source_content": "string",
    "manifesto_context": "string",
    "instructions": "string"
  }
}
```

#### Quality Control Agent
```json
{
  "name": "quality_control_agent", 
  "description": "Reviews content for manifesto alignment and quality standards",
  "parameters": {
    "content": "string",
    "manifesto_context": "string",
    "quality_standards": "string"
  }
}
```

### Webhook Endpoints
- `/webhook/document-upload` - Document processing trigger
- `/webhook/chat` - Conversational interface
- `/webhook/quality-feedback` - User feedback integration

## 🤝 Contributing Guidelines

### Development Workflow
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'feat: add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Standards
- Follow n8n workflow best practices
- Use descriptive node names and comments
- Implement proper error handling
- Add comprehensive tests
- Document all APIs and interfaces

### Testing Requirements
- Unit tests for MCP servers (>90% coverage)
- Integration tests for n8n workflows
- End-to-end tests for complete system
- Performance tests for scalability

## 📄 License and Legal

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.

### Usage for Political Purposes
This system is designed specifically for advancing economic justice and democratic renewal. While the code is open source, the manifesto integration and political framework embody Beau Lewis's specific vision for America.

### Data Privacy
- All conversations and documents are stored securely
- No data is shared with third parties without explicit consent
- Local deployment options available for sensitive content

## 🆘 Support and Contact

### Technical Support
- **Issues**: Submit GitHub issues for bugs and feature requests
- **Documentation**: See `docs/` directory for detailed guides
- **Troubleshooting**: Check `docs/troubleshooting.md`

### Political Movement
- **Campaign**: <EMAIL>
- **Website**: [BeauLewis.com](https://beaulewis.com)
- **Movement Building**: Join the American Social Trust Fund movement

## 📋 Changelog and Roadmap

### Version 1.0.0 (Current)
- ✅ Complete n8n workflow system
- ✅ 8 specialized MCP servers
- ✅ Conversational interface with memory
- ✅ Dynamic token allocation (4-tier system)
- ✅ Professional DOCX output formatting

### Planned Features (v1.1.0)
- 🔄 Advanced analytics dashboard
- 🔄 Mobile-responsive chat interface
- 🔄 Advanced messaging strategy tools
- 🔄 Real-time collaboration features

### Long-term Vision (v2.0.0)
- 🚀 Multi-user political campaign platform
- 🚀 Advanced AI research assistants
- 🚀 Integration with voter outreach systems
- 🚀 Comprehensive movement building tools

---

## 🇺🇸 Building the Movement

This technology serves a mission bigger than document processing - we're building the foundation for a political movement that can transform America. Every document generated, every conversation facilitated, every policy developed advances the cause of economic justice and democratic renewal.

**Together, we will build an America that works for everyone.**

---

*"The future of American democracy may well depend on how faithfully this system carries the message forward."*