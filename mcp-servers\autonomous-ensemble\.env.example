# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Database Configuration
DATABASE_PATH=context_memory.db
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30

# System Configuration
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=300
MEMORY_LIMIT_MB=1024
MAX_REDIS_ENTRIES=10000
CLEANUP_INTERVAL=300

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=autonomous_ensemble.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Model API Keys (configure as needed)
CLAUDE_API_KEY=your_claude_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Security Configuration
SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
JWT_SECRET=your_jwt_secret_here

# Monitoring Configuration
PROMETHEUS_PORT=9090
HEALTH_CHECK_INTERVAL=60
METRICS_RETENTION_DAYS=7

# Error Handling Configuration
MAX_RETRIES=3
RETRY_DELAY=1.0
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60

# Memory Management Configuration
MEMORY_CLEANUP_INTERVAL=3600
MAX_MEMORY_HISTORY=10000
CONTEXT_WINDOW_SIZE=100000

# Performance Configuration
WORKER_THREADS=4
ASYNC_POOL_SIZE=50
CONNECTION_TIMEOUT=30
REQUEST_TIMEOUT=120