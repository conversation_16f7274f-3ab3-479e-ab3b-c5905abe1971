-- Migration: Create indexed_documents table for vector search tracking
-- Date: 2025-07-17
-- Description: Table to track documents indexed in ChromaDB for vector search

CREATE TABLE IF NOT EXISTS indexed_documents (
    document_id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(500),
    category VARCHAR(100),
    document_type VARCHAR(50) DEFAULT 'general',
    file_path VARCHAR(1000),
    chunk_count INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index for faster category-based queries
CREATE INDEX IF NOT EXISTS idx_indexed_documents_category ON indexed_documents(category);

-- Index for faster document type queries
CREATE INDEX IF NOT EXISTS idx_indexed_documents_type ON indexed_documents(document_type);

-- Index for faster timestamp-based queries
CREATE INDEX IF NOT EXISTS idx_indexed_documents_indexed_at ON indexed_documents(indexed_at);

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_indexed_documents_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_indexed_documents_updated_at
    BEFORE UPDATE ON indexed_documents
    FOR EACH ROW
    EXECUTE FUNCTION update_indexed_documents_updated_at();

-- Insert initial comment
COMMENT ON TABLE indexed_documents IS 'Tracks political documents indexed in ChromaDB for vector search';
COMMENT ON COLUMN indexed_documents.document_id IS 'Unique identifier for the indexed document';
COMMENT ON COLUMN indexed_documents.title IS 'Human-readable title of the document';
COMMENT ON COLUMN indexed_documents.category IS 'Political category (healthcare, education, etc.)';
COMMENT ON COLUMN indexed_documents.document_type IS 'Type of document (manifesto, whitepaper, etc.)';
COMMENT ON COLUMN indexed_documents.file_path IS 'Original file path of the document';
COMMENT ON COLUMN indexed_documents.chunk_count IS 'Number of chunks created from this document';
COMMENT ON COLUMN indexed_documents.total_tokens IS 'Total token count for all chunks';
COMMENT ON COLUMN indexed_documents.indexed_at IS 'When the document was indexed';
COMMENT ON COLUMN indexed_documents.updated_at IS 'When the document was last updated';
COMMENT ON COLUMN indexed_documents.created_at IS 'When the record was created';