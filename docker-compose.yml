version: '3.8'

services:
  # n8n Workflow Engine
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-political
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD:-changeme123}
      - WEBHOOK_URL=http://localhost:5678/
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY:-your-encryption-key}
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgresql
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n_user
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - N8N_METRICS=true
      - N8N_LOG_LEVEL=info
      # MCP Server Configuration
      - MCP_SERVER_URL=http://mcp-main:8080
      - MCP_SERVER_API_KEY=${MCP_SERVER_API_KEY:-your-mcp-key}
      # AI Model APIs
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      # Google Drive API
      - GOOGLE_DRIVE_CLIENT_ID=${GOOGLE_DRIVE_CLIENT_ID}
      - GOOGLE_DRIVE_CLIENT_SECRET=${GOOGLE_DRIVE_CLIENT_SECRET}
      - GOOGLE_DRIVE_REFRESH_TOKEN=${GOOGLE_DRIVE_REFRESH_TOKEN}
      # CloudConvert API
      - CLOUDCONVERT_API_KEY=${CLOUDCONVERT_API_KEY}
      # Email Configuration
      - SMTP_HOST=${SMTP_HOST:-smtp.gmail.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./manifesto:/home/<USER>/.n8n/manifesto:ro
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
      - chromadb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgresql:
    image: postgres:15-alpine
    container_name: postgres-political
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - POSTGRES_MULTIPLE_DATABASES=political_conversations,manifesto_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./database/migrations:/docker-entrypoint-initdb.d/migrations
    networks:
      - political-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_user -d n8n"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ChromaDB Vector Database
  chromadb:
    image: chromadb/chroma:latest
    container_name: chromadb-political
    ports:
      - "8000:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - PERSIST_DIRECTORY=/chroma/chroma
    volumes:
      - chromadb_data:/chroma/chroma
    networks:
      - political-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache and Session Management
  redis:
    image: redis:7-alpine
    container_name: redis-political
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    networks:
      - political-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Main MCP Server (AI Agent Coordination)
  mcp-main:
    build:
      context: ./mcp-servers/main
      dockerfile: Dockerfile
    container_name: mcp-main-political
    ports:
      - "8080:8080"
    environment:
      - MCP_SERVER_PORT=8080
      - POSTGRES_URL=postgresql://n8n_user:${POSTGRES_PASSWORD:-secure_password}@postgresql:5432/political_conversations
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
      - CHROMADB_URL=http://chromadb:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
    volumes:
      - ./manifesto:/app/manifesto:ro
      - ./mcp-servers/main/logs:/app/logs
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
      - chromadb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Research MCP Server
  mcp-web-research:
    build:
      context: ./mcp-servers/web-research
      dockerfile: Dockerfile
    container_name: mcp-web-research
    ports:
      - "8081:8081"
    environment:
      - MCP_SERVER_PORT=8081
      - BRAVE_API_KEY=${BRAVE_API_KEY}
      - SERP_API_KEY=${SERP_API_KEY}
      - PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
    volumes:
      - ./mcp-servers/web-research/logs:/app/logs
    networks:
      - political-network
    restart: unless-stopped

  # Economic Analysis MCP Server
  mcp-economic-analysis:
    build:
      context: ./mcp-servers/economic-analysis
      dockerfile: Dockerfile
    container_name: mcp-economic-analysis
    ports:
      - "8082:8082"
    environment:
      - MCP_SERVER_PORT=8082
      - FRED_API_KEY=${FRED_API_KEY}
      - BLS_API_KEY=${BLS_API_KEY}
      - WORLD_BANK_API_KEY=${WORLD_BANK_API_KEY}
    volumes:
      - ./mcp-servers/economic-analysis/logs:/app/logs
      - ./mcp-servers/economic-analysis/models:/app/models
    networks:
      - political-network
    restart: unless-stopped

  # Legal Analysis MCP Server
  mcp-legal-analysis:
    build:
      context: ./mcp-servers/legal-analysis
      dockerfile: Dockerfile
    container_name: mcp-legal-analysis
    ports:
      - "8083:8083"
    environment:
      - MCP_SERVER_PORT=8083
      - LEGAL_DATABASE_URL=${LEGAL_DATABASE_URL}
      - WESTLAW_API_KEY=${WESTLAW_API_KEY}
    volumes:
      - ./mcp-servers/legal-analysis/logs:/app/logs
    networks:
      - political-network
    restart: unless-stopped

  # International Research MCP Server
  mcp-international-research:
    build:
      context: ./mcp-servers/international-research
      dockerfile: Dockerfile
    container_name: mcp-international-research
    ports:
      - "8084:8084"
    environment:
      - MCP_SERVER_PORT=8084
      - GOOGLE_TRANSLATE_API_KEY=${GOOGLE_TRANSLATE_API_KEY}
      - DEEPL_API_KEY=${DEEPL_API_KEY}
      - OECD_API_KEY=${OECD_API_KEY}
    volumes:
      - ./mcp-servers/international-research/logs:/app/logs
    networks:
      - political-network
    restart: unless-stopped

  # Document Intelligence MCP Server
  mcp-document-intelligence:
    build:
      context: ./mcp-servers/document-intelligence
      dockerfile: Dockerfile
    container_name: mcp-document-intelligence
    ports:
      - "8085:8085"
    environment:
      - MCP_SERVER_PORT=8085
      - CHROMADB_URL=http://chromadb:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./mcp-servers/document-intelligence/logs:/app/logs
      - chromadb_data:/shared/chromadb:ro
    networks:
      - political-network
    depends_on:
      - chromadb
    restart: unless-stopped

  # Memory & Context MCP Server
  mcp-memory-context:
    build:
      context: ./mcp-servers/memory-context
      dockerfile: Dockerfile
    container_name: mcp-memory-context
    ports:
      - "8086:8086"
    environment:
      - MCP_SERVER_PORT=8086
      - POSTGRES_URL=postgresql://n8n_user:${POSTGRES_PASSWORD:-secure_password}@postgresql:5432/political_conversations
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379
    volumes:
      - ./mcp-servers/memory-context/logs:/app/logs
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
    restart: unless-stopped

  # Fact-Checking MCP Server
  mcp-fact-checking:
    build:
      context: ./mcp-servers/fact-checking
      dockerfile: Dockerfile
    container_name: mcp-fact-checking
    ports:
      - "8087:8087"
    environment:
      - MCP_SERVER_PORT=8087
      - FACT_CHECK_API_KEY=${FACT_CHECK_API_KEY}
      - SNOPES_API_KEY=${SNOPES_API_KEY}
    volumes:
      - ./mcp-servers/fact-checking/logs:/app/logs
    networks:
      - political-network
    restart: unless-stopped

  # Social Monitoring MCP Server
  mcp-social-monitoring:
    build:
      context: ./mcp-servers/social-monitoring
      dockerfile: Dockerfile
    container_name: mcp-social-monitoring
    ports:
      - "8088:8088"
    environment:
      - MCP_SERVER_PORT=8088
      - TWITTER_API_KEY=${TWITTER_API_KEY}
      - REDDIT_API_KEY=${REDDIT_API_KEY}
      - SENTIMENT_API_KEY=${SENTIMENT_API_KEY}
    volumes:
      - ./mcp-servers/social-monitoring/logs:/app/logs
    networks:
      - political-network
    restart: unless-stopped

  # Vector Search MCP Server (ChromaDB RAG)
  mcp-vector-search:
    build:
      context: ./mcp-servers/vector-search
      dockerfile: Dockerfile
    container_name: mcp-vector-search
    ports:
      - "8089:8088"
    environment:
      - MCP_SERVER_PORT=8088
      - CHROMADB_URL=http://chromadb:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - POSTGRES_HOST=postgresql
      - POSTGRES_PORT=5432
      - POSTGRES_DB=political_conversations
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
    volumes:
      - ./mcp-servers/vector-search/logs:/app/logs
      - ./manifesto:/app/manifesto:ro
      - ./white_papers_markdown:/app/white_papers_markdown:ro
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
      - chromadb
    restart: unless-stopped

  # Secure Analytics MCP Server (OAuth 2.1 with Real-time Monitoring)
  mcp-analytics-secure:
    build:
      context: ./mcp-servers/analytics-secure
      dockerfile: Dockerfile
    container_name: mcp-analytics-secure
    ports:
      - "8090:8090"
    environment:
      - MCP_SERVER_PORT=8090
      - NODE_ENV=production
      - LOG_LEVEL=info
      # Database Configuration
      - POSTGRES_HOST=postgresql
      - POSTGRES_PORT=5432
      - POSTGRES_DB=political_conversations
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      # OAuth 2.1 Configuration
      - OAUTH_ISSUER=https://analytics.political-system.local
      - OAUTH_CLIENT_ID=analytics-mcp-client
      - OAUTH_CLIENT_SECRET=${OAUTH_CLIENT_SECRET:-secure_oauth_secret_2025}
      - OAUTH_REDIRECT_URI=http://localhost:8090/auth/callback
      # JWT Security
      - JWT_SECRET=${JWT_SECRET:-ultra_secure_jwt_secret_2025_analytics}
      - SESSION_SECRET=${SESSION_SECRET:-ultra_secure_session_secret_2025}
      # Security Configuration
      - TOKEN_EXPIRY=3600
      - REFRESH_TOKEN_EXPIRY=604800
      - ANALYTICS_RETENTION_DAYS=90
      - AGGREGATION_INTERVAL=300000
      # CORS Origins
      - ALLOWED_ORIGINS=http://localhost:3001,http://localhost:5678,http://localhost:3000
    volumes:
      - ./mcp-servers/analytics-secure/logs:/app/logs
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
      - prometheus
      - grafana
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Multimodal ChromaDB MCP Server (Images, Video, Voice Processing)
  mcp-multimodal-chromadb:
    build:
      context: ./mcp-servers/multimodal-chromadb
      dockerfile: Dockerfile
    container_name: mcp-multimodal-chromadb
    ports:
      - "8091:8091"
    environment:
      - MCP_SERVER_PORT=8091
      - NODE_ENV=production
      - LOG_LEVEL=info
      # Database Configuration
      - CHROMADB_URL=http://chromadb:8000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      # Security Configuration
      - ALLOWED_ORIGINS=http://localhost:3001,http://localhost:5678,http://localhost:3000
      # Processing Configuration
      - MAX_FILE_SIZE=104857600
      - MAX_FILES_PER_UPLOAD=10
      - TEMP_DIR=/tmp/multimodal
      # Audio Processing
      - WHISPER_MODEL=whisper-1
      - AUDIO_SAMPLE_RATE=16000
      # Video Processing
      - VIDEO_FRAME_EXTRACTION_COUNT=10
      - VIDEO_FRAME_SIZE=640x480
      # Image Processing
      - IMAGE_MAX_DIMENSIONS=1024x1024
      - IMAGE_QUALITY=90
    volumes:
      - ./mcp-servers/multimodal-chromadb/logs:/app/logs
      - chromadb_data:/shared/chromadb
    networks:
      - political-network
    depends_on:
      - chromadb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8091/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Voice Processing MCP Server (Real-time Speech-to-Text)
  mcp-voice-processing:
    build:
      context: ./mcp-servers/voice-processing
      dockerfile: Dockerfile
    container_name: mcp-voice-processing
    ports:
      - "8092:8092"
    environment:
      - MCP_SERVER_PORT=8092
      - NODE_ENV=production
      - LOG_LEVEL=info
      # Database Configuration
      - POSTGRES_HOST=postgresql
      - POSTGRES_PORT=5432
      - POSTGRES_DB=political_conversations
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      # AI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      # OAuth 2.1 Configuration
      - OAUTH_ISSUER=https://voice.political-system.local
      - OAUTH_CLIENT_ID=voice-mcp-client
      - OAUTH_CLIENT_SECRET=${OAUTH_CLIENT_SECRET:-secure_oauth_secret_2025}
      - OAUTH_REDIRECT_URI=http://localhost:8092/auth/callback
      # JWT Security
      - JWT_SECRET=${JWT_SECRET:-ultra_secure_jwt_secret_2025_voice}
      - SESSION_SECRET=${SESSION_SECRET:-ultra_secure_session_secret_2025}
      # Processing Configuration
      - MAX_AUDIO_SIZE=52428800
      - WHISPER_MODEL=whisper-1
      - AUDIO_SAMPLE_RATE=16000
      - CHUNK_SIZE=1024
      # Security Configuration
      - ALLOWED_ORIGINS=http://localhost:3001,http://localhost:5678,http://localhost:3000
    volumes:
      - ./mcp-servers/voice-processing/logs:/app/logs
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8092/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Autonomous Ensemble MCP Server (Multi-Agent Orchestration)
  mcp-autonomous-ensemble:
    build:
      context: ./mcp-servers/autonomous-ensemble
      dockerfile: Dockerfile
    container_name: mcp-autonomous-ensemble
    ports:
      - "8093:8093"
    environment:
      - MCP_SERVER_PORT=8093
      - NODE_ENV=production
      - LOG_LEVEL=info
      # Database Configuration
      - POSTGRES_HOST=postgresql
      - POSTGRES_PORT=5432
      - POSTGRES_DB=political_conversations
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-secure_password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      # AI Model Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      # Circuit Breaker Configuration
      - CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
      - CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60000
      - CIRCUIT_BREAKER_MONITOR_TIMEOUT=30000
      # Performance Configuration
      - MAX_CONCURRENT_TASKS=10
      - TASK_TIMEOUT=300000
      - RETRY_ATTEMPTS=3
      - RETRY_DELAY=1000
      # Security Configuration
      - ALLOWED_ORIGINS=http://localhost:3001,http://localhost:5678,http://localhost:3000
    volumes:
      - ./mcp-servers/autonomous-ensemble/logs:/app/logs
    networks:
      - political-network
    depends_on:
      - postgresql
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8093/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  n8n-mcp:
    command: npx n8n-mcp
    environment:
      MCP_MODE: stdio
      LOG_LEVEL: error
      DISABLE_CONSOLE_OUTPUT: "true"
      N8N_API_URL: https://kngpnn.app.n8n.cloud/api/v1
      N8N_API_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.yVD9u-iB6tXOKzx-G63_O_paAEpW5zkK1el5hUhw_w0
    networks:
      - political-network
    restart: unless-stopped

  # Chat Interface (React Application)
  chat-interface:
    build:
      context: ./chat-interface
      dockerfile: Dockerfile
    container_name: chat-interface-political
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_N8N_WEBHOOK_URL=http://localhost:5678/webhook/chat
      - REACT_APP_API_URL=http://localhost:8080
      - DATABASE_URL=postgresql://n8n_user:${POSTGRES_PASSWORD:-secure_password}@postgresql:5432/political_conversations
    volumes:
      - ./chat-interface:/app
      - /app/node_modules
    networks:
      - political-network
    depends_on:
      - n8n
      - mcp-main
    restart: unless-stopped
    command: npm start

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: nginx-political
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - political-network
    depends_on:
      - n8n
      - chat-interface
    restart: unless-stopped
    profiles:
      - production

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-political
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - political-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-political
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - political-network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  n8n_data:
    driver: local
  postgres_data:
    driver: local
  chromadb_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  political-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16