#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import { OpenAI } from 'openai';
import axios from 'axios';
import winston from 'winston';
import cheerio from 'cheerio';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import natural from 'natural';

/**
 * Fact Checking MCP Server
 * Advanced fact verification, source validation, and claim analysis for political content
 */

class FactCheckingServer {
  constructor() {
    this.server = new MCPServer({
      name: 'fact-checking',
      version: '1.0.0'
    });
    
    // Initialize AI clients
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.perplexityKey = process.env.PERPLEXITY_API_KEY;
    this.tokenizer = encoding_for_model('gpt-4');
    this.factCache = new Map();
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    // Trusted sources for fact checking
    this.trustedSources = [
      'factcheck.org',
      'snopes.com',
      'politifact.com',
      'reuters.com',
      'ap.org',
      'bbc.com',
      'npr.org',
      'cnn.com',
      'nytimes.com',
      'washingtonpost.com'
    ];
    
    // Setup logging
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: '/app/logs/fact-checking.log' })
      ]
    });

    this.setupTools();
    this.setupResources();
    this.setupHealthEndpoint();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'fact_checking',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      await this.pgClient.connect();
      await this.redisClient.connect();
      this.logger.info('Fact Checking MCP Server database connections established');
      
      await this.initializeTables();
    } catch (error) {
      this.logger.error('Failed to initialize:', error);
      throw error;
    }
  }

  async initializeTables() {
    try {
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS fact_checks (
          id SERIAL PRIMARY KEY,
          claim_id VARCHAR(255) UNIQUE,
          original_claim TEXT,
          claim_hash VARCHAR(64),
          verification_result JSONB,
          credibility_score DECIMAL(3,2),
          sources_checked INTEGER DEFAULT 0,
          verification_status VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS source_validations (
          id SERIAL PRIMARY KEY,
          claim_id VARCHAR(255),
          source_url VARCHAR(1000),
          source_domain VARCHAR(255),
          credibility_rating VARCHAR(20),
          relevance_score DECIMAL(3,2),
          content_snippet TEXT,
          validation_method VARCHAR(100),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS political_claims (
          id SERIAL PRIMARY KEY,
          claim_text TEXT,
          claim_category VARCHAR(100),
          political_figure VARCHAR(255),
          date_made DATE,
          context TEXT,
          fact_check_id VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
    } catch (error) {
      this.logger.error('Error initializing tables:', error);
      throw error;
    }
  }

  setupTools() {
    // Tool 1: Fact Verify - Verify claims against reliable sources
    this.server.addTool({
      name: 'fact_verify',
      description: 'Verify claims against reliable sources using comprehensive fact-checking methodology',
      inputSchema: {
        type: 'object',
        properties: {
          claim: {
            type: 'string',
            description: 'The political claim to verify'
          },
          context: {
            type: 'string',
            description: 'Additional context about when/where the claim was made'
          },
          political_figure: {
            type: 'string',
            description: 'Person who made the claim'
          },
          urgency_level: {
            type: 'string',
            enum: ['low', 'medium', 'high', 'critical'],
            description: 'Urgency of fact check',
            default: 'medium'
          },
          check_depth: {
            type: 'string',
            enum: ['quick', 'standard', 'comprehensive'],
            description: 'Depth of fact checking',
            default: 'standard'
          }
        },
        required: ['claim']
      }
    }, this.factVerify.bind(this));

    // Tool 2: Source Credibility Check - Evaluate source credibility and bias
    this.server.addTool({
      name: 'source_credibility_check',
      description: 'Evaluate the credibility, reliability, and bias of information sources',
      inputSchema: {
        type: 'object',
        properties: {
          source_url: {
            type: 'string',
            description: 'URL of the source to check'
          },
          source_content: {
            type: 'string',
            description: 'Content from the source (alternative to URL)'
          },
          check_factors: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['domain_reputation', 'author_expertise', 'bias_analysis', 'factual_accuracy', 'publication_quality']
            },
            description: 'Factors to evaluate'
          }
        }
      }
    }, this.sourceCredibilityCheck.bind(this));

    // Tool 3: Claim Extraction - Extract claims from text for verification
    this.server.addTool({
      name: 'claim_extraction',
      description: 'Extract verifiable claims from text for fact-checking analysis',
      inputSchema: {
        type: 'object',
        properties: {
          text_content: {
            type: 'string',
            description: 'Text to analyze for claims'
          },
          claim_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['statistical', 'policy', 'historical', 'comparative', 'predictive']
            },
            description: 'Types of claims to extract'
          },
          min_confidence: {
            type: 'number',
            minimum: 0.0,
            maximum: 1.0,
            default: 0.7,
            description: 'Minimum confidence threshold for claim extraction'
          }
        },
        required: ['text_content']
      }
    }, this.claimExtraction.bind(this));

    // Tool 4: Evidence Gathering - Gather supporting/contradicting evidence
    this.server.addTool({
      name: 'evidence_gathering',
      description: 'Gather supporting and contradicting evidence for claims from multiple sources',
      inputSchema: {
        type: 'object',
        properties: {
          claims: {
            type: 'array',
            items: { type: 'string' },
            description: 'List of claims to cross-reference'
          },
          reference_databases: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['factcheck_org', 'politifact', 'snopes', 'reuters_fact_check', 'all']
            },
            description: 'Databases to check against'
          },
          similarity_threshold: {
            type: 'number',
            minimum: 0.0,
            maximum: 1.0,
            default: 0.8,
            description: 'Similarity threshold for matching claims'
          }
        },
        required: ['claims']
      }
    }, this.evidenceGathering.bind(this));

    // Tool 5: Bias Detection - Detect potential bias in content
    this.server.addTool({
      name: 'bias_detection',
      description: 'Detect and analyze potential bias in content, sources, and claims',
      inputSchema: {
        type: 'object',
        properties: {
          content: {
            type: 'string',
            description: 'Content to analyze for bias'
          },
          content_type: {
            type: 'string', 
            enum: ['article', 'claim', 'source', 'speech', 'social_media'],
            description: 'Type of content being analyzed',
            default: 'article'
          },
          bias_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['political', 'ideological', 'confirmation', 'selection', 'framing', 'language', 'statistical']
            },
            description: 'Types of bias to analyze for',
            default: ['political', 'ideological', 'framing']
          },
          detailed_analysis: {
            type: 'boolean',
            default: false,
            description: 'Provide detailed bias analysis with examples'
          }
        },
        required: ['content']
      }
    }, this.biasDetection.bind(this));

    // Tool: Monitor Claim Propagation
    this.server.addTool({
      name: 'monitor_claim_propagation',
      description: 'Track how claims spread across different platforms and sources',
      inputSchema: {
        type: 'object',
        properties: {
          claim_text: {
            type: 'string',
            description: 'Claim to monitor'
          },
          platforms: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['social_media', 'news_sites', 'political_websites', 'fact_check_sites']
            },
            description: 'Platforms to monitor'
          },
          monitoring_duration: {
            type: 'string',
            enum: ['1_hour', '6_hours', '24_hours', '7_days'],
            description: 'How long to monitor',
            default: '24_hours'
          }
        },
        required: ['claim_text']
      }
    }, this.monitorClaimPropagation.bind(this));
  }

  setupResources() {
    // Resource: Fact Check Results
    this.server.addResource({
      uri: 'factcheck://verification_results',
      name: 'Fact Check Results',
      description: 'Comprehensive fact check verification results',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'factcheck://source_ratings',
      name: 'Source Credibility Ratings',
      description: 'Credibility ratings for information sources',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'factcheck://claim_database',
      name: 'Political Claims Database',
      description: 'Database of verified political claims',
      mimeType: 'application/json'
    });
  }

  setupHealthEndpoint() {
    // Setup express app for health checks
    this.app = express();
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(express.json());

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'fact-checking-mcp-server',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        checks: {
          database: this.pgClient ? 'connected' : 'disconnected',
          redis: this.redisClient ? 'connected' : 'disconnected',
          openai: process.env.OPENAI_API_KEY ? 'configured' : 'missing',
          perplexity: process.env.PERPLEXITY_API_KEY ? 'configured' : 'missing'
        }
      });
    });

    // Start health check server
    this.app.listen(8090, () => {
      this.logger.info('Health check endpoint running on port 8090');
    });
  }

  async factVerify(params) {
    try {
      const { 
        claim, 
        context = '', 
        political_figure = 'Unknown', 
        urgency_level = 'medium',
        check_depth = 'standard'
      } = params;
      
      const claimId = crypto.createHash('sha256').update(claim).digest('hex').substring(0, 16);
      
      // Check cache first
      const cacheKey = `fact_check:${claimId}:${check_depth}`;
      const cachedResult = await this.redisClient.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }

      this.logger.info(`Starting fact check for claim: ${claim.substring(0, 100)}...`);

      const verificationResult = await this.performFactCheck(claim, context, check_depth);
      
      // Store results in database
      await this.storeFactCheckResult(claimId, claim, political_figure, verificationResult);
      
      // Cache results
      const cacheTime = urgency_level === 'critical' ? 300 : 3600; // 5 min for critical, 1 hour otherwise
      await this.redisClient.setex(cacheKey, cacheTime, JSON.stringify(verificationResult));
      
      return {
        claim_id: claimId,
        original_claim: claim,
        political_figure,
        urgency_level,
        check_depth,
        ...verificationResult
      };

    } catch (error) {
      this.logger.error('Error verifying political claim:', error);
      throw error;
    }
  }

  async performFactCheck(claim, context, depth) {
    const result = {
      verification_status: 'pending',
      credibility_score: 0.0,
      evidence: [],
      sources_checked: 0,
      methodology: [],
      timestamp: new Date().toISOString()
    };

    try {
      // Step 1: Use AI to analyze the claim
      const aiAnalysis = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a fact-checker. Analyze this political claim for verifiable facts, identify what needs to be checked, and suggest search strategies."
          },
          {
            role: "user",
            content: `Claim: "${claim}"\nContext: "${context}"\n\nPlease analyze this claim and identify specific facts that need verification.`
          }
        ],
        max_tokens: 800
      });

      result.ai_analysis = aiAnalysis.choices[0].message.content;
      result.methodology.push('AI_analysis_completed');

      // Step 2: Search for supporting/contradicting evidence
      if (this.perplexityKey) {
        const searchResults = await this.searchWithPerplexity(claim);
        result.search_results = searchResults;
        result.methodology.push('perplexity_search_completed');
      }

      // Step 3: Check against fact-checking databases
      const factCheckResults = await this.searchFactCheckDatabases(claim);
      result.existing_fact_checks = factCheckResults;
      result.methodology.push('fact_check_database_search');

      // Step 4: Validate sources
      const sourceValidations = [];
      for (const source of result.search_results?.sources || []) {
        const validation = await this.validateSource(source);
        sourceValidations.push(validation);
      }
      result.source_validations = sourceValidations;
      result.sources_checked = sourceValidations.length;

      // Step 5: Calculate credibility score
      result.credibility_score = this.calculateCredibilityScore(result);
      
      // Step 6: Determine verification status
      result.verification_status = this.determineVerificationStatus(result.credibility_score);

      // Step 7: Generate summary
      result.summary = await this.generateVerificationSummary(claim, result);

    } catch (error) {
      this.logger.error('Error in fact check process:', error);
      result.verification_status = 'error';
      result.error_message = error.message;
    }

    return result;
  }

  async sourceCredibilityCheck(params) {
    try {
      const { source_url, source_content, check_factors = ['domain_reputation', 'bias_analysis', 'factual_accuracy'] } = params;
      
      let domain = '';
      let content = source_content || '';
      
      if (source_url) {
        try {
          const url = new URL(source_url);
          domain = url.hostname;
          
          // Fetch content if not provided
          if (!content) {
            const response = await axios.get(source_url, { timeout: 10000 });
            content = response.data;
          }
        } catch (error) {
          this.logger.warn(`Could not fetch content from ${source_url}:`, error.message);
        }
      }

      const credibilityAssessment = {
        domain,
        url: source_url,
        overall_rating: 'unknown',
        factors: {},
        assessment_date: new Date().toISOString()
      };

      // Check domain reputation
      if (check_factors.includes('domain_reputation')) {
        credibilityAssessment.factors.domain_reputation = this.assessDomainReputation(domain);
      }

      // Analyze bias
      if (check_factors.includes('bias_analysis') && content) {
        credibilityAssessment.factors.bias_analysis = await this.analyzeBias(content.substring(0, 5000));
      }

      // Check factual accuracy indicators
      if (check_factors.includes('factual_accuracy') && content) {
        credibilityAssessment.factors.factual_accuracy = await this.assessFactualAccuracy(content.substring(0, 5000));
      }

      // Calculate overall rating
      credibilityAssessment.overall_rating = this.calculateOverallCredibilityRating(credibilityAssessment.factors);

      return credibilityAssessment;

    } catch (error) {
      this.logger.error('Error checking source credibility:', error);
      throw error;
    }
  }

  async claimExtraction(params) {
    try {
      const { text_content, claim_types = ['statistical', 'policy', 'historical'], min_confidence = 0.7 } = params;
      
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `Extract verifiable claims from this text. Focus on ${claim_types.join(', ')} claims. Return as JSON array with fields: claim_text, claim_type, confidence_score, context.`
          },
          {
            role: "user",
            content: text_content.substring(0, 8000)
          }
        ],
        max_tokens: 1200
      });

      const extractedClaims = JSON.parse(response.choices[0].message.content);
      
      // Filter by confidence threshold
      const filteredClaims = extractedClaims.filter(claim => claim.confidence_score >= min_confidence);

      return {
        extracted_claims: filteredClaims,
        total_claims: filteredClaims.length,
        claim_types_found: [...new Set(filteredClaims.map(c => c.claim_type))],
        extraction_confidence: filteredClaims.reduce((sum, claim) => sum + claim.confidence_score, 0) / filteredClaims.length
      };

    } catch (error) {
      this.logger.error('Error extracting claims:', error);
      throw error;
    }
  }

  async evidenceGathering(params) {
    try {
      const { claims, reference_databases = ['all'], similarity_threshold = 0.8 } = params;
      
      const crossReferences = [];
      
      for (const claim of claims) {
        const matches = await this.findSimilarClaims(claim, reference_databases, similarity_threshold);
        crossReferences.push({
          original_claim: claim,
          matches: matches,
          match_count: matches.length,
          highest_similarity: matches.length > 0 ? Math.max(...matches.map(m => m.similarity)) : 0
        });
      }

      return {
        cross_references: crossReferences,
        total_matches: crossReferences.reduce((sum, ref) => sum + ref.match_count, 0),
        databases_checked: reference_databases
      };

    } catch (error) {
      this.logger.error('Error gathering evidence:', error);
      throw error;
    }
  }

  async biasDetection(params) {
    try {
      const { 
        content, 
        content_type = 'article', 
        bias_types = ['political', 'ideological', 'framing'],
        detailed_analysis = false 
      } = params;

      const biasAnalysis = {
        content_type,
        analysis_timestamp: new Date().toISOString(),
        bias_detected: false,
        overall_bias_score: 0.5, // 0 = strongly left, 0.5 = neutral, 1 = strongly right
        bias_indicators: [],
        confidence_score: 0.0
      };

      // Analyze for each requested bias type
      for (const biasType of bias_types) {
        const typeAnalysis = await this.analyzeBiasType(content, biasType, detailed_analysis);
        biasAnalysis.bias_indicators.push({
          type: biasType,
          ...typeAnalysis
        });
      }

      // Calculate overall metrics
      biasAnalysis.bias_detected = biasAnalysis.bias_indicators.some(indicator => indicator.detected);
      biasAnalysis.confidence_score = biasAnalysis.bias_indicators.reduce((sum, indicator) => 
        sum + indicator.confidence, 0) / biasAnalysis.bias_indicators.length;

      // Calculate overall bias score (weighted average)
      let totalWeight = 0;
      let weightedScore = 0;
      biasAnalysis.bias_indicators.forEach(indicator => {
        const weight = indicator.confidence;
        totalWeight += weight;
        weightedScore += indicator.bias_score * weight;
      });
      biasAnalysis.overall_bias_score = totalWeight > 0 ? weightedScore / totalWeight : 0.5;

      // Generate summary if detailed analysis requested
      if (detailed_analysis) {
        biasAnalysis.summary = await this.generateBiasSummary(content, biasAnalysis);
      }

      return biasAnalysis;

    } catch (error) {
      this.logger.error('Error detecting bias:', error);
      throw error;
    }
  }

  async generateFactCheckReport(params) {
    try {
      const { claim_id, report_format = 'standard', include_sources = true, include_methodology = false } = params;
      
      // Retrieve fact check data
      const result = await this.pgClient.query('SELECT * FROM fact_checks WHERE claim_id = $1', [claim_id]);
      if (result.rows.length === 0) {
        throw new Error(`Fact check not found for claim ID: ${claim_id}`);
      }

      const factCheck = result.rows[0];
      const verificationData = factCheck.verification_result;

      const report = {
        claim_id,
        report_format,
        generated_at: new Date().toISOString(),
        claim: factCheck.original_claim,
        verification_status: factCheck.verification_status,
        credibility_score: factCheck.credibility_score,
        summary: verificationData.summary
      };

      if (include_sources) {
        const sourcesResult = await this.pgClient.query('SELECT * FROM source_validations WHERE claim_id = $1', [claim_id]);
        report.sources = sourcesResult.rows;
      }

      if (include_methodology) {
        report.methodology = verificationData.methodology;
        report.ai_analysis = verificationData.ai_analysis;
      }

      // Format report based on type
      if (report_format === 'detailed') {
        report.evidence = verificationData.evidence;
        report.existing_fact_checks = verificationData.existing_fact_checks;
      }

      return report;

    } catch (error) {
      this.logger.error('Error generating fact check report:', error);
      throw error;
    }
  }

  async monitorClaimPropagation(params) {
    try {
      const { claim_text, platforms = ['news_sites'], monitoring_duration = '24_hours' } = params;
      
      // This would implement real-time monitoring
      // For now, return a placeholder structure
      return {
        claim_text,
        monitoring_started: new Date().toISOString(),
        monitoring_duration,
        platforms,
        propagation_data: {
          initial_sources: 0,
          spread_rate: 0,
          platform_breakdown: {},
          sentiment_analysis: 'neutral'
        },
        alerts: [],
        status: 'monitoring_active'
      };

    } catch (error) {
      this.logger.error('Error monitoring claim propagation:', error);
      throw error;
    }
  }

  // Helper methods
  async analyzeBiasType(content, biasType, detailed) {
    try {
      let prompt = '';
      switch (biasType) {
        case 'political':
          prompt = `Analyze this content for political bias. Rate from 0 (strongly left-leaning) to 1 (strongly right-leaning), 0.5 is neutral. Look for partisan language, selective facts, and ideological framing.`;
          break;
        case 'ideological':
          prompt = `Analyze this content for ideological bias. Look for confirmation bias, cherry-picking of data, and presentation that favors particular worldviews.`;
          break;
        case 'framing':
          prompt = `Analyze how this content frames issues. Look for loaded language, selective context, and presentation that influences perception.`;
          break;
        case 'selection':
          prompt = `Analyze for selection bias. Look for cherry-picked data, omitted context, and selective presentation of facts.`;
          break;
        case 'confirmation':
          prompt = `Analyze for confirmation bias. Look for evidence that supports predetermined conclusions while ignoring contradictory evidence.`;
          break;
        case 'language':
          prompt = `Analyze the language for bias. Look for emotionally charged words, loaded terms, and inflammatory language.`;
          break;
        case 'statistical':
          prompt = `Analyze for statistical bias. Look for misleading statistics, improper correlations, and misrepresentation of data.`;
          break;
      }

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `${prompt} Return JSON with: detected (boolean), bias_score (0-1), confidence (0-1), explanation (string), examples (array of strings if detailed analysis requested).`
          },
          {
            role: "user",
            content: content.substring(0, 8000)
          }
        ],
        max_tokens: detailed ? 600 : 300
      });

      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      this.logger.error(`Error analyzing ${biasType} bias:`, error);
      return {
        detected: false,
        bias_score: 0.5,
        confidence: 0.0,
        explanation: `Unable to analyze ${biasType} bias`,
        examples: []
      };
    }
  }

  async generateBiasSummary(content, biasAnalysis) {
    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Generate a concise summary of the bias analysis results, highlighting key findings and recommendations."
          },
          {
            role: "user",
            content: `Content analyzed: "${content.substring(0, 500)}..."\n\nBias Analysis Results: ${JSON.stringify(biasAnalysis.bias_indicators)}`
          }
        ],
        max_tokens: 300
      });

      return response.choices[0].message.content;
    } catch (error) {
      return "Unable to generate bias summary";
    }
  }

  async searchWithPerplexity(query) {
    if (!this.perplexityKey) {
      return { sources: [], summary: 'Perplexity API not configured' };
    }

    try {
      const response = await axios.post('https://api.perplexity.ai/chat/completions', {
        model: "sonar-small-online",
        messages: [
          {
            role: "user",
            content: `Search for factual information about: ${query}`
          }
        ]
      }, {
        headers: {
          'Authorization': `Bearer ${this.perplexityKey}`,
          'Content-Type': 'application/json'
        }
      });

      return {
        summary: response.data.choices[0].message.content,
        sources: response.data.citations || []
      };
    } catch (error) {
      this.logger.error('Perplexity search error:', error);
      return { sources: [], summary: 'Search failed' };
    }
  }

  async searchFactCheckDatabases(claim) {
    // Placeholder for fact-check database integration
    return [];
  }

  async validateSource(source) {
    const domain = new URL(source).hostname;
    return {
      url: source,
      domain,
      credibility_rating: this.assessDomainReputation(domain),
      validation_method: 'domain_check'
    };
  }

  assessDomainReputation(domain) {
    if (this.trustedSources.includes(domain)) {
      return 'high';
    }
    if (domain.includes('edu') || domain.includes('gov')) {
      return 'high';
    }
    if (domain.includes('blog') || domain.includes('wordpress')) {
      return 'low';
    }
    return 'medium';
  }

  async analyzeBias(content) {
    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Analyze this text for political bias. Rate from -5 (extremely liberal) to +5 (extremely conservative) and explain."
          },
          {
            role: "user",
            content: content
          }
        ],
        max_tokens: 300
      });

      return {
        bias_analysis: response.choices[0].message.content,
        bias_score: 0 // Would extract from AI response
      };
    } catch (error) {
      return { bias_analysis: 'Unable to analyze bias', bias_score: 0 };
    }
  }

  async assessFactualAccuracy(content) {
    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Assess the factual accuracy of this content. Look for verifiable claims and assess reliability."
          },
          {
            role: "user",
            content: content
          }
        ],
        max_tokens: 300
      });

      return {
        accuracy_assessment: response.choices[0].message.content,
        accuracy_score: 0.75 // Would calculate based on AI response
      };
    } catch (error) {
      return { accuracy_assessment: 'Unable to assess accuracy', accuracy_score: 0.5 };
    }
  }

  calculateCredibilityScore(result) {
    let score = 0.5; // Start neutral
    
    // Factor in source credibility
    if (result.source_validations) {
      const avgSourceRating = result.source_validations.reduce((sum, source) => {
        const rating = source.credibility_rating === 'high' ? 1 : source.credibility_rating === 'medium' ? 0.5 : 0;
        return sum + rating;
      }, 0) / result.source_validations.length;
      score = (score + avgSourceRating) / 2;
    }

    // Factor in existing fact checks
    if (result.existing_fact_checks && result.existing_fact_checks.length > 0) {
      // Would analyze existing fact check results
    }

    return Math.max(0, Math.min(1, score));
  }

  determineVerificationStatus(credibilityScore) {
    if (credibilityScore >= 0.8) return 'verified_true';
    if (credibilityScore >= 0.6) return 'likely_true';
    if (credibilityScore >= 0.4) return 'mixed_evidence';
    if (credibilityScore >= 0.2) return 'likely_false';
    return 'verified_false';
  }

  async generateVerificationSummary(claim, result) {
    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Generate a concise fact-check summary based on the verification results."
          },
          {
            role: "user",
            content: `Claim: "${claim}"\nVerification Status: ${result.verification_status}\nCredibility Score: ${result.credibility_score}\nEvidence: ${JSON.stringify(result.evidence)}`
          }
        ],
        max_tokens: 200
      });

      return response.choices[0].message.content;
    } catch (error) {
      return `Claim verification completed with status: ${result.verification_status}`;
    }
  }

  calculateOverallCredibilityRating(factors) {
    const ratings = Object.values(factors).filter(f => f && f.rating);
    if (ratings.length === 0) return 'unknown';
    
    const highCount = ratings.filter(r => r === 'high').length;
    const mediumCount = ratings.filter(r => r === 'medium').length;
    
    if (highCount > ratings.length / 2) return 'high';
    if (mediumCount + highCount > ratings.length / 2) return 'medium';
    return 'low';
  }

  async findSimilarClaims(claim, databases, threshold) {
    // Placeholder - would implement semantic similarity search
    return [];
  }

  async storeFactCheckResult(claimId, claim, politicalFigure, result) {
    try {
      await this.pgClient.query(`
        INSERT INTO fact_checks 
        (claim_id, original_claim, verification_result, credibility_score, sources_checked, verification_status)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (claim_id) 
        DO UPDATE SET verification_result = $3, credibility_score = $4, updated_at = CURRENT_TIMESTAMP
      `, [
        claimId,
        claim,
        JSON.stringify(result),
        result.credibility_score,
        result.sources_checked,
        result.verification_status
      ]);

      // Store political claim info
      await this.pgClient.query(`
        INSERT INTO political_claims (claim_text, political_figure, fact_check_id)
        VALUES ($1, $2, $3)
      `, [claim, politicalFigure, claimId]);

    } catch (error) {
      this.logger.error('Error storing fact check result:', error);
    }
  }

  async start() {
    await this.initialize();
    await this.server.start();
    this.logger.info('Fact Checking MCP Server started');
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    await this.server.stop();
  }
}

// Start the server
const server = new FactCheckingServer();

process.on('SIGINT', async () => {
  console.log('Shutting down...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);