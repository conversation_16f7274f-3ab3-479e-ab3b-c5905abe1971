# Economic Analysis MCP Server

A comprehensive Model Context Protocol (MCP) server for economic analysis, policy impact assessment, budget analysis, market research, and cost-benefit analysis. This server provides essential tools for political and economic policy analysis.

## Features

### Core Tools

1. **Economic Indicator Analysis** (`economic_indicator_analysis`)
   - Analyze economic indicators and trends with comprehensive statistical analysis
   - Support for multiple data sources (FRED, World Bank, BEA, Census)
   - Statistical tests, correlations, and forecasting
   - Data transformations (growth rates, percent change, log)

2. **Policy Impact Assessment** (`policy_impact_assessment`)
   - Assess economic impact of policy proposals using economic modeling
   - Support for fiscal, monetary, regulatory, trade, and social policies
   - Macroeconomic and sectoral impact analysis
   - Confidence scoring and risk assessment

3. **Budget Analysis** (`budget_analysis`)
   - Analyze government budgets and spending patterns
   - Support for federal, state, local, department, and program budgets
   - Trend, composition, efficiency, and comparison analysis
   - Multi-year budget tracking and forecasting

4. **Market Research** (`market_research`)
   - Research market conditions and economic data across various sectors
   - Labor, housing, financial, commodity, and sector-specific analysis
   - Geographic scope from local to national
   - Trend identification and competitive analysis

5. **Cost-Benefit Analysis** (`cost_benefit_analysis`)
   - Comprehensive economic cost-benefit analysis for projects and policies
   - NPV, BCR, IRR, and payback period calculations
   - Sensitivity analysis and risk assessment
   - Support for infrastructure, social programs, regulations, and technology projects

### Key Capabilities

- **Real-time Data Integration**: Connects to major economic data sources
- **Advanced Analytics**: Statistical analysis, trend identification, and forecasting
- **Policy Modeling**: Economic impact simulation and scenario analysis
- **Comprehensive Reporting**: Detailed analysis reports with insights and recommendations
- **Secure Architecture**: Enterprise-grade security with rate limiting and audit logging
- **Scalable Design**: Redis caching and PostgreSQL storage for performance

## Installation

### Prerequisites

- Node.js 18+
- PostgreSQL 12+
- Redis 6+
- Docker (optional)

### Environment Variables

Create a `.env` file with the following variables:

```env
# Server Configuration
MCP_SERVER_PORT=8091
LOG_LEVEL=info
NODE_ENV=production

# Database Configuration
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432
POSTGRES_DB=political_conversations
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=your_postgres_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Data Source API Keys
FRED_API_KEY=your_fred_api_key
BEA_API_KEY=your_bea_api_key
CENSUS_API_KEY=your_census_api_key

# Security Configuration
JWT_SECRET=your_jwt_secret
SESSION_SECRET=your_session_secret

# Cache Configuration
CACHE_TTL=3600
MAX_DATA_POINTS=1000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3001,http://localhost:5678
```

### Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Security audit
npm run security-audit
```

### Docker Deployment

```bash
# Build image
docker build -t economic-analysis-mcp .

# Run container
docker run -d \
  --name economic-analysis-mcp \
  -p 8091:8091 \
  --env-file .env \
  economic-analysis-mcp
```

## API Documentation

### Economic Indicator Analysis

Analyze economic indicators with comprehensive statistical analysis.

```json
{
  "name": "economic_indicator_analysis",
  "arguments": {
    "indicators": ["GDP", "UNRATE", "CPIAUCSL"],
    "start_date": "2020-01-01",
    "end_date": "2024-01-01",
    "frequency": "monthly",
    "transformation": "percent_change",
    "include_forecast": true,
    "statistical_tests": true
  }
}
```

**Parameters:**
- `indicators`: Array of economic indicator codes
- `start_date`: Start date for analysis (YYYY-MM-DD)
- `end_date`: End date for analysis (YYYY-MM-DD)
- `frequency`: Data frequency (daily, weekly, monthly, quarterly, annual)
- `transformation`: Data transformation (level, growth, percent_change, log)
- `include_forecast`: Include economic forecasts (boolean)
- `statistical_tests`: Include statistical tests and correlations (boolean)

### Policy Impact Assessment

Assess economic impact of policy proposals using economic modeling.

```json
{
  "name": "policy_impact_assessment",
  "arguments": {
    "policy_description": "Universal Basic Income program providing $1000/month to all adults",
    "policy_type": "fiscal",
    "time_horizon": "long_term",
    "affected_sectors": ["labor", "consumer_spending", "taxation"],
    "budget_impact": 3000000000000,
    "implementation_timeline": "3-year phased rollout"
  }
}
```

**Parameters:**
- `policy_description`: Detailed description of the policy proposal
- `policy_type`: Type of policy (fiscal, monetary, regulatory, trade, healthcare, education, infrastructure)
- `time_horizon`: Time horizon for impact assessment (short_term, medium_term, long_term)
- `affected_sectors`: Economic sectors affected by the policy
- `budget_impact`: Estimated budget impact in dollars
- `implementation_timeline`: Timeline for policy implementation

### Budget Analysis

Analyze government budgets and spending patterns with detailed breakdown.

```json
{
  "name": "budget_analysis",
  "arguments": {
    "budget_type": "federal",
    "jurisdiction": "United States",
    "fiscal_year": 2024,
    "categories": ["defense", "healthcare", "education", "infrastructure"],
    "analysis_type": "trend",
    "comparison_years": [2022, 2023]
  }
}
```

**Parameters:**
- `budget_type`: Type of budget (federal, state, local, department, program)
- `jurisdiction`: Government jurisdiction
- `fiscal_year`: Fiscal year for analysis
- `categories`: Specific budget categories to analyze
- `analysis_type`: Type of analysis (trend, composition, efficiency, comparison)
- `comparison_years`: Additional years for comparison analysis

### Market Research

Research market conditions and economic data across various sectors.

```json
{
  "name": "market_research",
  "arguments": {
    "market_type": "housing",
    "geographic_scope": "national",
    "time_period": "current",
    "sector": "residential",
    "metrics": ["prices", "inventory", "sales_volume"],
    "include_trends": true,
    "competitive_analysis": false
  }
}
```

**Parameters:**
- `market_type`: Type of market (labor, housing, financial, commodity, sector)
- `geographic_scope`: Geographic scope (national, regional, state, metro, local)
- `time_period`: Time period for research (current, historical, forecast)
- `sector`: Specific sector or industry to focus on
- `metrics`: Specific metrics to analyze
- `include_trends`: Include trend analysis (boolean)
- `competitive_analysis`: Include competitive market analysis (boolean)

### Cost-Benefit Analysis

Perform comprehensive economic cost-benefit analysis for projects and policies.

```json
{
  "name": "cost_benefit_analysis",
  "arguments": {
    "project_description": "High-speed rail infrastructure project connecting major cities",
    "project_type": "infrastructure",
    "costs": {
      "initial": *********00,
      "annual_operating": 2000000000,
      "maintenance": *********
    },
    "benefits": {
      "quantifiable": [
        {
          "type": "time_savings",
          "annual_value": 3000000000,
          "description": "Reduced travel time for passengers"
        },
        {
          "type": "environmental",
          "annual_value": 1000000000,
          "description": "Reduced carbon emissions"
        }
      ],
      "qualitative": [
        "Economic development in connected regions",
        "Improved quality of life",
        "Reduced traffic congestion"
      ]
    },
    "time_horizon": 30,
    "discount_rate": 0.03,
    "sensitivity_analysis": true
  }
}
```

**Parameters:**
- `project_description`: Detailed description of the project or policy
- `project_type`: Type of project (infrastructure, social_program, regulation, technology, education, healthcare)
- `costs`: Cost structure with initial, annual operating, and maintenance costs
- `benefits`: Benefit structure with quantifiable and qualitative benefits
- `time_horizon`: Analysis time horizon in years
- `discount_rate`: Discount rate for NPV calculation
- `sensitivity_analysis`: Include sensitivity analysis (boolean)

## Data Sources

The server integrates with multiple authoritative economic data sources:

### Federal Reserve Economic Data (FRED)
- **URL**: https://fred.stlouisfed.org/
- **Coverage**: US economic indicators, monetary policy, financial markets
- **API Key Required**: Yes
- **Key Indicators**: GDP, unemployment rate, inflation, interest rates

### Bureau of Economic Analysis (BEA)
- **URL**: https://www.bea.gov/
- **Coverage**: National accounts, GDP components, regional data
- **API Key Required**: Yes
- **Key Indicators**: GDP by industry, personal income, trade balance

### US Census Bureau
- **URL**: https://www.census.gov/
- **Coverage**: Population, housing, business, trade statistics
- **API Key Required**: Yes
- **Key Indicators**: Population data, housing statistics, business indicators

### World Bank Open Data
- **URL**: https://data.worldbank.org/
- **Coverage**: International economic indicators
- **API Key Required**: No
- **Key Indicators**: Cross-country economic comparisons

## Database Schema

The server uses PostgreSQL with the following main tables:

- **economic_indicators**: Metadata for economic indicators
- **economic_data_points**: Time series data points
- **policy_impact_assessments**: Policy analysis results
- **budget_analyses**: Budget analysis results
- **market_research**: Market research findings
- **cost_benefit_analyses**: Cost-benefit analysis results
- **economic_forecasts**: Forecast data and models

## Security Features

- **Rate Limiting**: API endpoint rate limiting with configurable limits
- **Authentication**: JWT-based authentication with session management
- **Input Validation**: Comprehensive input validation using Joi schemas
- **SQL Injection Protection**: Parameterized queries for all database operations
- **CORS Protection**: Configurable CORS policies
- **Security Headers**: Helmet.js for security headers
- **Audit Logging**: Comprehensive audit logging for all operations

## Performance Optimization

- **Redis Caching**: Intelligent caching of economic data with configurable TTL
- **Database Indexing**: Optimized indexes for query performance
- **Connection Pooling**: PostgreSQL connection pooling for scalability
- **Data Compression**: Gzip compression for API responses
- **Lazy Loading**: On-demand data fetching and processing

## Monitoring and Logging

- **Health Checks**: Built-in health check endpoints
- **Structured Logging**: Winston-based structured logging with daily rotation
- **Performance Metrics**: Response time and throughput monitoring
- **Error Tracking**: Comprehensive error logging and tracking
- **Data Source Status**: Monitoring of external data source availability

## Development

### Project Structure

```
economic-analysis/
├── server.js              # Main server file
├── package.json           # Node.js dependencies
├── Dockerfile            # Docker configuration
├── README.md            # This file
├── scripts/             # Utility scripts
├── logs/               # Log files (created at runtime)
└── .env                # Environment variables (not in repo)
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration

# Run linting
npm run lint
```

## Deployment

### Production Deployment

1. Set up PostgreSQL and Redis infrastructure
2. Configure environment variables
3. Build and deploy Docker container
4. Set up load balancing and monitoring
5. Configure data source API keys
6. Set up scheduled data refresh jobs

### Scaling Considerations

- **Horizontal Scaling**: Multiple server instances behind load balancer
- **Database Scaling**: Read replicas for analytical queries
- **Cache Optimization**: Redis cluster for high availability
- **Rate Limiting**: Distributed rate limiting across instances
- **Data Partitioning**: Time-based partitioning for historical data

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- GitHub Issues: https://github.com/beau-lewis/political-document-system/issues
- Documentation: See inline code documentation
- API Reference: Available at `/api/docs` when server is running

## Changelog

### v1.0.0
- Initial release
- Core economic analysis tools
- Integration with major data sources
- Comprehensive security and performance features
- Docker support and deployment guides