{"name": "mcp-autonomous-fact-checking", "version": "1.0.0", "description": "Autonomous Fact-Checking MCP Server with real-time verification and cross-source validation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint ."}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "express": "^4.18.3", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "morgan": "^1.10.0", "openai": "^4.28.0", "anthropic": "^0.20.0", "google-generative-ai": "^0.2.1", "axios": "^1.6.5", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.7.0", "natural": "^6.12.0", "compromise": "^14.11.0", "sentiment": "^5.0.2", "uuid": "^9.0.1", "redis": "^4.6.12", "pg": "^8.11.3", "sqlite3": "^5.1.6", "prom-client": "^15.1.0", "express-prometheus-middleware": "^1.2.0", "node-cache": "^5.1.2", "socket.io": "^4.7.4", "rss-parser": "^3.13.0", "date-fns": "^3.1.0", "lodash": "^4.17.21", "validator": "^13.11.0", "url-parse": "^1.5.10", "fact-check-api": "^1.0.0", "news-api": "^2.4.1", "twitter-api-v2": "^1.15.2", "google-fact-check": "^1.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "fact-checking", "verification", "autonomous", "real-time", "political-analysis", "source-validation", "truth-assessment"], "author": "Political Document Processing System", "license": "MIT"}