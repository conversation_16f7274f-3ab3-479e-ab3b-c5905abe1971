# n8n AI Document Processing Workflow - Implementation Guide

## MVP Workflow Structure

Let's start with a simplified version that covers the core functionality:

```
Google Drive Trigger (Start Node) → Read Document → Load Document Content → AI Analysis → Route Decision → Document Processing → CloudConvert → Upload Result → Email Notification
```

## Node-by-Node Implementation

### 1. Google Drive Trigger (Start Node) (Start Node)
**Node Type:** Google Drive Trigger (Start Node)
**Configuration:**
- **Authentication:** Google Drive OAuth2 credential
- **Watch:** Folder
- **Folder ID:** `YOUR_ACTUAL_POLITICAL_IN_FOLDER_ID`
- **Trigger On:** New File
- **Include:** Binary Data (checked)
- **File Type Filter:** `application/octet-stream` or `text/markdown`

**Output:** Binary file data + metadata

### 2. Read Document Content (Code Node)
**Node Type:** Code
**Purpose:** Convert binary markdown to text

```javascript
// Extract markdown content from binary data
const items = [];

for (const item of $input.all()) {
  if (item.binary && item.binary.data) {
    const markdownContent = Buffer.from(item.binary.data.data, 'base64').toString('utf8');
    
    items.push({
      json: {
        markdownContent: markdownContent,
        originalFilename: item.json.name,
        originalFileId: item.json.id,
        originalMimeType: item.json.mimeType,
        createdTime: item.json.createdTime
      }
    });
  }
}

return items;
```

### 3. Load Document Content (Google Drive Download)
**Node Type:** Google Drive
**Operation:** File → Download
**Configuration:**
- **Authentication:** Google Drive OAuth2 credential
- **File ID:** `YOUR_MANIFESTO_FILE_ID`
- **Include:** Binary Data (checked)

### 4. Extract Manifesto Content (Code Node)
**Node Type:** Code

```javascript
const items = [];

for (const item of $input.all()) {
  if (item.binary && item.binary.data) {
    const manifestoContent = Buffer.from(item.binary.data.data, 'base64').toString('utf8');
    
    items.push({
      json: {
        manifestoContent: manifestoContent
      }
    });
  }
}

return items;
```

### 5. Merge Document and Manifesto (Merge Node)
**Node Type:** Merge
**Mode:** Merge By Position
**Input 1:** Read Document Content output
**Input 2:** Extract Manifesto Content output

### 6. AI Document Analysis (HTTP Request Node)
**Node Type:** HTTP Request
**Purpose:** Call Gemini/Claude API for document analysis

**For Gemini API:**
```json
{
  "method": "POST",
  "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=YOUR_API_KEY",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "contents": [{
      "parts": [{
        "text": "You are an expert policy analyst focused on systemic improvements for society. Your goal is to analyze the provided document against the manifesto and determine the most strategic action.\n\nANALYSIS FRAMEWORK:\n1. Read the document thoroughly\n2. Identify key themes, arguments, and policy positions\n3. Compare against the manifesto for alignment\n4. Determine strategic value and potential improvements\n5. Recommend the most valuable action\n\nDECISION CRITERIA:\n- edit: Document has good content but needs improvement/alignment\n- combine: Document would benefit from synthesis with related content\n- generate_whitepaper: Document suggests need for comprehensive policy paper\n- create_new: Document identifies gap requiring new content\n- store_original: Document is already optimal or needs human review\n\nRespond with JSON in this EXACT format:\n{\n  \"suggested_action\": \"edit|combine|generate_whitepaper|create_new|store_original\",\n  \"action_details\": {\n    \"edit_instructions\": \"string (if edit)\",\n    \"combination_purpose\": \"string (if combine)\",\n    \"whitepaper_topic\": \"string (if generate_whitepaper)\",\n    \"new_document_topic\": \"string (if create_new)\",\n    \"reason\": \"string (if store_original)\"\n  },\n  \"analysis_summary\": \"Brief summary of document content and alignment\",\n  \"confidence_score\": 0.85\n}\n\nMANIFESTO:\n{{ $json.manifestoContent }}\n\nDOCUMENT TITLE: {{ $json.originalFilename }}\n\nDOCUMENT CONTENT:\n{{ $json.markdownContent }}"
      }]
    }],
    "generationConfig": {
      "temperature": 0.3,
      "maxOutputTokens": 2048
    }
  }
}
```

### 7. Parse AI Response (Code Node)
**Node Type:** Code
**Purpose:** Extract and validate AI response

```javascript
const items = [];

for (const item of $input.all()) {
  try {
    // Extract AI response from Gemini API format
    const aiResponse = item.json.candidates[0].content.parts[0].text;
    
    // Parse JSON response
    const parsedResponse = JSON.parse(aiResponse);
    
    // Validate required fields
    if (!parsedResponse.suggested_action || !parsedResponse.confidence_score) {
      throw new Error('Invalid AI response format');
    }
    
    items.push({
      json: {
        // Preserve original data
        markdownContent: item.json.markdownContent,
        originalFilename: item.json.originalFilename,
        originalFileId: item.json.originalFileId,
        manifestoContent: item.json.manifestoContent,
        
        // Add AI analysis
        suggested_action: parsedResponse.suggested_action,
        action_details: parsedResponse.action_details,
        analysis_summary: parsedResponse.analysis_summary,
        confidence_score: parsedResponse.confidence_score,
        
        // Add timestamp
        processed_at: new Date().toISOString()
      }
    });
  } catch (error) {
    // Handle parsing errors - route to human review
    items.push({
      json: {
        markdownContent: item.json.markdownContent,
        originalFilename: item.json.originalFilename,
        originalFileId: item.json.originalFileId,
        manifestoContent: item.json.manifestoContent,
        
        suggested_action: 'store_original',
        action_details: { reason: `AI parsing error: ${error.message}` },
        analysis_summary: 'Error processing document',
        confidence_score: 0.0,
        error: error.message
      }
    });
  }
}

return items;
```

### 8. Decision Router (Switch Node)
**Node Type:** Switch
**Mode:** Rules
**Rules:**
1. **Edit Branch:** `{{ $json.suggested_action === 'edit' && $json.confidence_score > 0.7 }}`
2. **Combine Branch:** `{{ $json.suggested_action === 'combine' && $json.confidence_score > 0.7 }}`
3. **Whitepaper Branch:** `{{ $json.suggested_action === 'generate_whitepaper' && $json.confidence_score > 0.7 }}`
4. **Create New Branch:** `{{ $json.suggested_action === 'create_new' && $json.confidence_score > 0.7 }}`
5. **Human Review Branch:** `{{ $json.confidence_score <= 0.7 || $json.suggested_action === 'store_original' }}`

## Document Processing Branches

### Branch 1: Edit Document (HTTP Request Node)
**Node Type:** HTTP Request
**Purpose:** Call AI to edit the document

```json
{
  "method": "POST",
  "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=YOUR_API_KEY",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "contents": [{
      "parts": [{
        "text": "You are a skilled policy document editor. Your goal is to improve the document according to the provided instructions while maintaining alignment with the manifesto.\n\nEDITING GUIDELINES:\n- Improve clarity and readability\n- Strengthen arguments and evidence\n- Ensure alignment with manifesto principles\n- Add actionable recommendations\n- Maintain the original structure and intent\n- Use professional, persuasive language\n\nMANIFESTO:\n{{ $json.manifestoContent }}\n\nEDIT INSTRUCTIONS:\n{{ $json.action_details.edit_instructions }}\n\nORIGINAL DOCUMENT:\n{{ $json.markdownContent }}\n\nProvide the edited document in markdown format. Return only the improved document content, no additional commentary."
      }]
    }],
    "generationConfig": {
      "temperature": 0.4,
      "maxOutputTokens": 4096
    }
  }
}
```

### Branch 2: Store Original / Human Review (Code Node)
**Node Type:** Code
**Purpose:** Prepare original document for output

```javascript
const items = [];

for (const item of $input.all()) {
  items.push({
    json: {
      finalMarkdownContent: item.json.markdownContent,
      originalFilename: item.json.originalFilename,
      originalFileId: item.json.originalFileId,
      suggested_action: item.json.suggested_action,
      analysis_summary: item.json.analysis_summary,
      confidence_score: item.json.confidence_score,
      action_details: item.json.action_details,
      processed_at: item.json.processed_at,
      needs_review: true
    }
  });
}

return items;
```

## Output Processing

### 9. Process AI Response (Code Node)
**Node Type:** Code
**Purpose:** Extract final content from AI response

```javascript
const items = [];

for (const item of $input.all()) {
  let finalContent = item.json.markdownContent; // Default to original
  
  // Extract AI-generated content if available
  if (item.json.candidates && item.json.candidates[0]) {
    finalContent = item.json.candidates[0].content.parts[0].text;
  }
  
  // Generate output filename
  const baseFilename = item.json.originalFilename.replace('.md', '');
  let outputFilename = `${baseFilename}_processed.docx`;
  
  switch (item.json.suggested_action) {
    case 'edit':
      outputFilename = `${baseFilename}_edited.docx`;
      break;
    case 'combine':
      outputFilename = `${baseFilename}_combined.docx`;
      break;
    case 'generate_whitepaper':
      outputFilename = `whitepaper_${baseFilename}.docx`;
      break;
    case 'create_new':
      outputFilename = `new_${baseFilename}.docx`;
      break;
    default:
      outputFilename = `${baseFilename}_original.docx`;
  }
  
  items.push({
    json: {
      finalMarkdownContent: finalContent,
      outputFilename: outputFilename,
      originalFilename: item.json.originalFilename,
      suggested_action: item.json.suggested_action,
      analysis_summary: item.json.analysis_summary,
      confidence_score: item.json.confidence_score,
      processed_at: item.json.processed_at
    }
  });
}

return items;
```

### 10. Convert to DOCX (HTTP Request Node)
**Node Type:** HTTP Request
**Purpose:** CloudConvert API call

```json
{
  "method": "POST",
  "url": "https://api.cloudconvert.com/v2/jobs",
  "headers": {
    "Authorization": "Bearer YOUR_CLOUDCONVERT_API_KEY",
    "Content-Type": "application/json"
  },
  "body": {
    "tasks": {
      "import": {
        "operation": "import/raw",
        "file": "{{ $json.finalMarkdownContent }}",
        "filename": "{{ $json.originalFilename }}"
      },
      "convert": {
        "operation": "convert",
        "input": "import",
        "output_format": "docx",
        "some_other_option": "value"
      },
      "export": {
        "operation": "export/url",
        "input": "convert"
      }
    }
  }
}
```

### 11. Upload to Google Drive (Google Drive Node)
**Node Type:** Google Drive
**Operation:** File → Upload
**Configuration:**
- **Authentication:** Google Drive OAuth2 credential
- **File Name:** `{{ $json.outputFilename }}`
- **Parent Folder ID:** `YOUR_POLITICAL_OUT_FOLDER_ID`
- **Upload From:** URL (from CloudConvert response)

### 12. Send Notification (Email Node)
**Node Type:** Email Send
**Configuration:**
- **Authentication:** Your email credential
- **To:** <EMAIL>
- **Subject:** `n8n AI Document Processing Complete: {{ $json.outputFilename }}`
- **Body:**
```html
<h2>Document Processing Complete</h2>
<p><strong>Original Document:</strong> {{ $json.originalFilename }}</p>
<p><strong>AI Action:</strong> {{ $json.suggested_action }}</p>
<p><strong>Output File:</strong> {{ $json.outputFilename }}</p>
<p><strong>Confidence Score:</strong> {{ $json.confidence_score }}</p>

<h3>Analysis Summary:</h3>
<p>{{ $json.analysis_summary }}</p>

<h3>Document Link:</h3>
<p><a href="{{ $json.webViewLink }}">View Document in Google Drive</a></p>

<p><em>Processed at: {{ $json.processed_at }}</em></p>
```

## Prerequisites Setup

### Required Credentials:
1. **Google Drive OAuth2:** For file access
2. **Gemini API Key:** For AI analysis
3. **CloudConvert API Key:** For document conversion
4. **Email Credential:** For notifications

### Required IDs:
- `YOUR_ACTUAL_POLITICAL_IN_FOLDER_ID`: Input folder ID
- `YOUR_POLITICAL_OUT_FOLDER_ID`: Output folder ID  
- `YOUR_MANIFESTO_FILE_ID`: Manifesto file ID

## Error Handling

Add these configurations to key nodes:

### HTTP Request Nodes:
- **Retry on Fail:** 3 attempts
- **Wait Time:** 5 seconds
- **Continue on Fail:** True (for graceful degradation)

### Global Error Handling:
Create a separate workflow for error notifications with an Error Trigger node.

## Testing Strategy

1. **Test with single document:** Start with a simple markdown file
2. **Verify AI responses:** Check JSON parsing and decision logic
3. **Test each branch:** Ensure all processing paths work
4. **End-to-end test:** Full workflow from trigger to notification

## Next Steps

1. Build this MVP workflow first
2. Test thoroughly with sample documents
3. Add RAG functionality (ChromaDB integration)
4. Expand to more complex document processing
5. Add batch processing capabilities

Would you like me to create any specific configurations or expand on any particular section? 