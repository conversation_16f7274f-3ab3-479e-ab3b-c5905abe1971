{"name": "mcp-autonomous-ensemble", "version": "1.0.0", "description": "Autonomous Model Ensemble MCP Server with intelligent AI coordination and multi-agent orchestration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "setup": "python3 setup.py"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "express": "^4.18.3", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "morgan": "^1.10.0", "child_process": "^1.0.2", "python-shell": "^5.0.0", "ws": "^8.16.0", "uuid": "^9.0.1", "redis": "^4.6.12", "sqlite3": "^5.1.6", "prom-client": "^15.1.0", "express-prometheus-middleware": "^1.2.0", "openai": "^4.28.0", "anthropic": "^0.20.0", "google-generative-ai": "^0.2.1", "lodash": "^4.17.21", "moment": "^2.30.1", "cron": "^3.1.6", "node-cache": "^5.1.2", "circuit-breaker-js": "^0.8.0", "async": "^3.2.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0", "python": ">=3.8.0"}, "keywords": ["mcp", "autonomous", "ensemble", "ai-coordination", "multi-agent", "orchestration", "political-analysis", "intelligent-routing"], "author": "Political Document Processing System", "license": "MIT"}