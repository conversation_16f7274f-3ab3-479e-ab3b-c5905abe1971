#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import { OpenAI } from 'openai';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
import winston from 'winston';
import natural from 'natural';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';

/**
 * Legal Analysis MCP Server
 * Advanced legal document analysis, constitutional compliance checking, and legal research
 */

class LegalAnalysisServer {
  constructor() {
    this.server = new MCPServer({
      name: 'legal-analysis',
      version: '1.0.0'
    });
    
    // Initialize AI clients
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.tokenizer = encoding_for_model('gpt-4');
    this.legalCache = new Map();
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    // NLP tools
    this.stemmer = natural.PorterStemmer;
    this.tokenizer_nlp = new natural.WordTokenizer();
    
    // Legal databases and references
    this.constitutionalAmendments = this.loadConstitutionalReferences();
    this.legalCitationRegex = /\b\d+\s+[A-Z][a-z]+\.?\s+\d+(?:\s*\(\d{4}\))?\b/g;
    
    // Setup logging
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: '/app/logs/legal-analysis.log' })
      ]
    });

    this.setupTools();
    this.setupResources();
    this.setupHealthEndpoint();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'legal_analysis',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      await this.pgClient.connect();
      await this.redisClient.connect();
      this.logger.info('Legal Analysis MCP Server database connections established');
      
      await this.initializeTables();
    } catch (error) {
      this.logger.error('Failed to initialize:', error);
      throw error;
    }
  }

  async initializeTables() {
    try {
      // Legal document analysis table
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS legal_analysis (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255) UNIQUE,
          filename VARCHAR(255),
          document_type VARCHAR(100),
          content_hash VARCHAR(64),
          analysis_result JSONB,
          constitutional_issues JSONB,
          legal_risks JSONB,
          compliance_score DECIMAL(3,2),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      // Legal precedents and citations
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS legal_precedents (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255),
          citation VARCHAR(500),
          case_name VARCHAR(500),
          court VARCHAR(200),
          year INTEGER,
          relevance_score DECIMAL(3,2),
          legal_principle TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Constitutional compliance tracking
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS constitutional_compliance (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255),
          amendment_number INTEGER,
          provision_text TEXT,
          compliance_status VARCHAR(50),
          issues_identified TEXT[],
          recommendations TEXT[],
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Regulatory impact assessments
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS regulatory_impact (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255),
          regulation_type VARCHAR(100),
          impact_category VARCHAR(100),
          impact_level VARCHAR(50),
          affected_parties TEXT[],
          economic_impact JSONB,
          implementation_timeline TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Legal risk assessments
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS legal_risk_assessment (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255),
          risk_category VARCHAR(100),
          risk_level VARCHAR(50),
          risk_description TEXT,
          mitigation_strategies TEXT[],
          probability DECIMAL(3,2),
          impact_severity VARCHAR(50),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

    } catch (error) {
      this.logger.error('Error initializing tables:', error);
      throw error;
    }
  }

  setupTools() {
    // Tool: Legal Document Analyzer
    this.server.addTool({
      name: 'legal_document_analyzer',
      description: 'Analyze legal documents and identify key provisions, legal principles, and potential issues',
      inputSchema: {
        type: 'object',
        properties: {
          document_path: {
            type: 'string',
            description: 'Path to the legal document file'
          },
          document_content: {
            type: 'string',
            description: 'Raw text content of the legal document'
          },
          analysis_type: {
            type: 'string',
            enum: ['basic', 'comprehensive', 'constitutional', 'regulatory', 'contractual'],
            description: 'Type of legal analysis to perform',
            default: 'comprehensive'
          },
          extract_citations: {
            type: 'boolean',
            default: true,
            description: 'Extract legal citations and case references'
          },
          identify_key_provisions: {
            type: 'boolean',
            default: true,
            description: 'Identify key legal provisions and clauses'
          }
        }
      }
    }, this.legalDocumentAnalyzer.bind(this));

    // Tool: Constitutional Compliance Check
    this.server.addTool({
      name: 'constitutional_compliance_check',
      description: 'Check compliance with constitutional requirements and identify potential constitutional issues',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Legal document content to check for constitutional compliance'
          },
          focus_amendments: {
            type: 'array',
            items: {
              type: 'integer',
              minimum: 1,
              maximum: 27
            },
            description: 'Specific constitutional amendments to focus on (1-27)'
          },
          document_type: {
            type: 'string',
            enum: ['legislation', 'regulation', 'policy', 'executive_order', 'judicial_opinion'],
            description: 'Type of document being analyzed',
            default: 'legislation'
          },
          detailed_analysis: {
            type: 'boolean',
            default: true,
            description: 'Provide detailed constitutional analysis with specific citations'
          }
        },
        required: ['document_content']
      }
    }, this.constitutionalComplianceCheck.bind(this));

    // Tool: Precedent Research
    this.server.addTool({
      name: 'precedent_research',
      description: 'Research legal precedents and case law relevant to specific legal issues or principles',
      inputSchema: {
        type: 'object',
        properties: {
          legal_issue: {
            type: 'string',
            description: 'Legal issue or principle to research'
          },
          jurisdiction: {
            type: 'string',
            enum: ['federal', 'state', 'all'],
            description: 'Jurisdiction scope for precedent research',
            default: 'federal'
          },
          time_period: {
            type: 'string',
            enum: ['recent', 'landmark', 'all'],
            description: 'Time period focus for precedent research',
            default: 'all'
          },
          court_level: {
            type: 'string',
            enum: ['supreme_court', 'appellate', 'district', 'all'],
            description: 'Court level for precedent research',
            default: 'all'
          },
          max_results: {
            type: 'integer',
            minimum: 1,
            maximum: 50,
            default: 10,
            description: 'Maximum number of precedents to return'
          }
        },
        required: ['legal_issue']
      }
    }, this.precedentResearch.bind(this));

    // Tool: Regulatory Impact Analysis
    this.server.addTool({
      name: 'regulatory_impact_analysis',
      description: 'Analyze regulatory impact of proposed legislation or policy changes',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Proposed legislation or regulation content'
          },
          impact_categories: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['economic', 'social', 'environmental', 'administrative', 'constitutional']
            },
            description: 'Categories of impact to analyze',
            default: ['economic', 'social', 'administrative']
          },
          affected_sectors: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific sectors or industries that may be affected'
          },
          implementation_timeline: {
            type: 'string',
            description: 'Proposed implementation timeline'
          },
          stakeholder_analysis: {
            type: 'boolean',
            default: true,
            description: 'Include stakeholder impact analysis'
          }
        },
        required: ['document_content']
      }
    }, this.regulatoryImpactAnalysis.bind(this));

    // Tool: Legal Risk Assessment
    this.server.addTool({
      name: 'legal_risk_assessment',
      description: 'Assess legal risks and compliance issues in documents or proposed actions',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to assess for legal risks'
          },
          context_description: {
            type: 'string',
            description: 'Additional context about the situation or proposed action'
          },
          risk_categories: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['constitutional', 'statutory', 'regulatory', 'contractual', 'tort', 'criminal']
            },
            description: 'Categories of legal risk to assess',
            default: ['constitutional', 'statutory', 'regulatory']
          },
          risk_tolerance: {
            type: 'string',
            enum: ['low', 'medium', 'high'],
            description: 'Risk tolerance level for assessment',
            default: 'medium'
          },
          include_mitigation: {
            type: 'boolean',
            default: true,
            description: 'Include risk mitigation strategies'
          }
        },
        required: ['document_content']
      }
    }, this.legalRiskAssessment.bind(this));

    // Tool: Legal Citation Extractor
    this.server.addTool({
      name: 'legal_citation_extractor',
      description: 'Extract and validate legal citations from documents',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to extract citations from'
          },
          citation_types: {
            type: 'array',
            items: {
              type: 'string',   
              enum: ['case_law', 'statutes', 'regulations', 'constitutional', 'secondary_sources']
            },
            description: 'Types of citations to extract',
            default: ['case_law', 'statutes', 'constitutional']
          },
          validate_citations: {
            type: 'boolean',
            default: true,
            description: 'Validate citation format and accuracy'
          },
          include_context: {
            type: 'boolean',
            default: true,
            description: 'Include surrounding context for each citation'
          }
        },
        required: ['document_content']
      }
    }, this.legalCitationExtractor.bind(this));

    // Tool: Legal Terminology Analyzer
    this.server.addTool({
      name: 'legal_terminology_analyzer',
      description: 'Analyze legal terminology usage and provide definitions and explanations',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to analyze for legal terminology'
          },
          complexity_level: {
            type: 'string',
            enum: ['basic', 'intermediate', 'advanced'],
            description: 'Complexity level for terminology analysis',
            default: 'intermediate'
          },
          include_definitions: {
            type: 'boolean',
            default: true,
            description: 'Include definitions for identified legal terms'
          },
          plain_language_summary: {
            type: 'boolean',
            default: false,
            description: 'Provide plain language summary of complex terms'
          }
        },
        required: ['document_content']
      }
    }, this.legalTerminologyAnalyzer.bind(this));

    // Tool: Comparative Legal Analysis
    this.server.addTool({
      name: 'comparative_legal_analysis',
      description: 'Compare legal documents for consistency, conflicts, and alignment',
      inputSchema: {
        type: 'object',
        properties: {
          document_a: {
            type: 'string',
            description: 'First legal document content'
          },
          document_b: {
            type: 'string',
            description: 'Second legal document content'
          },
          comparison_type: {
            type: 'string',
            enum: ['consistency', 'conflicts', 'alignment', 'comprehensive'],
            description: 'Type of comparison to perform',
            default: 'comprehensive'
          },
          focus_areas: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific legal areas to focus comparison on'
          }
        },
        required: ['document_a', 'document_b']
      }
    }, this.comparativeLegalAnalysis.bind(this));
  }

  setupResources() {
    // Resource: Legal Analysis Results
    this.server.addResource({
      uri: 'legal://analysis_results',
      name: 'Legal Analysis Results',
      description: 'Comprehensive legal analysis results for processed documents',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'legal://constitutional_compliance',
      name: 'Constitutional Compliance Reports',
      description: 'Constitutional compliance analysis reports',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'legal://precedent_research',
      name: 'Legal Precedent Research',
      description: 'Legal precedent and case law research results',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'legal://risk_assessment',
      name: 'Legal Risk Assessments',
      description: 'Legal risk assessment reports and mitigation strategies',
      mimeType: 'application/json'
    });
  }

  setupHealthEndpoint() {
    const app = express();
    app.use(helmet());
    app.use(cors());
    app.use(express.json());

    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'legal-analysis-mcp-server',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0'
      });
    });

    const port = process.env.HEALTH_PORT || 8091;
    app.listen(port, () => {
      this.logger.info(`Legal Analysis MCP Server health endpoint running on port ${port}`);
    });
  }

  async legalDocumentAnalyzer(params) {
    try {
      const { 
        document_path, 
        document_content, 
        analysis_type = 'comprehensive',
        extract_citations = true,
        identify_key_provisions = true
      } = params;
      
      let content;
      let filename = 'unknown';
      
      if (document_path) {
        const fullPath = path.resolve(document_path);
        if (!await fs.pathExists(fullPath)) {
          throw new Error(`Document not found: ${document_path}`);
        }
        
        filename = path.basename(fullPath);
        content = await this.extractDocumentContent(fullPath);
      } else if (document_content) {
        content = document_content;
      } else {
        throw new Error('Either document_path or document_content must be provided');
      }

      const documentId = crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
      
      // Check cache first
      const cacheKey = `legal_analysis:${documentId}:${analysis_type}`;
      const cachedResult = await this.redisClient.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }

      // Perform comprehensive legal analysis
      const analysisResult = await this.performLegalAnalysis(
        content, 
        analysis_type, 
        extract_citations,
        identify_key_provisions
      );
      
      // Store results in database
      await this.storeLegalAnalysisResult(documentId, filename, analysisResult);
      
      // Cache results
      await this.redisClient.setex(cacheKey, 3600, JSON.stringify(analysisResult));
      
      return {
        document_id: documentId,
        filename,
        analysis_type,
        ...analysisResult
      };

    } catch (error) {
      this.logger.error('Error analyzing legal document:', error);
      throw error;
    }
  }

  async constitutionalComplianceCheck(params) {
    try {
      const { 
        document_content, 
        focus_amendments = [], 
        document_type = 'legislation',
        detailed_analysis = true
      } = params;
      
      const documentId = crypto.createHash('sha256').update(document_content).digest('hex').substring(0, 16);
      
      // Check cache
      const cacheKey = `constitutional_check:${documentId}:${focus_amendments.join(',')}`;
      const cachedResult = await this.redisClient.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }

      const complianceResult = await this.performConstitutionalAnalysis(
        document_content,
        focus_amendments,
        document_type,
        detailed_analysis
      );

      // Store in database
      await this.storeConstitutionalComplianceResult(documentId, complianceResult);

      // Cache results
      await this.redisClient.setex(cacheKey, 1800, JSON.stringify(complianceResult));

      return {
        document_id: documentId,
        document_type,
        focus_amendments,
        ...complianceResult
      };

    } catch (error) {
      this.logger.error('Error checking constitutional compliance:', error);
      throw error;
    }
  }

  async precedentResearch(params) {
    try {
      const {
        legal_issue,
        jurisdiction = 'federal',
        time_period = 'all',
        court_level = 'all',
        max_results = 10
      } = params;

      // Check cache
      const cacheKey = `precedent_research:${legal_issue}:${jurisdiction}:${time_period}:${court_level}`;
      const cachedResult = await this.redisClient.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }

      const precedents = await this.searchLegalPrecedents(
        legal_issue,
        jurisdiction,
        time_period,
        court_level,
        max_results
      );

      // Cache results
      await this.redisClient.setex(cacheKey, 3600, JSON.stringify(precedents));

      return {
        legal_issue,
        jurisdiction,
        time_period,
        court_level,
        total_found: precedents.length,
        precedents
      };

    } catch (error) {
      this.logger.error('Error researching precedents:', error);
      throw error;
    }
  }

  async regulatoryImpactAnalysis(params) {
    try {
      const {
        document_content,
        impact_categories = ['economic', 'social', 'administrative'],
        affected_sectors = [],
        implementation_timeline,
        stakeholder_analysis = true
      } = params;

      const documentId = crypto.createHash('sha256').update(document_content).digest('hex').substring(0, 16);

      const impactAnalysis = await this.performRegulatoryImpactAnalysis(
        document_content,
        impact_categories,
        affected_sectors,
        implementation_timeline,
        stakeholder_analysis
      );

      // Store in database
      await this.storeRegulatoryImpactResult(documentId, impactAnalysis);

      return {
        document_id: documentId,
        impact_categories,
        affected_sectors,
        implementation_timeline,
        ...impactAnalysis
      };

    } catch (error) {
      this.logger.error('Error analyzing regulatory impact:', error);
      throw error;
    }
  }

  async legalRiskAssessment(params) {
    try {
      const {
        document_content,
        context_description = '',
        risk_categories = ['constitutional', 'statutory', 'regulatory'],
        risk_tolerance = 'medium',
        include_mitigation = true
      } = params;

      const documentId = crypto.createHash('sha256').update(document_content).digest('hex').substring(0, 16);

      const riskAssessment = await this.performLegalRiskAssessment(
        document_content,
        context_description,
        risk_categories,
        risk_tolerance,
        include_mitigation
      );

      // Store in database
      await this.storeLegalRiskAssessment(documentId, riskAssessment);

      return {
        document_id: documentId,
        risk_categories,
        risk_tolerance,
        ...riskAssessment
      };

    } catch (error) {
      this.logger.error('Error assessing legal risks:', error);
      throw error;
    }
  }

  async legalCitationExtractor(params) {
    try {
      const {
        document_content,
        citation_types = ['case_law', 'statutes', 'constitutional'],
        validate_citations = true,
        include_context = true
      } = params;

      const citations = await this.extractLegalCitations(
        document_content,
        citation_types,
        validate_citations,
        include_context
      );

      return {
        citation_types,
        total_citations: citations.length,
        citations
      };

    } catch (error) {
      this.logger.error('Error extracting legal citations:', error);
      throw error;
    }
  }

  async legalTerminologyAnalyzer(params) {
    try {
      const {
        document_content,
        complexity_level = 'intermediate',
        include_definitions = true,
        plain_language_summary = false
      } = params;

      const terminologyAnalysis = await this.analyzeLegalTerminology(
        document_content,
        complexity_level,
        include_definitions,
        plain_language_summary
      );

      return {
        complexity_level,
        include_definitions,
        plain_language_summary,
        ...terminologyAnalysis
      };

    } catch (error) {
      this.logger.error('Error analyzing legal terminology:', error);
      throw error;
    }
  }

  async comparativeLegalAnalysis(params) {
    try {
      const {
        document_a,
        document_b,
        comparison_type = 'comprehensive',
        focus_areas = []
      } = params;

      const comparison = await this.performComparativeLegalAnalysis(
        document_a,
        document_b,
        comparison_type,
        focus_areas
      );

      return {
        comparison_type,
        focus_areas,
        ...comparison
      };

    } catch (error) {
      this.logger.error('Error performing comparative legal analysis:', error);
      throw error;
    }
  }

  // Helper Methods

  async extractDocumentContent(filePath) {
    const fileExt = path.extname(filePath).toLowerCase();
    
    if (fileExt === '.pdf') {
      const pdfBuffer = await fs.readFile(filePath);
      const pdfData = await pdfParse(pdfBuffer);
      return pdfData.text;
    } else if (fileExt === '.docx') {
      const docxBuffer = await fs.readFile(filePath);
      const result = await mammoth.extractRawText({ buffer: docxBuffer });
      return result.value;
    } else {
      return await fs.readFile(filePath, 'utf8');
    }
  }

  async performLegalAnalysis(content, analysisType, extractCitations, identifyProvisions) {
    // AI-powered legal analysis
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are an expert legal analyst. Analyze the following legal document with focus on ${analysisType} analysis. 
          ${extractCitations ? 'Extract legal citations.' : ''} 
          ${identifyProvisions ? 'Identify key legal provisions.' : ''}
          Provide structured analysis in JSON format.`
        },
        {
          role: "user",
          content: content.substring(0, 12000)
        }
      ],
      max_tokens: 1500
    });

    const analysis = {
      analysis_type: analysisType,
      ai_analysis: response.choices[0].message.content,
      word_count: content.split(/\s+/).length,
      character_count: content.length,
      token_count: this.tokenizer.encode(content).length
    };

    if (extractCitations) {
      analysis.citations = this.extractBasicCitations(content);
    }

    if (identifyProvisions) {
      analysis.key_provisions = await this.identifyKeyLegalProvisions(content);
    }

    analysis.document_classification = await this.classifyLegalDocument(content);
    analysis.complexity_score = this.calculateLegalComplexity(content);

    return analysis;
  }

  async performConstitutionalAnalysis(content, focusAmendments, docType, detailedAnalysis) {
    const systemPrompt = `You are a constitutional law expert. Analyze this ${docType} for constitutional compliance. 
    ${focusAmendments.length > 0 ? `Focus on amendments: ${focusAmendments.join(', ')}.` : 'Consider all relevant constitutional provisions.'}
    ${detailedAnalysis ? 'Provide detailed analysis with specific constitutional citations.' : 'Provide summary analysis.'}
    Return structured JSON with compliance assessment, issues, and recommendations.`;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: content.substring(0, 10000)
        }
      ],
      max_tokens: 1200
    });

    const constitutionalAnalysis = {
      compliance_analysis: response.choices[0].message.content,
      focus_amendments: focusAmendments,
      document_type: docType,
      detailed_analysis: detailedAnalysis,
      constitutional_references: this.extractConstitutionalReferences(content)
    };

    // Add compliance scoring
    constitutionalAnalysis.compliance_score = await this.calculateComplianceScore(content, focusAmendments);

    return constitutionalAnalysis;
  }

  async searchLegalPrecedents(legalIssue, jurisdiction, timePeriod, courtLevel, maxResults) {
    // Simulated precedent search - in production, this would integrate with legal databases
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are a legal research expert. Research relevant precedents for: "${legalIssue}"
          Jurisdiction: ${jurisdiction}, Time period: ${timePeriod}, Court level: ${courtLevel}
          Return structured JSON array of precedents with case name, citation, year, court, legal principle, and relevance score.`
        },
        {
          role: "user",
          content: `Find ${maxResults} most relevant precedents for: ${legalIssue}`
        }
      ],
      max_tokens: 1000
    });

    try {
      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      // Fallback if JSON parsing fails
      return [{
        case_name: "Research Results",
        citation: "Generated by AI",
        year: new Date().getFullYear(),
        court: jurisdiction,
        legal_principle: response.choices[0].message.content,
        relevance_score: 0.85
      }];
    }
  }

  async performRegulatoryImpactAnalysis(content, impactCategories, affectedSectors, timeline, stakeholderAnalysis) {
    const systemPrompt = `You are a regulatory impact assessment expert. Analyze the regulatory impact of this proposed legislation/regulation.
    Focus on: ${impactCategories.join(', ')}
    ${affectedSectors.length > 0 ? `Affected sectors: ${affectedSectors.join(', ')}` : ''}
    ${timeline ? `Implementation timeline: ${timeline}` : ''}
    ${stakeholderAnalysis ? 'Include stakeholder impact analysis.' : ''}
    Return structured JSON with impact assessment by category.`;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: content.substring(0, 10000)
        }
      ],
      max_tokens: 1200
    });

    return {
      impact_analysis: response.choices[0].message.content,
      impact_categories: impactCategories,
      affected_sectors: affectedSectors,
      implementation_timeline: timeline,
      stakeholder_analysis: stakeholderAnalysis,
      impact_summary: await this.generateImpactSummary(response.choices[0].message.content)
    };
  }

  async performLegalRiskAssessment(content, contextDescription, riskCategories, riskTolerance, includeMitigation) {
    const systemPrompt = `You are a legal risk assessment expert. Assess legal risks in this document/situation.
    Context: ${contextDescription}
    Risk categories: ${riskCategories.join(', ')}
    Risk tolerance: ${riskTolerance}
    ${includeMitigation ? 'Include mitigation strategies for each identified risk.' : ''}
    Return structured JSON with risk assessment by category, including probability and impact levels.`;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: content.substring(0, 10000)
        }
      ],
      max_tokens: 1200
    });

    return {
      risk_analysis: response.choices[0].message.content,
      context_description: contextDescription,
      risk_categories: riskCategories,
      risk_tolerance: riskTolerance,
      overall_risk_level: await this.calculateOverallRiskLevel(response.choices[0].message.content),
      mitigation_included: includeMitigation
    };
  }

  async extractLegalCitations(content, citationTypes, validateCitations, includeContext) {
    const citations = [];
    
    // Extract different types of citations
    if (citationTypes.includes('case_law')) {
      const caseCitations = content.match(/\b[A-Z][a-z]+\s+v\.\s+[A-Z][a-z]+(?:,\s*\d+\s+[A-Z][a-z]+\.?\s+\d+(?:\s*\(\d{4}\))?)?/g) || [];
      citations.push(...caseCitations.map(citation => ({
        type: 'case_law',
        citation,
        context: includeContext ? this.extractCitationContext(content, citation) : null
      })));
    }

    if (citationTypes.includes('statutes')) {
      const statuteCitations = content.match(/\b\d+\s+U\.S\.C\.?\s+§?\s*\d+/g) || [];
      citations.push(...statuteCitations.map(citation => ({
        type: 'statute',
        citation,
        context: includeContext ? this.extractCitationContext(content, citation) : null
      })));
    }

    if (citationTypes.includes('constitutional')) {
      const constitutionalCitations = content.match(/\b(?:U\.S\.?\s+)?Const\.?\s+(?:art\.?\s+[IVX]+|amend\.?\s+[IVX]+|\d+(?:st|nd|rd|th)?\s+amend\.?)/gi) || [];
      citations.push(...constitutionalCitations.map(citation => ({
        type: 'constitutional',
        citation,
        context: includeContext ? this.extractCitationContext(content, citation) : null
      })));
    }

    if (validateCitations) {
      for (let citation of citations) {
        citation.validation = await this.validateCitation(citation.citation, citation.type);
      }
    }

    return citations;
  }

  async analyzeLegalTerminology(content, complexityLevel, includeDefinitions, plainLanguageSummary) {
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are a legal terminology expert. Analyze legal terminology in this document for ${complexityLevel} complexity level.
          ${includeDefinitions ? 'Include definitions for legal terms.' : ''}
          ${plainLanguageSummary ? 'Provide plain language summaries for complex terms.' : ''}
          Return structured JSON with terminology analysis.`
        },
        {
          role: "user",
          content: content.substring(0, 8000)
        }
      ],
      max_tokens: 1000
    });

    const legalTerms = this.extractLegalTerms(content, complexityLevel);

    return {
      terminology_analysis: response.choices[0].message.content,
      complexity_level: complexityLevel,
      legal_terms_found: legalTerms,
      total_legal_terms: legalTerms.length,
      complexity_score: this.calculateTerminologyComplexity(legalTerms)
    };
  }

  async performComparativeLegalAnalysis(documentA, documentB, comparisonType, focusAreas) {
    const systemPrompt = `You are a comparative legal analysis expert. Compare these two legal documents for ${comparisonType}.
    ${focusAreas.length > 0 ? `Focus on: ${focusAreas.join(', ')}` : ''}
    Identify similarities, differences, conflicts, and alignment issues.
    Return structured JSON with detailed comparison results.`;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: `Document A:\n${documentA.substring(0, 5000)}\n\nDocument B:\n${documentB.substring(0, 5000)}`
        }
      ],
      max_tokens: 1200
    });

    return {
      comparison_analysis: response.choices[0].message.content,
      comparison_type: comparisonType,
      focus_areas: focusAreas,
      similarity_score: this.calculateDocumentSimilarity(documentA, documentB),
      conflict_indicators: await this.identifyLegalConflicts(documentA, documentB)
    };
  }

  // Additional helper methods
  extractBasicCitations(content) {
    return content.match(this.legalCitationRegex) || [];
  }

  async identifyKeyLegalProvisions(content) {
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "Identify key legal provisions in this document. Return as JSON array with provision text and significance."
        },
        {
          role: "user",
          content: content.substring(0, 8000)
        }
      ],
      max_tokens: 800
    });

    try {
      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      return [{ provision: response.choices[0].message.content, significance: "Key provision" }];
    }
  }

  async classifyLegalDocument(content) {
    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "Classify this legal document by type. Return JSON with document_type, legal_area, and confidence."
        },
        {
          role: "user",
          content: content.substring(0, 4000)
        }
      ],
      max_tokens: 200
    });

    try {
      return JSON.parse(response.choices[0].message.content);
    } catch (error) {
      return { document_type: "unknown", legal_area: "general", confidence: 0.5 };
    }
  }

  calculateLegalComplexity(content) {
    const words = content.split(/\s+/);
    const sentences = content.split(/[.!?]+/).length;
    const legalTerms = this.extractLegalTerms(content, 'basic').length;
    const citations = this.extractBasicCitations(content).length;
    
    // Simple complexity scoring
    const avgWordsPerSentence = words.length / sentences;
    const legalTermDensity = legalTerms / words.length;
    const citationDensity = citations / sentences;
    
    return Math.min(10, (avgWordsPerSentence / 10) + (legalTermDensity * 100) + (citationDensity * 50));
  }

  extractConstitutionalReferences(content) {
    const references = [];
    const amendmentMatches = content.match(/(?:amendment|amend\.?)\s+([IVX]+|\d+(?:st|nd|rd|th)?)/gi) || [];
    const articleMatches = content.match(/(?:article|art\.?)\s+([IVX]+)/gi) || [];
    
    references.push(...amendmentMatches.map(ref => ({ type: 'amendment', reference: ref })));
    references.push(...articleMatches.map(ref => ({ type: 'article', reference: ref })));
    
    return references;
  }

  async calculateComplianceScore(content, focusAmendments) {
    // Simplified compliance scoring - would be more sophisticated in production
    const issues = await this.identifyConstitutionalIssues(content, focusAmendments);
    const baseScore = 8.5;
    const deductions = issues.length * 0.5;
    return Math.max(0, Math.min(10, baseScore - deductions));
  }

  async identifyConstitutionalIssues(content, focusAmendments) {
    // Placeholder for constitutional issue identification
    const commonIssues = [];
    
    if (content.toLowerCase().includes('restrict') && content.toLowerCase().includes('speech')) {
      commonIssues.push('Potential First Amendment concern');
    }
    
    if (content.toLowerCase().includes('search') || content.toLowerCase().includes('seizure')) {
      commonIssues.push('Potential Fourth Amendment concern');
    }
    
    return commonIssues;
  }

  extractCitationContext(content, citation) {
    const index = content.indexOf(citation);
    if (index === -1) return null;
    
    const start = Math.max(0, index - 100);
    const end = Math.min(content.length, index + citation.length + 100);
    
    return content.substring(start, end);
  }

  async validateCitation(citation, type) {
    // Simplified citation validation
    return {
      is_valid: true, // Would implement proper validation
      format_correct: true,
      confidence: 0.85
    };
  }

  extractLegalTerms(content, complexityLevel) {
    const legalTermPatterns = {
      basic: /\b(?:whereas|therefore|herein|hereby|heretofore|plaintiff|defendant|jurisdiction|statute|regulation)\b/gi,
      intermediate: /\b(?:injunction|subpoena|habeas corpus|prima facie|res judicata|stare decisis|voir dire|amicus curiae)\b/gi,
      advanced: /\b(?:certiorari|mandamus|quo warranto|nunc pro tunc|ex parte|inter alia|sui generis|ultra vires)\b/gi
    };
    
    const pattern = legalTermPatterns[complexityLevel] || legalTermPatterns.basic;
    return content.match(pattern) || [];
  }

  calculateTerminologyComplexity(legalTerms) {
    // Simple complexity calculation based on term frequency and rarity
    return Math.min(10, legalTerms.length / 10);
  }

  calculateDocumentSimilarity(docA, docB) {
    const tokensA = new Set(this.tokenizer_nlp.tokenize(docA.toLowerCase()));
    const tokensB = new Set(this.tokenizer_nlp.tokenize(docB.toLowerCase()));
    const intersection = new Set([...tokensA].filter(x => tokensB.has(x)));
    const union = new Set([...tokensA, ...tokensB]);
    
    return intersection.size / union.size;
  }

  async identifyLegalConflicts(docA, docB) {
    // Simplified conflict identification
    const conflicts = [];
    
    if (docA.includes('shall') && docB.includes('shall not')) {
      conflicts.push('Conflicting mandatory provisions');
    }
    
    if (docA.includes('prohibited') && docB.includes('permitted')) {
      conflicts.push('Conflicting permission/prohibition');
    }
    
    return conflicts;
  }

  async generateImpactSummary(impactAnalysis) {
    return {
      overall_impact: 'moderate', // Would be calculated from analysis
      key_concerns: ['Implementation cost', 'Regulatory burden'],
      benefits: ['Improved compliance', 'Enhanced protection']
    };
  }

  async calculateOverallRiskLevel(riskAnalysis) {
    // Simplified overall risk calculation
    if (riskAnalysis.toLowerCase().includes('high risk')) return 'high';
    if (riskAnalysis.toLowerCase().includes('low risk')) return 'low';
    return 'medium';
  }

  loadConstitutionalReferences() {
    return {
      amendments: [
        { number: 1, topic: 'Freedom of speech, religion, press, assembly, petition' },
        { number: 2, topic: 'Right to bear arms' },
        { number: 4, topic: 'Protection against unreasonable searches and seizures' },
        { number: 5, topic: 'Due process, self-incrimination, double jeopardy' },
        { number: 14, topic: 'Equal protection, due process' }
        // Would include all amendments in production
      ]
    };
  }

  // Database storage methods
  async storeLegalAnalysisResult(documentId, filename, result) {
    try {
      await this.pgClient.query(
        `INSERT INTO legal_analysis (document_id, filename, document_type, content_hash, analysis_result, compliance_score)
         VALUES ($1, $2, $3, $4, $5, $6)
         ON CONFLICT (document_id) DO UPDATE SET
         analysis_result = $5, compliance_score = $6, updated_at = CURRENT_TIMESTAMP`,
        [
          documentId,
          filename,
          result.document_classification?.document_type || 'unknown',
          documentId,
          JSON.stringify(result),
          result.compliance_score || null
        ]
      );
    } catch (error) {
      this.logger.error('Error storing legal analysis result:', error);
    }
  }

  async storeConstitutionalComplianceResult(documentId, result) {
    try {
      await this.pgClient.query(
        `INSERT INTO constitutional_compliance (document_id, compliance_status, issues_identified, recommendations)
         VALUES ($1, $2, $3, $4)`,
        [
          documentId,
          result.compliance_score > 7 ? 'compliant' : 'issues_identified',
          [],
          []
        ]
      );
    } catch (error) {
      this.logger.error('Error storing constitutional compliance result:', error);
    }
  }

  async storeRegulatoryImpactResult(documentId, result) {
    try {
      await this.pgClient.query(
        `INSERT INTO regulatory_impact (document_id, regulation_type, impact_category, impact_level, affected_parties)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          documentId,
          'proposed_legislation',
          'comprehensive',
          result.impact_summary?.overall_impact || 'moderate',
          result.affected_sectors || []
        ]
      );
    } catch (error) {
      this.logger.error('Error storing regulatory impact result:', error);
    }
  }

  async storeLegalRiskAssessment(documentId, result) {
    try {
      await this.pgClient.query(
        `INSERT INTO legal_risk_assessment (document_id, risk_category, risk_level, risk_description, probability, impact_severity)
         VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          documentId,
          'comprehensive',
          result.overall_risk_level || 'medium',
          result.risk_analysis || '',
          0.5,
          result.overall_risk_level || 'medium'
        ]
      );
    } catch (error) {
      this.logger.error('Error storing legal risk assessment:', error);
    }
  }
}

// Initialize and start the server
const server = new LegalAnalysisServer();

async function main() {
  try {
    await server.initialize();
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      server.logger.info('Shutting down Legal Analysis MCP Server...');
      if (server.pgClient) await server.pgClient.end();
      if (server.redisClient) await server.redisClient.quit();
      process.exit(0);
    });

    server.logger.info('Legal Analysis MCP Server initialized and ready');
    
    // Keep the process running
    process.stdin.resume();
    
  } catch (error) {
    console.error('Failed to start Legal Analysis MCP Server:', error);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default LegalAnalysisServer;