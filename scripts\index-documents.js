#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Document Indexing Script
 * Automatically indexes all political documents in the vector search system
 */

class DocumentIndexer {
  constructor() {
    this.vectorSearchUrl = process.env.VECTOR_SEARCH_URL || 'http://localhost:8089';
    this.manifestoPath = path.join(__dirname, '..', 'manifesto');
    this.whitePapersPath = path.join(__dirname, '..', 'white_papers_markdown');
    this.n8nDocsPath = path.join(__dirname, '..', 'n8n docs');
    
    this.indexedCount = 0;
    this.errors = [];
  }

  async initialize() {
    console.log('🚀 Starting Document Indexing Process');
    console.log(`Vector Search URL: ${this.vectorSearchUrl}`);
    console.log(`Manifesto Path: ${this.manifestoPath}`);
    console.log(`White Papers Path: ${this.whitePapersPath}`);
    console.log(`N8N Docs Path: ${this.n8nDocsPath}`);
    
    // Wait for vector search service to be ready
    await this.waitForService();
  }

  async waitForService() {
    const maxRetries = 30;
    const retryDelay = 2000;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        await axios.get(`${this.vectorSearchUrl}/health`);
        console.log('✅ Vector Search Service is ready');
        return;
      } catch (error) {
        console.log(`⏳ Waiting for Vector Search Service... (${i + 1}/${maxRetries})`);
        await this.delay(retryDelay);
      }
    }
    
    throw new Error('Vector Search Service is not responding');
  }

  async indexAllDocuments() {
    console.log('📚 Starting document indexing...');
    
    // Index manifesto documents
    await this.indexDirectory(this.manifestoPath, 'manifesto');
    
    // Index white papers
    await this.indexDirectory(this.whitePapersPath, 'whitepaper');
    
    // Index n8n docs
    await this.indexDirectory(this.n8nDocsPath, 'documentation');
    
    console.log(`\n🎉 Indexing completed!`);
    console.log(`📄 Total documents indexed: ${this.indexedCount}`);
    
    if (this.errors.length > 0) {
      console.log(`❌ Errors encountered: ${this.errors.length}`);
      this.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    // Get final collection stats
    await this.getCollectionStats();
  }

  async indexDirectory(dirPath, documentType) {
    if (!await fs.pathExists(dirPath)) {
      console.log(`⚠️  Directory not found: ${dirPath}`);
      return;
    }

    console.log(`\n📁 Indexing ${documentType} documents from: ${dirPath}`);
    
    const files = await this.findMarkdownFiles(dirPath);
    console.log(`Found ${files.length} markdown files`);
    
    for (const filePath of files) {
      try {
        await this.indexDocument(filePath, documentType, dirPath);
        this.indexedCount++;
        
        // Progress indicator
        if (this.indexedCount % 10 === 0) {
          console.log(`📊 Indexed ${this.indexedCount} documents so far...`);
        }
      } catch (error) {
        const errorMsg = `Failed to index ${filePath}: ${error.message}`;
        this.errors.push(errorMsg);
        console.log(`❌ ${errorMsg}`);
      }
    }
  }

  async indexDocument(filePath, documentType, basePath) {
    const content = await fs.readFile(filePath, 'utf8');
    const relativePath = path.relative(basePath, filePath);
    const filename = path.basename(filePath, '.md');
    
    const category = this.extractCategoryFromPath(relativePath);
    const documentId = `${documentType}_${filename.replace(/[^a-zA-Z0-9]/g, '_')}`;
    
    const metadata = {
      title: this.extractTitleFromContent(content) || filename,
      category,
      document_type: documentType,
      file_path: relativePath,
      author: 'Beau Lewis',
      indexed_by: 'automated_indexing_script',
      source_directory: basePath
    };

    // Call vector search API to index the document
    const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
      tool: 'index_document',
      parameters: {
        document_id: documentId,
        content,
        metadata
      }
    });

    if (response.data.success) {
      console.log(`✅ Indexed: ${filename} (${response.data.chunk_count} chunks)`);
    } else {
      throw new Error(response.data.error || 'Unknown error');
    }
  }

  async findMarkdownFiles(dirPath) {
    const files = [];
    
    const items = await fs.readdir(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        const subFiles = await this.findMarkdownFiles(itemPath);
        files.push(...subFiles);
      } else if (item.endsWith('.md')) {
        files.push(itemPath);
      }
    }
    
    return files;
  }

  extractCategoryFromPath(filePath) {
    const parts = filePath.split(path.sep);
    
    // Category mapping
    const categoryMap = {
      'healthcare': 'healthcare',
      'education': 'education',
      'economic': 'economic_policy',
      'housing': 'housing',
      'jobs': 'jobs_automation',
      'constitution': 'constitutional_amendments',
      'ethics': 'ethics_accountability',
      'repair': 'rights_repair_grow',
      'grow': 'rights_repair_grow',
      'funding': 'funding_revenue',
      'social': 'social_policy',
      'trust': 'social_policy',
      'guns': 'gun_policy',
      'others': 'general'
    };

    for (const part of parts) {
      const lowerPart = part.toLowerCase();
      for (const [key, value] of Object.entries(categoryMap)) {
        if (lowerPart.includes(key)) {
          return value;
        }
      }
    }

    return 'general';
  }

  extractTitleFromContent(content) {
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('# ')) {
        return trimmed.substring(2).trim();
      }
    }
    
    return null;
  }

  async getCollectionStats() {
    try {
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_collection_stats',
        parameters: {}
      });

      if (response.data.success) {
        const stats = response.data;
        console.log(`\n📊 Collection Statistics:`);
        console.log(`  Total Documents: ${stats.total_documents}`);
        console.log(`  Categories:`);
        
        if (stats.categories) {
          stats.categories.forEach(cat => {
            console.log(`    - ${cat.category}: ${cat.count} documents (${cat.total_chunks} chunks, ${cat.total_tokens} tokens)`);
          });
        }
      }
    } catch (error) {
      console.log(`❌ Failed to get collection stats: ${error.message}`);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Main execution
async function main() {
  const indexer = new DocumentIndexer();
  
  try {
    await indexer.initialize();
    await indexer.indexAllDocuments();
    
    console.log('\n🎯 Document indexing completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('💥 Document indexing failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (process.argv[1] === __filename) {
  main();
}

export default DocumentIndexer;