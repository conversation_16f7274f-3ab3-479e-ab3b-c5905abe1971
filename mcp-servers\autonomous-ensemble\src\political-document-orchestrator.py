#!/usr/bin/env python3
"""
Political Document Processing Autonomous Orchestrator
Adapted from the autonomous AI development system patterns
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import redis
from pydantic import BaseModel

class TaskPriority(Enum):
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class PoliticalDocumentTask:
    """Task representation for political document processing"""
    task_id: str
    task_type: str  # 'document_analysis', 'research', 'synthesis', 'fact_check'
    priority: TaskPriority
    status: TaskStatus = TaskStatus.PENDING
    document_id: Optional[str] = None
    query: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    assigned_agent: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class AgentType(Enum):
    RESEARCH = "research"
    ANALYSIS = "analysis"
    SYNTHESIS = "synthesis"
    FACT_CHECK = "fact_check"
    QUALITY_CONTROL = "quality_control"

@dataclass
class AgentResource:
    """Agent resource representation"""
    agent_id: str
    agent_type: AgentType
    status: str  # 'idle', 'busy', 'error'
    current_task: Optional[str] = None
    capabilities: List[str] = field(default_factory=list)
    last_activity: datetime = field(default_factory=datetime.now)
    performance_metrics: Dict[str, float] = field(default_factory=dict)

class PoliticalDocumentOrchestrator:
    """
    Master orchestrator for political document processing
    Adapted from AutonomousOrchestrator patterns
    """
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379):
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
        self.agents: Dict[str, AgentResource] = {}
        self.tasks: Dict[str, PoliticalDocumentTask] = {}
        self.task_queue: List[PoliticalDocumentTask] = []
        self.running = False
        
        # Configuration
        self.max_concurrent_tasks = 10
        self.task_timeout = 300  # 5 minutes
        self.agent_health_check_interval = 30  # seconds
        
        # Initialize logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize agent types
        self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize different agent types for political document processing"""
        agent_configs = [
            {
                "agent_id": "research_agent_01",
                "agent_type": AgentType.RESEARCH,
                "capabilities": ["perplexity_search", "web_scraping", "source_verification"]
            },
            {
                "agent_id": "analysis_agent_01", 
                "agent_type": AgentType.ANALYSIS,
                "capabilities": ["document_parsing", "sentiment_analysis", "entity_extraction"]
            },
            {
                "agent_id": "synthesis_agent_01",
                "agent_type": AgentType.SYNTHESIS,
                "capabilities": ["content_synthesis", "report_generation", "cross_reference"]
            },
            {
                "agent_id": "fact_check_agent_01",
                "agent_type": AgentType.FACT_CHECK,
                "capabilities": ["claim_verification", "source_validation", "bias_detection"]
            },
            {
                "agent_id": "quality_control_agent_01",
                "agent_type": AgentType.QUALITY_CONTROL,
                "capabilities": ["output_validation", "quality_scoring", "error_detection"]
            }
        ]
        
        for config in agent_configs:
            agent = AgentResource(
                agent_id=config["agent_id"],
                agent_type=config["agent_type"],
                status="idle",
                capabilities=config["capabilities"]
            )
            self.agents[config["agent_id"]] = agent
    
    async def start(self):
        """Start the orchestrator"""
        self.running = True
        self.logger.info("Starting Political Document Orchestrator")
        
        # Start background tasks
        await asyncio.gather(
            self._task_scheduler(),
            self._agent_health_monitor(),
            self._performance_monitor()
        )
    
    async def stop(self):
        """Stop the orchestrator"""
        self.running = False
        self.logger.info("Stopping Political Document Orchestrator")
    
    async def submit_task(self, task: PoliticalDocumentTask) -> str:
        """Submit a new task for processing"""
        self.tasks[task.task_id] = task
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda t: t.priority.value)
        
        # Publish task event
        await self._publish_event("task_submitted", {
            "task_id": task.task_id,
            "task_type": task.task_type,
            "priority": task.priority.value
        })
        
        self.logger.info(f"Task {task.task_id} submitted for processing")
        return task.task_id
    
    async def _task_scheduler(self):
        """Main task scheduling loop"""
        while self.running:
            try:
                # Process pending tasks
                await self._process_task_queue()
                
                # Check for completed tasks
                await self._check_completed_tasks()
                
                # Wait before next iteration
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in task scheduler: {e}")
                await asyncio.sleep(5)
    
    async def _process_task_queue(self):
        """Process the task queue and assign tasks to agents"""
        if not self.task_queue:
            return
        
        # Get available agents
        available_agents = {
            agent_id: agent for agent_id, agent in self.agents.items()
            if agent.status == "idle"
        }
        
        if not available_agents:
            return
        
        # Process tasks
        tasks_to_process = []
        for task in self.task_queue[:]:
            if task.status != TaskStatus.PENDING:
                continue
                
            # Check dependencies
            if not self._check_task_dependencies(task):
                continue
                
            # Find suitable agent
            suitable_agent = self._find_suitable_agent(task, available_agents)
            if suitable_agent:
                tasks_to_process.append((task, suitable_agent))
                available_agents.pop(suitable_agent.agent_id)
                
            if len(tasks_to_process) >= self.max_concurrent_tasks:
                break
        
        # Assign tasks to agents
        for task, agent in tasks_to_process:
            await self._assign_task_to_agent(task, agent)
            self.task_queue.remove(task)
    
    def _check_task_dependencies(self, task: PoliticalDocumentTask) -> bool:
        """Check if task dependencies are satisfied"""
        for dep_id in task.dependencies:
            if dep_id in self.tasks:
                dep_task = self.tasks[dep_id]
                if dep_task.status != TaskStatus.COMPLETED:
                    return False
            else:
                # Dependency not found
                return False
        return True
    
    def _find_suitable_agent(self, task: PoliticalDocumentTask, 
                           available_agents: Dict[str, AgentResource]) -> Optional[AgentResource]:
        """Find the most suitable agent for a task"""
        # Map task types to agent types
        task_to_agent_mapping = {
            "document_analysis": AgentType.ANALYSIS,
            "research": AgentType.RESEARCH,
            "synthesis": AgentType.SYNTHESIS,
            "fact_check": AgentType.FACT_CHECK,
            "quality_control": AgentType.QUALITY_CONTROL
        }
        
        required_agent_type = task_to_agent_mapping.get(task.task_type)
        if not required_agent_type:
            return None
        
        # Find agents of the required type
        suitable_agents = [
            agent for agent in available_agents.values()
            if agent.agent_type == required_agent_type
        ]
        
        if not suitable_agents:
            return None
        
        # Select best agent based on performance metrics
        return max(suitable_agents, 
                  key=lambda a: a.performance_metrics.get("success_rate", 0.5))
    
    async def _assign_task_to_agent(self, task: PoliticalDocumentTask, 
                                   agent: AgentResource):
        """Assign a task to an agent"""
        # Update task status
        task.status = TaskStatus.RUNNING
        task.assigned_agent = agent.agent_id
        task.started_at = datetime.now()
        
        # Update agent status
        agent.status = "busy"
        agent.current_task = task.task_id
        agent.last_activity = datetime.now()
        
        # Publish assignment event
        await self._publish_event("task_assigned", {
            "task_id": task.task_id,
            "agent_id": agent.agent_id,
            "task_type": task.task_type
        })
        
        # Start task execution
        asyncio.create_task(self._execute_task(task, agent))
        
        self.logger.info(f"Task {task.task_id} assigned to agent {agent.agent_id}")
    
    async def _execute_task(self, task: PoliticalDocumentTask, 
                           agent: AgentResource):
        """Execute a task through the assigned agent"""
        try:
            # Execute task based on type
            if task.task_type == "document_analysis":
                result = await self._execute_document_analysis(task, agent)
            elif task.task_type == "research":
                result = await self._execute_research(task, agent)
            elif task.task_type == "synthesis":
                result = await self._execute_synthesis(task, agent)
            elif task.task_type == "fact_check":
                result = await self._execute_fact_check(task, agent)
            elif task.task_type == "quality_control":
                result = await self._execute_quality_control(task, agent)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
            
            # Update task with result
            task.status = TaskStatus.COMPLETED
            task.result = result
            task.completed_at = datetime.now()
            
            # Update agent performance metrics
            await self._update_agent_performance(agent, True)
            
        except Exception as e:
            # Handle task failure
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.completed_at = datetime.now()
            
            # Update agent performance metrics
            await self._update_agent_performance(agent, False)
            
            self.logger.error(f"Task {task.task_id} failed: {e}")
        
        finally:
            # Reset agent status
            agent.status = "idle"
            agent.current_task = None
            agent.last_activity = datetime.now()
            
            # Publish completion event
            await self._publish_event("task_completed", {
                "task_id": task.task_id,
                "agent_id": agent.agent_id,
                "status": task.status.value,
                "result": task.result is not None
            })
    
    async def _execute_document_analysis(self, task: PoliticalDocumentTask, 
                                        agent: AgentResource) -> Dict[str, Any]:
        """Execute document analysis task"""
        # Simulate document analysis
        await asyncio.sleep(2)
        
        return {
            "document_id": task.document_id,
            "analysis_type": "political_document",
            "key_topics": ["immigration", "healthcare", "economy"],
            "sentiment": "neutral",
            "entities": [
                {"text": "Democratic Party", "type": "ORG"},
                {"text": "Republican Party", "type": "ORG"}
            ],
            "confidence": 0.85
        }
    
    async def _execute_research(self, task: PoliticalDocumentTask, 
                               agent: AgentResource) -> Dict[str, Any]:
        """Execute research task"""
        # Simulate research
        await asyncio.sleep(3)
        
        return {
            "query": task.query,
            "sources": [
                {"url": "https://example.com/source1", "title": "Political Analysis"},
                {"url": "https://example.com/source2", "title": "Policy Review"}
            ],
            "key_findings": ["Finding 1", "Finding 2"],
            "confidence": 0.78
        }
    
    async def _execute_synthesis(self, task: PoliticalDocumentTask, 
                                agent: AgentResource) -> Dict[str, Any]:
        """Execute synthesis task"""
        # Simulate synthesis
        await asyncio.sleep(4)
        
        return {
            "synthesis_type": "cross_document",
            "summary": "Synthesized political document analysis",
            "key_insights": ["Insight 1", "Insight 2"],
            "recommendations": ["Recommendation 1", "Recommendation 2"],
            "confidence": 0.82
        }
    
    async def _execute_fact_check(self, task: PoliticalDocumentTask, 
                                 agent: AgentResource) -> Dict[str, Any]:
        """Execute fact checking task"""
        # Simulate fact checking
        await asyncio.sleep(3)
        
        return {
            "claims_checked": 5,
            "verified_claims": 3,
            "disputed_claims": 1,
            "unverified_claims": 1,
            "fact_check_score": 0.75,
            "details": [
                {"claim": "Claim 1", "status": "verified", "confidence": 0.9},
                {"claim": "Claim 2", "status": "disputed", "confidence": 0.6}
            ]
        }
    
    async def _execute_quality_control(self, task: PoliticalDocumentTask, 
                                      agent: AgentResource) -> Dict[str, Any]:
        """Execute quality control task"""
        # Simulate quality control
        await asyncio.sleep(2)
        
        return {
            "quality_score": 0.88,
            "issues_found": 2,
            "recommendations": ["Improve source diversity", "Add more context"],
            "approved": True
        }
    
    async def _update_agent_performance(self, agent: AgentResource, success: bool):
        """Update agent performance metrics"""
        if "success_rate" not in agent.performance_metrics:
            agent.performance_metrics["success_rate"] = 0.5
            agent.performance_metrics["total_tasks"] = 0
        
        total_tasks = agent.performance_metrics["total_tasks"]
        current_rate = agent.performance_metrics["success_rate"]
        
        # Update metrics
        agent.performance_metrics["total_tasks"] = total_tasks + 1
        agent.performance_metrics["success_rate"] = (
            (current_rate * total_tasks + (1 if success else 0)) / (total_tasks + 1)
        )
        
        # Update last activity
        agent.last_activity = datetime.now()
    
    async def _check_completed_tasks(self):
        """Check for completed tasks and handle post-processing"""
        for task in self.tasks.values():
            if task.status == TaskStatus.COMPLETED and task.result:
                # Handle completed task
                await self._handle_completed_task(task)
    
    async def _handle_completed_task(self, task: PoliticalDocumentTask):
        """Handle post-processing of completed tasks"""
        # Store results in Redis
        await self._store_task_result(task)
        
        # Trigger dependent tasks
        await self._trigger_dependent_tasks(task)
    
    async def _store_task_result(self, task: PoliticalDocumentTask):
        """Store task result in Redis"""
        result_key = f"task_result:{task.task_id}"
        result_data = {
            "task_id": task.task_id,
            "result": task.result,
            "completed_at": task.completed_at.isoformat(),
            "agent_id": task.assigned_agent
        }
        
        self.redis_client.set(result_key, json.dumps(result_data))
        self.redis_client.expire(result_key, 3600)  # Expire after 1 hour
    
    async def _trigger_dependent_tasks(self, task: PoliticalDocumentTask):
        """Trigger tasks that depend on this completed task"""
        for pending_task in self.task_queue:
            if task.task_id in pending_task.dependencies:
                # Check if all dependencies are now satisfied
                if self._check_task_dependencies(pending_task):
                    self.logger.info(f"Dependencies satisfied for task {pending_task.task_id}")
    
    async def _agent_health_monitor(self):
        """Monitor agent health and performance"""
        while self.running:
            try:
                current_time = datetime.now()
                
                for agent in self.agents.values():
                    # Check if agent is stuck
                    if agent.status == "busy" and agent.current_task:
                        task = self.tasks.get(agent.current_task)
                        if task and task.started_at:
                            elapsed = (current_time - task.started_at).total_seconds()
                            if elapsed > self.task_timeout:
                                await self._handle_stuck_agent(agent, task)
                    
                    # Check agent activity
                    if agent.last_activity:
                        inactive_time = (current_time - agent.last_activity).total_seconds()
                        if inactive_time > 300:  # 5 minutes
                            self.logger.warning(f"Agent {agent.agent_id} inactive for {inactive_time} seconds")
                
                await asyncio.sleep(self.agent_health_check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in agent health monitor: {e}")
                await asyncio.sleep(30)
    
    async def _handle_stuck_agent(self, agent: AgentResource, task: PoliticalDocumentTask):
        """Handle stuck agent situation"""
        self.logger.error(f"Agent {agent.agent_id} stuck on task {task.task_id}")
        
        # Mark task as failed
        task.status = TaskStatus.FAILED
        task.error = "Task timeout"
        task.completed_at = datetime.now()
        
        # Reset agent
        agent.status = "idle"
        agent.current_task = None
        agent.last_activity = datetime.now()
        
        # Publish event
        await self._publish_event("task_timeout", {
            "task_id": task.task_id,
            "agent_id": agent.agent_id
        })
    
    async def _performance_monitor(self):
        """Monitor system performance and metrics"""
        while self.running:
            try:
                # Collect metrics
                metrics = {
                    "timestamp": datetime.now().isoformat(),
                    "active_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
                    "completed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]),
                    "failed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
                    "agent_utilization": {
                        agent_id: agent.status for agent_id, agent in self.agents.items()
                    }
                }
                
                # Store metrics
                self.redis_client.lpush("performance_metrics", json.dumps(metrics))
                self.redis_client.ltrim("performance_metrics", 0, 99)  # Keep last 100 entries
                
                await asyncio.sleep(60)  # Collect metrics every minute
                
            except Exception as e:
                self.logger.error(f"Error in performance monitor: {e}")
                await asyncio.sleep(60)
    
    async def _publish_event(self, event_type: str, data: Dict[str, Any]):
        """Publish event to Redis pub/sub"""
        event = {
            "type": event_type,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        
        self.redis_client.publish("orchestrator_events", json.dumps(event))
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            return {
                "task_id": task.task_id,
                "status": task.status.value,
                "progress": self._calculate_task_progress(task),
                "assigned_agent": task.assigned_agent,
                "result": task.result,
                "error": task.error
            }
        return None
    
    def _calculate_task_progress(self, task: PoliticalDocumentTask) -> float:
        """Calculate task progress percentage"""
        if task.status == TaskStatus.PENDING:
            return 0.0
        elif task.status == TaskStatus.RUNNING:
            if task.started_at:
                elapsed = (datetime.now() - task.started_at).total_seconds()
                # Estimate progress based on elapsed time (rough estimate)
                return min(elapsed / 60.0, 0.9)  # Max 90% until completion
            return 0.1
        elif task.status == TaskStatus.COMPLETED:
            return 1.0
        else:  # FAILED or CANCELLED
            return 0.0
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            "orchestrator_status": "running" if self.running else "stopped",
            "total_agents": len(self.agents),
            "active_agents": len([a for a in self.agents.values() if a.status == "busy"]),
            "total_tasks": len(self.tasks),
            "pending_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING]),
            "running_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
            "completed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.COMPLETED]),
            "failed_tasks": len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
            "agents": {
                agent_id: {
                    "type": agent.agent_type.value,
                    "status": agent.status,
                    "current_task": agent.current_task,
                    "performance": agent.performance_metrics
                }
                for agent_id, agent in self.agents.items()
            }
        }


# Example usage
async def main():
    orchestrator = PoliticalDocumentOrchestrator()
    
    # Create a sample task
    task = PoliticalDocumentTask(
        task_id="doc_analysis_001",
        task_type="document_analysis",
        priority=TaskPriority.HIGH,
        document_id="manifesto_001",
        context={"source": "political_party_a"}
    )
    
    # Start orchestrator
    await orchestrator.start()
    
    # Submit task
    await orchestrator.submit_task(task)
    
    # Wait for completion
    await asyncio.sleep(10)
    
    # Get status
    status = await orchestrator.get_task_status(task.task_id)
    print(f"Task Status: {status}")
    
    # Get system status
    system_status = await orchestrator.get_system_status()
    print(f"System Status: {system_status}")
    
    # Stop orchestrator
    await orchestrator.stop()

if __name__ == "__main__":
    asyncio.run(main())