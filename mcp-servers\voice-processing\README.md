# Voice Processing MCP Server

A comprehensive voice and speech processing server with real-time transcription, political content analysis, and advanced security features for the political document processing system.

## Overview

The Voice Processing MCP Server provides:
- High-accuracy speech transcription using OpenAI Whisper
- Real-time voice processing with WebSocket support
- Political content analysis and sentiment detection
- Multi-language support with confidence scoring
- OAuth 2.1 authentication and session management
- Comprehensive analytics and usage tracking

## Features

### 🎙️ Speech Processing
- **Whisper Transcription**: State-of-the-art transcription accuracy
- **Multi-Language Support**: Automatic language detection and processing
- **Confidence Scoring**: Detailed confidence metrics for transcriptions
- **Audio Format Conversion**: Automatic conversion to optimal formats using FFmpeg

### 🏛️ Political Analysis
- **Content Detection**: Identifies political topics, themes, and categories
- **Sentiment Analysis**: Comprehensive sentiment scoring and classification
- **Entity Recognition**: Extracts people, places, organizations, and topics
- **AI-Enhanced Analysis**: GPT-4 powered political discourse analysis

### ⚡ Real-Time Processing
- **WebSocket Support**: Live transcription sessions
- **Live Political Analysis**: Real-time content analysis during transcription
- **Session Management**: Secure session handling with Redis
- **Connection Monitoring**: Active connection tracking and metrics

### 🔐 Security Features
- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Multi-tier rate limiting for different operations
- **Session Management**: Redis-backed secure sessions
- **Audit Logging**: Comprehensive security and usage logging

## Installation

### Prerequisites
- Node.js 16+
- Redis 6+
- PostgreSQL 12+
- FFmpeg (for audio processing)
- OpenAI API key
- Docker (optional)

### Environment Variables

```bash
# Server Configuration
MCP_SERVER_PORT=8092
LOG_LEVEL=info

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
WHISPER_MODEL=whisper-1

# Database Configuration
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432
POSTGRES_DB=political_conversations
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=your_postgres_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Authentication Configuration
JWT_SECRET=your_jwt_secret_key
SESSION_SECRET=your_session_secret
TOKEN_EXPIRY=3600  # 1 hour

# Admin Credentials (for demo)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5678
```

### Docker Installation

```bash
docker run -d \
  --name voice-processing-mcp \
  -p 8092:8092 \
  --env-file .env \
  --network n8n-network \
  -v /tmp:/tmp \
  voice-processing-mcp:latest
```

### Manual Installation

```bash
# Clone the repository
git clone <repository-url>
cd mcp-servers/voice-processing

# Install dependencies
npm install

# Install system dependencies
apt-get update && apt-get install -y ffmpeg

# Initialize database
npm run init-db

# Start the server
npm start
```

## Authentication

### Login Endpoint
```bash
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "your_admin_password"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expiresIn": 3600,
  "user": {
    "id": "admin-user",
    "username": "admin"
  }
}
```

### Using Authentication Token
Include the token in all MCP tool requests:
```
Authorization: Bearer <your-jwt-token>
```

## MCP Tools

### 1. transcribe_audio_file
Transcribe uploaded audio file with political content analysis.

**Input Schema:**
```json
{
  "language": "en|es|fr",
  "prompt": "Context prompt to improve transcription accuracy",
  "includePoliticalAnalysis": true
}
```

**Usage:**
```bash
curl -X POST http://localhost:8092/mcp/call \
  -H "Authorization: Bearer <token>" \
  -F "tool=transcribe_audio_file" \
  -F "arguments={\"language\":\"en\",\"includePoliticalAnalysis\":true}" \
  -F "audioFiles=@speech.wav"
```

**Response:**
```json
{
  "message": "Successfully processed 1 audio files",
  "results": [
    {
      "filename": "speech.wav",
      "transcription": {
        "text": "Ladies and gentlemen, today we discuss the federal budget...",
        "confidence": 0.95,
        "language": "en",
        "duration": 120.5
      },
      "analysis": {
        "sentiment": {
          "score": 2,
          "classification": "positive"
        },
        "politicalCategories": {
          "economy": ["budget", "fiscal", "debt"],
          "governance": ["federal", "policy"]
        },
        "entities": {
          "people": ["Senator Johnson"],
          "organizations": ["Congress"],
          "topics": ["budget", "fiscal policy"]
        }
      }
    }
  ]
}
```

### 2. realtime_transcription_session
Start a real-time voice transcription session with live political analysis.

**Input Schema:**
```json
{
  "sessionId": "unique-session-id",
  "language": "en",
  "enableLivePoliticalAnalysis": true
}
```

**Response:**
```json
{
  "sessionId": "unique-session-id",
  "status": "ready",
  "websocketUrl": "/realtime/unique-session-id",
  "message": "Connect to the WebSocket endpoint to start real-time transcription"
}
```

### 3. analyze_voice_sentiment
Perform detailed sentiment and political bias analysis on transcribed text.

**Input Schema:**
```json
{
  "text": "Text to analyze for sentiment and political content",
  "includeFactChecking": false
}
```

**Response:**
```json
{
  "sentiment": {
    "score": 3,
    "comparative": 0.5,
    "classification": "positive"
  },
  "politicalCategories": {
    "governance": ["policy", "government"],
    "economy": ["budget", "tax"]
  },
  "entities": {
    "people": ["President Biden"],
    "organizations": ["White House"],
    "topics": ["healthcare", "infrastructure"]
  },
  "enhancedAnalysis": "This statement shows a positive stance toward infrastructure spending...",
  "factCheckingSuggestions": "Claims about infrastructure spending should be verified..."
}
```

### 4. get_voice_processing_history
Retrieve historical voice processing results with filtering options.

**Input Schema:**
```json
{
  "userId": "user-id",
  "dateFrom": "2024-01-01T00:00:00Z",
  "dateTo": "2024-01-31T23:59:59Z",
  "language": "en",
  "minConfidence": 0.8,
  "limit": 50
}
```

### 5. voice_processing_analytics
Get analytics and statistics about voice processing usage and accuracy.

**Input Schema:**
```json
{
  "timeRange": "1h|24h|7d|30d",
  "groupBy": "hour|day|language|user"
}
```

**Response:**
```json
{
  "timeRange": "24h",
  "statistics": {
    "total_processed": 125,
    "avg_confidence": 0.92,
    "avg_duration": 45.3,
    "languages_detected": 3
  },
  "languageDistribution": [
    { "language": "en", "count": 98 },
    { "language": "es", "count": 20 },
    { "language": "fr", "count": 7 }
  ]
}
```

## WebSocket Real-Time Transcription

### Connection
```javascript
const socket = io('http://localhost:8092');

// Join a session
socket.emit('join-session', {
  sessionId: 'your-session-id',
  token: 'your-jwt-token'
});

// Listen for session confirmation
socket.on('session-joined', (data) => {
  console.log('Ready for transcription:', data);
});

// Send audio chunks
socket.emit('audio-chunk', audioBuffer);

// Receive transcription results
socket.on('transcription-chunk', (data) => {
  console.log('Transcription:', data.text);
  console.log('Confidence:', data.confidence);
});
```

### WebSocket Events

**Client to Server:**
- `join-session` - Join a transcription session
- `audio-chunk` - Send audio data for processing
- `disconnect` - Close connection

**Server to Client:**
- `session-joined` - Confirm session join
- `transcription-chunk` - Real-time transcription results
- `error` - Error messages

## Political Content Analysis

### Supported Categories
- **Governance**: Government, policy, regulation, law, legislation
- **Economy**: Budget, tax, fiscal policy, GDP, inflation
- **International**: Foreign policy, diplomacy, trade, treaties
- **Social**: Healthcare, education, welfare, civil rights
- **Security**: Defense, military, homeland security
- **Environment**: Climate, energy, sustainability

### Analysis Pipeline
1. **Sentiment Analysis**: Emotional tone and polarity
2. **Keyword Detection**: Category-specific political terms
3. **Entity Recognition**: People, places, organizations using NLP
4. **AI Enhancement**: GPT-4 political discourse analysis
5. **Confidence Scoring**: Reliability metrics for each analysis

## API Endpoints

### Authentication
- `POST /auth/login` - User authentication

### Health & Monitoring
- `GET /health` - Service health check
- `GET /metrics` - Prometheus metrics

### MCP Tool Endpoint
- `POST /mcp/call` - Execute MCP tools (requires authentication)

## Database Schema

### voice_processing_results
Stores all voice processing results:
```sql
CREATE TABLE voice_processing_results (
  id UUID PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  transcription_text TEXT NOT NULL,
  confidence_score FLOAT,
  language VARCHAR(10),
  duration FLOAT,
  political_analysis JSONB,
  entities JSONB,
  sentiment_analysis JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  metadata JSONB
);
```

## Performance Metrics

The server tracks comprehensive metrics:
- **Processing Counters**: Total requests by type, status, and language
- **Duration Histograms**: Processing time distribution
- **Active Connections**: Real-time WebSocket connections
- **Political Content**: Detected content by category and sentiment
- **Transcription Accuracy**: Confidence score distributions

## Security Features

### Rate Limiting
- **General**: 1000 requests/15min
- **Voice Upload**: 50 uploads/15min
- **Real-time**: 200 requests/1min

### Authentication
- JWT-based authentication with configurable expiry
- Session management with Redis
- Secure token validation on all MCP endpoints

### File Upload Security
- File type validation (audio formats only)
- Size limits (25MB per file, 5 files max)
- Temporary file cleanup
- Malicious file detection

## Troubleshooting

### Common Issues

1. **FFmpeg Not Found**
   ```bash
   # Install FFmpeg
   apt-get update && apt-get install -y ffmpeg
   ```

2. **Audio Processing Timeout**
   - Check audio file size and duration
   - Verify FFmpeg installation
   - Monitor server resource usage

3. **WebSocket Connection Issues**
   - Verify JWT token validity
   - Check session ID existence in Redis
   - Ensure WebSocket support in client

4. **Transcription Accuracy Issues**
   - Improve audio quality (reduce background noise)
   - Use appropriate language setting
   - Provide context prompts for better accuracy

### Debug Mode
Enable detailed logging:
```bash
LOG_LEVEL=debug npm start
```

## Monitoring

### Key Metrics
- `voice_processing_total` - Processing requests by type/status
- `voice_processing_duration_seconds` - Processing time histograms
- `realtime_voice_connections` - Active WebSocket connections
- `political_content_detected_total` - Political content detection
- `transcription_accuracy_score` - Accuracy distribution

### Health Checks
```bash
curl http://localhost:8092/health
```

## Best Practices

1. **Audio Quality**: Use high-quality audio (16kHz+, minimal background noise)
2. **Language Detection**: Specify language when known for better accuracy
3. **Context Prompts**: Provide context for political/technical terminology
4. **Batch Processing**: Process multiple files efficiently
5. **Security**: Regularly rotate JWT secrets and admin credentials

## License

This MCP server is part of the political document processing system. See the main project license for details.