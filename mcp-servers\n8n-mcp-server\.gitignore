# Dependency directories
node_modules/
coverage/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# macOS
.DS_Store

# Misc
.npm
.eslintcache
.yarn-integrity

# CRCT System
cline_docs/
strategy_tasks/
Previous_versions/
--output
test-output-summary.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/
env/
.env/
.venv/
*.egg-info/
dist/
build/

# Embeddings
*.embedding
embeddings/
mcp-config.json

# chat logs folder
/n8n_workflow_windows\claude-code-chat-logs/