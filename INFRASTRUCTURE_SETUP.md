# Infrastructure Setup Instructions

## Prerequisites

1. **Start Docker Desktop**
   - Ensure Docker Desktop is running on Windows
   - You can start it from the Start menu or system tray

2. **Environment Configuration**
   - Copy `.env.example` to `.env`
   - Fill in your API keys and configuration values

## Quick Start

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your API keys
# Required: OPENAI_API_KEY, ANTHROPIC_API_KEY, PERPLEXITY_API_KEY

# Start the infrastructure
docker-compose up -d

# Check services are running
docker-compose ps

# View logs
docker-compose logs -f
```

## Service URLs

Once running, the services will be available at:

- **n8n Workflow Engine**: http://localhost:5678
- **Chat Interface**: http://localhost:3001
- **ChromaDB**: http://localhost:8000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **Grafana Monitoring**: http://localhost:3000

## Initial Setup

1. **Access n8n**: Go to http://localhost:5678 and create admin account
2. **Import Workflows**: Use the workflows in `/workflows` directory
3. **Configure MCP Servers**: Follow the MCP setup guide below

## Troubleshooting

- If services fail to start, check Docker Desktop is running
- View logs with `docker-compose logs [service-name]`
- Reset everything with `docker-compose down -v && docker-compose up -d`