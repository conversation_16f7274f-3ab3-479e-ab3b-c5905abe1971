{"name": "document-processing-mcp-server", "version": "1.0.0", "description": "MCP server for advanced document processing and format conversion using CloudConvert", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "axios": "^1.6.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "pg": "^8.11.3", "redis": "^4.6.10", "form-data": "^4.0.0", "mime-types": "^2.1.35", "cloudconvert": "^2.2.0", "marked": "^9.1.6", "puppeteer": "^21.5.0", "jsdom": "^23.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}