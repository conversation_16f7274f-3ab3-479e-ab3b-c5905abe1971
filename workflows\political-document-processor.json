{"name": "Political Document Processor", "active": true, "nodes": [{"parameters": {"triggerOn": "webhookReceived", "httpMethod": "POST", "path": "process-document", "options": {}}, "id": "webhook-trigger", "name": "Document Processing Webhook", "type": "n8n-nodes-base.webhook", "position": [240, 300], "webhookId": "political-doc-processor"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO document_processing_jobs (job_id, filename, file_path, document_type, processing_status, task_type, manifesto_category, token_tier, processing_start, processing_metadata) VALUES ($1, $2, $3, $4, 'pending', $5, $6, $7, CURRENT_TIMESTAMP, $8)", "additionalFields": {"queryParameters": "={{ [$node[\"Generate Job ID\"].json.jobId, $json.body.filename, $json.body.filePath, $json.body.documentType, $json.body.taskType, $json.body.category, $json.body.tokenTier, JSON.stringify($json.body.metadata || {})] }}"}}, "id": "create-job-record", "name": "Create Job Record", "type": "n8n-nodes-base.postgres", "position": [660, 300], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"functionCode": "// Generate unique job ID\nconst timestamp = Date.now();\nconst random = Math.random().toString(36).substr(2, 9);\nconst jobId = `job_${timestamp}_${random}`;\n\nreturn {\n  jobId,\n  timestamp,\n  requestData: $input.all()[0].json.body\n};"}, "id": "generate-job-id", "name": "Generate Job ID", "type": "n8n-nodes-base.code", "position": [450, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "task-type-switch", "leftValue": "={{ $json.requestData.taskType }}", "rightValue": "generate_whitepaper", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "task-type-router", "name": "Task Type Router", "type": "n8n-nodes-base.switch", "position": [870, 300]}, {"parameters": {"server": "manifesto-context", "tool": "get_context_by_tier", "arguments": {"tier": "={{ $json.requestData.tokenTier }}", "category": "={{ $json.requestData.category }}", "include_voice_guide": true}}, "id": "get-manifesto-context", "name": "Get Manifesto Context", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1080, 200]}, {"parameters": {"server": "research-integration", "tool": "research_topic", "arguments": {"topic": "={{ $json.requestData.topic }}", "research_depth": "={{ $json.requestData.researchLevel || 'standard' }}", "focus_areas": "={{ $json.requestData.focusAreas || ['policy_examples', 'international_comparisons'] }}", "time_frame": "both"}}, "id": "research-topic", "name": "Research Topic", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1280, 200]}, {"parameters": {"server": "political-content", "tool": "generate_white_paper", "arguments": {"topic": "={{ $json.requestData.topic }}", "category": "={{ $json.requestData.category }}", "token_tier": "={{ $json.requestData.tokenTier }}", "research_level": "={{ $json.requestData.researchLevel || 'comprehensive' }}", "output_format": "={{ $json.requestData.outputFormat || 'markdown' }}"}, "options": {"timeout": 120000}}, "id": "generate-whitepaper", "name": "Generate White Paper", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1480, 200]}, {"parameters": {"server": "quality-control", "tool": "review_document", "arguments": {"document_id": "={{ $node[\"Generate White Paper\"].json.jobId }}", "review_type": "full", "strict_mode": true}}, "id": "quality-review", "name": "Quality Review", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1680, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "quality-approved", "leftValue": "={{ $json.approved }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "quality-gate", "name": "Quality Gate", "type": "n8n-nodes-base.switch", "position": [1880, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE document_processing_jobs SET processing_status = 'completed', processing_end = CURRENT_TIMESTAMP, output_filename = $1, output_path = $2, quality_score = $3 WHERE job_id = $4", "additionalFields": {"queryParameters": "={{ [$node[\"Generate White Paper\"].json.filename, $node[\"Generate White Paper\"].json.filePath, $node[\"Quality Review\"].json.scores.overall_quality_score, $node[\"Generate Job ID\"].json.jobId] }}"}}, "id": "complete-job-success", "name": "Complete Job - Success", "type": "n8n-nodes-base.postgres", "position": [2080, 120], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"server": "voice-generation", "tool": "generate_speech_version", "arguments": {"document_id": "={{ $node[\"Generate White Paper\"].json.jobId }}", "speech_style": "passionate_speech", "target_length": "10-15 minutes"}}, "id": "generate-speech-version", "name": "Generate Speech Version", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [2080, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE document_processing_jobs SET processing_status = 'failed', processing_end = CURRENT_TIMESTAMP, error_message = $1 WHERE job_id = $2", "additionalFields": {"queryParameters": "={{ [$node[\"Quality Review\"].json.feedback, $node[\"Generate Job ID\"].json.jobId] }}"}}, "id": "complete-job-failed", "name": "Complete Job - Failed QC", "type": "n8n-nodes-base.postgres", "position": [2080, 280], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"server": "conversation-memory", "tool": "store_interaction", "arguments": {"interaction_type": "document_generation", "document_id": "={{ $node[\"Generate White Paper\"].json.jobId }}", "user_request": "={{ $json.requestData }}", "system_response": "={{ $node[\"Generate White Paper\"].json }}", "quality_scores": "={{ $node[\"Quality Review\"].json.scores }}"}}, "id": "store-interaction", "name": "Store Interaction", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [2280, 200]}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"jobId\": \"{{ $node[\"Generate Job ID\"].json.jobId }}\",\n  \"status\": \"completed\",\n  \"document\": {\n    \"filename\": \"{{ $node[\"Generate White Paper\"].json.filename }}\",\n    \"path\": \"{{ $node[\"Generate White Paper\"].json.filePath }}\",\n    \"tokens\": {{ $node[\"Generate White Paper\"].json.tokens }},\n    \"category\": \"{{ $node[\"Generate White Paper\"].json.category }}\",\n    \"topic\": \"{{ $node[\"Generate White Paper\"].json.topic }}\"\n  },\n  \"quality\": {{ $node[\"Quality Review\"].json.scores | dump }},\n  \"speechVersion\": {\n    \"filename\": \"{{ $node[\"Generate Speech Version\"].json.filename }}\",\n    \"duration\": \"{{ $node[\"Generate Speech Version\"].json.estimatedDuration }}\"\n  },\n  \"timestamp\": \"{{ $node[\"Generate Job ID\"].json.timestamp }}\"\n}", "options": {}}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "position": [2480, 200]}, {"parameters": {"server": "political-content", "tool": "edit_document", "arguments": {"document_id": "={{ $json.requestData.documentId }}", "edit_instructions": "={{ $json.requestData.editInstructions }}", "preserve_voice": true, "token_tier": "={{ $json.requestData.tokenTier || 2 }}"}}, "id": "edit-document", "name": "Edit Document", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1080, 400]}, {"parameters": {"server": "political-content", "tool": "combine_documents", "arguments": {"document_ids": "={{ $json.requestData.documentIds }}", "combination_strategy": "={{ $json.requestData.strategy || 'thematic' }}", "output_title": "={{ $json.requestData.outputTitle }}", "category": "={{ $json.requestData.category }}"}}, "id": "combine-documents", "name": "Combine Documents", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1080, 500]}, {"parameters": {"server": "political-content", "tool": "generate_policy_brief", "arguments": {"policy_topic": "={{ $json.requestData.topic }}", "target_audience": "={{ $json.requestData.audience || 'legislators' }}", "urgency_level": "={{ $json.requestData.urgency || 'important' }}", "length": "={{ $json.requestData.length || 'medium' }}"}}, "id": "generate-policy-brief", "name": "Generate Policy Brief", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1080, 600]}, {"parameters": {"functionCode": "// Error handling and logging\nconst error = $input.all()[0].json.error || 'Unknown error occurred';\nconst jobId = $node[\"Generate Job ID\"].json.jobId;\n\n// Log error to system\nconsole.error(`Job ${jobId} failed:`, error);\n\nreturn {\n  jobId,\n  error,\n  timestamp: new Date().toISOString(),\n  status: 'failed'\n};"}, "id": "error-handler", "name": "<PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.code", "position": [2080, 400]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE document_processing_jobs SET processing_status = 'failed', processing_end = CURRENT_TIMESTAMP, error_message = $1 WHERE job_id = $2", "additionalFields": {"queryParameters": "={{ [$node[\"Error Handler\"].json.error, $node[\"Error Handler\"].json.jobId] }}"}}, "id": "log-error", "name": "Log <PERSON>r", "type": "n8n-nodes-base.postgres", "position": [2280, 400], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": false,\n  \"jobId\": \"{{ $node[\"Error Handler\"].json.jobId }}\",\n  \"status\": \"failed\",\n  \"error\": \"{{ $node[\"Erro<PERSON> Handler\"].json.error }}\",\n  \"timestamp\": \"{{ $node[\"Error Handler\"].json.timestamp }}\"\n}", "options": {}}, "id": "error-response", "name": "Error Response", "type": "n8n-nodes-base.respondToWebhook", "position": [2480, 400]}], "connections": {"Document Processing Webhook": {"main": [[{"node": "Generate Job ID", "type": "main", "index": 0}]]}, "Generate Job ID": {"main": [[{"node": "Create Job Record", "type": "main", "index": 0}]]}, "Create Job Record": {"main": [[{"node": "Task Type Router", "type": "main", "index": 0}]]}, "Task Type Router": {"main": [[{"node": "Get Manifesto Context", "type": "main", "index": 0}], [{"node": "Edit Document", "type": "main", "index": 0}], [{"node": "Combine Documents", "type": "main", "index": 0}], [{"node": "Generate Policy Brief", "type": "main", "index": 0}]]}, "Get Manifesto Context": {"main": [[{"node": "Research Topic", "type": "main", "index": 0}]]}, "Research Topic": {"main": [[{"node": "Generate White Paper", "type": "main", "index": 0}]]}, "Generate White Paper": {"main": [[{"node": "Quality Review", "type": "main", "index": 0}]]}, "Quality Review": {"main": [[{"node": "Quality Gate", "type": "main", "index": 0}]]}, "Quality Gate": {"main": [[{"node": "Complete Job - Success", "type": "main", "index": 0}, {"node": "Generate Speech Version", "type": "main", "index": 0}], [{"node": "Complete Job - Failed QC", "type": "main", "index": 0}]]}, "Generate Speech Version": {"main": [[{"node": "Store Interaction", "type": "main", "index": 0}]]}, "Store Interaction": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Edit Document": {"main": [[{"node": "Quality Review", "type": "main", "index": 0}]]}, "Combine Documents": {"main": [[{"node": "Quality Review", "type": "main", "index": 0}]]}, "Generate Policy Brief": {"main": [[{"node": "Quality Review", "type": "main", "index": 0}]]}, "Error Handler": {"main": [[{"node": "Log <PERSON>r", "type": "main", "index": 0}]]}, "Log Error": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}}, "createdAt": "2025-07-17T12:00:00.000Z", "updatedAt": "2025-07-17T12:00:00.000Z", "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "1"}