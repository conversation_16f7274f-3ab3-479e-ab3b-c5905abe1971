#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { createClient } from 'redis';
import { Client } from 'pg';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import joi from 'joi';
import morgan from 'morgan';
import session from 'express-session';
import RedisStore from 'connect-redis';
import cron from 'node-cron';
import { promisify } from 'util';
import crypto from 'crypto';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Secure Analytics MCP Server
 * OAuth 2.1 compliant authentication with comprehensive security measures
 * Real-time document processing analytics and performance monitoring
 */

class SecureAnalyticsMCPServer {
  constructor() {
    this.server = new MCPServer({
      name: 'analytics-secure',
      version: '1.0.0'
    });

    // Initialize Express app with security middleware
    this.app = express();
    this.setupSecurity();
    this.setupLogging();
    this.setupValidation();
    
    // Initialize database clients
    this.redisClient = null;
    this.pgClient = null;
    
    // OAuth 2.1 configuration
    this.oauthConfig = {
      issuer: process.env.OAUTH_ISSUER || 'https://analytics.political-system.local',
      clientId: process.env.OAUTH_CLIENT_ID || 'analytics-mcp-client',
      clientSecret: process.env.OAUTH_CLIENT_SECRET,
      redirectUri: process.env.OAUTH_REDIRECT_URI || 'http://localhost:8090/auth/callback',
      scopes: ['analytics:read', 'analytics:write', 'analytics:admin'],
      tokenExpiry: parseInt(process.env.TOKEN_EXPIRY) || 3600, // 1 hour
      refreshTokenExpiry: parseInt(process.env.REFRESH_TOKEN_EXPIRY) || 604800 // 1 week
    };

    // Security configuration
    this.securityConfig = {
      jwtSecret: process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex'),
      saltRounds: 12,
      maxLoginAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      sessionSecret: process.env.SESSION_SECRET || crypto.randomBytes(64).toString('hex')
    };

    // Analytics configuration
    this.analyticsConfig = {
      retentionDays: parseInt(process.env.ANALYTICS_RETENTION_DAYS) || 90,
      aggregationInterval: parseInt(process.env.AGGREGATION_INTERVAL) || 300000, // 5 minutes
      metricsBuffer: new Map(),
      realTimeMetrics: new Map()
    };

    this.setupMCPTools();
    this.setupExpressRoutes();
    this.setupPerformanceMetrics();
  }

  setupSecurity() {
    // Helmet for security headers
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001', 'http://localhost:5678'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization', 'MCP-Protocol-Version'],
      maxAge: 86400 // 24 hours
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting with sliding window
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting for health checks
        return req.path === '/health';
      }
    });

    const authLimiter = rateLimit({
      windowMs: 15 * 60 * 1000,
      max: 5, // Stricter limit for auth endpoints
      message: {
        error: 'Too many authentication attempts',
        retryAfter: '15 minutes'
      }
    });

    // Speed limiting
    const speedLimiter = slowDown({
      windowMs: 15 * 60 * 1000,
      delayAfter: 50,
      delayMs: 500
    });

    this.app.use('/api/', limiter);
    this.app.use('/auth/', authLimiter);
    this.app.use(speedLimiter);

    // Body parsing with size limits
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  setupLogging() {
    // Create logger with daily rotation
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'analytics-secure-mcp' },
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        }),
        new DailyRotateFile({
          filename: '/app/logs/analytics-secure-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          auditFile: '/app/logs/audit.json'
        }),
        new DailyRotateFile({
          filename: '/app/logs/analytics-secure-error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '30d'
        })
      ]
    });

    // Security logging for authentication events
    this.securityLogger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      defaultMeta: { service: 'security' },
      transports: [
        new DailyRotateFile({
          filename: '/app/logs/security-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '90d'
        })
      ]
    });

    // HTTP request logging
    this.app.use(morgan('combined', {
      stream: {
        write: (message) => this.logger.info(message.trim())
      }
    }));
  }

  setupValidation() {
    // Common validation schemas
    this.validationSchemas = {
      oauthRequest: joi.object({
        grant_type: joi.string().valid('authorization_code', 'refresh_token').required(),
        code: joi.string().when('grant_type', { is: 'authorization_code', then: joi.required() }),
        refresh_token: joi.string().when('grant_type', { is: 'refresh_token', then: joi.required() }),
        client_id: joi.string().required(),
        client_secret: joi.string().required(),
        redirect_uri: joi.string().uri().when('grant_type', { is: 'authorization_code', then: joi.required() })
      }),
      
      metricsQuery: joi.object({
        start_time: joi.date().iso().required(),
        end_time: joi.date().iso().min(joi.ref('start_time')).required(),
        metric_types: joi.array().items(joi.string().valid(
          'document_generation', 'vector_search', 'quality_control', 
          'user_interactions', 'system_performance'
        )).min(1),
        aggregation: joi.string().valid('hourly', 'daily', 'weekly').default('hourly'),
        filters: joi.object().pattern(joi.string(), joi.alternatives().try(joi.string(), joi.number(), joi.boolean()))
      }),

      analyticsEvent: joi.object({
        event_type: joi.string().required(),
        event_data: joi.object().required(),
        timestamp: joi.date().iso().default(() => new Date()),
        user_id: joi.string().uuid().optional(),
        session_id: joi.string().uuid().optional(),
        metadata: joi.object().optional()
      })
    };
  }

  async initialize() {
    try {
      // Initialize Redis client with security
      this.redisClient = createClient({
        host: process.env.REDIS_HOST || 'redis',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        db: 2, // Use separate DB for analytics
        retry_unfulfilled_commands: true,
        retry_delay_on_cluster_down: 300,
        retry_delay_on_failover: 100,
        max_attempts: 3
      });

      this.redisClient.on('error', (err) => {
        this.logger.error('Redis connection error:', err);
      });

      await this.redisClient.connect();

      // Initialize PostgreSQL client
      this.pgClient = new Client({
        host: process.env.POSTGRES_HOST || 'postgresql',
        port: process.env.POSTGRES_PORT || 5432,
        database: process.env.POSTGRES_DB || 'political_conversations',
        user: process.env.POSTGRES_USER || 'n8n_user',
        password: process.env.POSTGRES_PASSWORD,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        connectionTimeoutMillis: 5000,
        idleTimeoutMillis: 30000,
        max: 20
      });

      await this.pgClient.connect();

      // Initialize database schema
      await this.initializeDatabase();

      // Setup session store with Redis
      this.app.use(session({
        store: new RedisStore({ client: this.redisClient }),
        secret: this.securityConfig.sessionSecret,
        resave: false,
        saveUninitialized: false,
        rolling: true,
        cookie: {
          secure: process.env.NODE_ENV === 'production',
          httpOnly: true,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
          sameSite: 'strict'
        }
      }));

      // Schedule cleanup tasks
      this.scheduleCleanupTasks();

      this.logger.info('Secure Analytics MCP Server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Secure Analytics MCP Server:', error);
      throw error;
    }
  }

  async initializeDatabase() {
    const queries = [
      // OAuth clients table
      `CREATE TABLE IF NOT EXISTS oauth_clients (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        client_id VARCHAR(255) UNIQUE NOT NULL,
        client_secret_hash TEXT NOT NULL,
        name VARCHAR(255) NOT NULL,
        redirect_uris TEXT[] NOT NULL,
        scopes TEXT[] NOT NULL,
        grant_types TEXT[] NOT NULL DEFAULT ARRAY['authorization_code', 'refresh_token'],
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // OAuth authorization codes
      `CREATE TABLE IF NOT EXISTS oauth_authorization_codes (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        code VARCHAR(255) UNIQUE NOT NULL,
        client_id VARCHAR(255) NOT NULL REFERENCES oauth_clients(client_id),
        user_id UUID,
        redirect_uri TEXT NOT NULL,
        scopes TEXT[] NOT NULL,
        code_challenge VARCHAR(255),
        code_challenge_method VARCHAR(10),
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // OAuth access tokens
      `CREATE TABLE IF NOT EXISTS oauth_access_tokens (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        token VARCHAR(255) UNIQUE NOT NULL,
        client_id VARCHAR(255) NOT NULL REFERENCES oauth_clients(client_id),
        user_id UUID,
        scopes TEXT[] NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // OAuth refresh tokens
      `CREATE TABLE IF NOT EXISTS oauth_refresh_tokens (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        token VARCHAR(255) UNIQUE NOT NULL,
        access_token_id UUID NOT NULL REFERENCES oauth_access_tokens(id) ON DELETE CASCADE,
        client_id VARCHAR(255) NOT NULL REFERENCES oauth_clients(client_id),
        user_id UUID,
        scopes TEXT[] NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Analytics events table with partitioning
      `CREATE TABLE IF NOT EXISTS analytics_events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_type VARCHAR(100) NOT NULL,
        event_data JSONB NOT NULL,
        timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        user_id UUID,
        session_id UUID,
        client_id VARCHAR(255),
        ip_address INET,
        user_agent TEXT,
        metadata JSONB,
        processed BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) PARTITION BY RANGE (timestamp)`,

      // Analytics aggregated metrics
      `CREATE TABLE IF NOT EXISTS analytics_metrics (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        metric_type VARCHAR(100) NOT NULL,
        metric_name VARCHAR(255) NOT NULL,
        metric_value NUMERIC NOT NULL,
        dimensions JSONB,
        timestamp TIMESTAMP NOT NULL,
        aggregation_period VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Security audit log
      `CREATE TABLE IF NOT EXISTS security_audit_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_type VARCHAR(100) NOT NULL,
        user_id UUID,
        client_id VARCHAR(255),
        ip_address INET,
        user_agent TEXT,
        success BOOLEAN NOT NULL,
        error_message TEXT,
        additional_data JSONB,
        timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      )`,

      // Failed login attempts tracking
      `CREATE TABLE IF NOT EXISTS failed_login_attempts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        ip_address INET NOT NULL,
        user_identifier VARCHAR(255),
        attempt_count INTEGER DEFAULT 1,
        first_attempt_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_attempt_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        locked_until TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const query of queries) {
      await this.pgClient.query(query);
    }

    // Create indexes for performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_oauth_clients_client_id ON oauth_clients(client_id)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_codes_code ON oauth_authorization_codes(code)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_codes_expires ON oauth_authorization_codes(expires_at)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_tokens_token ON oauth_access_tokens(token)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires ON oauth_access_tokens(expires_at)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_events_type ON analytics_events(event_type)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_events_user ON analytics_events(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_events_session ON analytics_events(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_metrics_type ON analytics_metrics(metric_type, metric_name)',
      'CREATE INDEX IF NOT EXISTS idx_analytics_metrics_timestamp ON analytics_metrics(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_security_audit_type ON security_audit_log(event_type)',
      'CREATE INDEX IF NOT EXISTS idx_security_audit_timestamp ON security_audit_log(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_failed_login_ip ON failed_login_attempts(ip_address)',
      'CREATE INDEX IF NOT EXISTS idx_failed_login_locked ON failed_login_attempts(locked_until)'
    ];

    for (const index of indexes) {
      await this.pgClient.query(index);
    }

    // Initialize default OAuth client if not exists
    await this.createDefaultOAuthClient();

    this.logger.info('Database schema initialized successfully');
  }

  async createDefaultOAuthClient() {
    const clientSecret = process.env.OAUTH_CLIENT_SECRET || crypto.randomBytes(32).toString('hex');
    const hashedSecret = await bcrypt.hash(clientSecret, this.securityConfig.saltRounds);

    try {
      await this.pgClient.query(
        `INSERT INTO oauth_clients (client_id, client_secret_hash, name, redirect_uris, scopes, grant_types)
         VALUES ($1, $2, $3, $4, $5, $6)
         ON CONFLICT (client_id) DO NOTHING`,
        [
          this.oauthConfig.clientId,
          hashedSecret,
          'Analytics MCP Client',
          [this.oauthConfig.redirectUri],
          this.oauthConfig.scopes,
          ['authorization_code', 'refresh_token']
        ]
      );

      if (!process.env.OAUTH_CLIENT_SECRET) {
        this.logger.warn(`Generated OAuth client secret: ${clientSecret}`);
        this.logger.warn('Please set OAUTH_CLIENT_SECRET environment variable');
      }
    } catch (error) {
      this.logger.error('Failed to create default OAuth client:', error);
    }
  }

  setupMCPTools() {
    // Tool: Get Analytics Dashboard Data
    this.server.addTool({
      name: 'get_analytics_dashboard',
      description: 'Retrieve comprehensive analytics dashboard data with real-time metrics',
      inputSchema: {
        type: 'object',
        properties: {
          dashboard_type: {
            type: 'string',
            enum: ['overview', 'performance', 'usage', 'quality', 'security'],
            description: 'Type of dashboard to retrieve'
          },
          time_range: {
            type: 'string',
            enum: ['1h', '24h', '7d', '30d', '90d'],
            default: '24h',
            description: 'Time range for analytics data'
          },
          real_time: {
            type: 'boolean',
            default: true,
            description: 'Include real-time metrics'
          }
        },
        required: ['dashboard_type']
      }
    }, this.getAnalyticsDashboard.bind(this));

    // Tool: Query Document Generation Metrics
    this.server.addTool({
      name: 'query_document_metrics',
      description: 'Query detailed document generation performance and quality metrics',
      inputSchema: {
        type: 'object',
        properties: {
          start_time: {
            type: 'string',
            format: 'date-time',
            description: 'Start time for metrics query (ISO 8601)'
          },
          end_time: {
            type: 'string',
            format: 'date-time',
            description: 'End time for metrics query (ISO 8601)'
          },
          metric_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['generation_time', 'quality_score', 'token_usage', 'user_satisfaction', 'error_rate']
            },
            description: 'Types of metrics to retrieve'
          },
          filters: {
            type: 'object',
            properties: {
              document_type: { type: 'string' },
              category: { type: 'string' },
              token_tier: { type: 'number' },
              user_id: { type: 'string' }
            },
            description: 'Filters to apply to the metrics query'
          },
          aggregation: {
            type: 'string',
            enum: ['raw', 'hourly', 'daily', 'weekly'],
            default: 'hourly',
            description: 'Data aggregation level'
          }
        },
        required: ['start_time', 'end_time']
      }
    }, this.queryDocumentMetrics.bind(this));

    // Tool: Get System Performance Analytics
    this.server.addTool({
      name: 'get_system_performance',
      description: 'Retrieve system performance analytics including resource usage and response times',
      inputSchema: {
        type: 'object',
        properties: {
          services: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['n8n', 'mcp-servers', 'vector-search', 'database', 'redis', 'all']
            },
            default: ['all'],
            description: 'Services to analyze'
          },
          metrics: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['cpu', 'memory', 'response_time', 'throughput', 'error_rate', 'availability']
            },
            default: ['response_time', 'throughput', 'error_rate'],
            description: 'Performance metrics to retrieve'
          },
          time_range: {
            type: 'string',
            enum: ['1h', '6h', '24h', '7d'],
            default: '1h',
            description: 'Time range for performance data'
          }
        }
      }
    }, this.getSystemPerformance.bind(this));

    // Tool: Analyze User Behavior
    this.server.addTool({
      name: 'analyze_user_behavior',
      description: 'Analyze user interaction patterns and document usage trends',
      inputSchema: {
        type: 'object',
        properties: {
          analysis_type: {
            type: 'string',
            enum: ['usage_patterns', 'document_preferences', 'session_analysis', 'conversion_funnel'],
            description: 'Type of user behavior analysis'
          },
          user_segment: {
            type: 'string',
            enum: ['all', 'new_users', 'returning_users', 'power_users'],
            default: 'all',
            description: 'User segment to analyze'
          },
          time_period: {
            type: 'string',
            enum: ['7d', '30d', '90d'],
            default: '30d',
            description: 'Time period for analysis'
          }
        },
        required: ['analysis_type']
      }
    }, this.analyzeUserBehavior.bind(this));

    // Tool: Generate Analytics Report
    this.server.addTool({
      name: 'generate_analytics_report',
      description: 'Generate comprehensive analytics reports with insights and recommendations',
      inputSchema: {
        type: 'object',
        properties: {
          report_type: {
            type: 'string',
            enum: ['executive_summary', 'performance_report', 'usage_analysis', 'quality_assessment', 'security_audit'],
            description: 'Type of report to generate'
          },
          period: {
            type: 'string',
            enum: ['daily', 'weekly', 'monthly', 'quarterly'],
            description: 'Reporting period'
          },
          format: {
            type: 'string',
            enum: ['json', 'markdown', 'html', 'pdf'],
            default: 'json',
            description: 'Output format for the report'
          },
          include_recommendations: {
            type: 'boolean',
            default: true,
            description: 'Include AI-generated insights and recommendations'
          }
        },
        required: ['report_type', 'period']
      }
    }, this.generateAnalyticsReport.bind(this));

    // Tool: Track Real-time Event
    this.server.addTool({
      name: 'track_realtime_event',
      description: 'Track real-time analytics events for immediate processing and alerting',
      inputSchema: {
        type: 'object',
        properties: {
          event_type: {
            type: 'string',
            enum: ['document_generated', 'quality_review_completed', 'user_action', 'system_event', 'error_occurred'],
            description: 'Type of event to track'
          },
          event_data: {
            type: 'object',
            description: 'Event-specific data payload'
          },
          priority: {
            type: 'string',
            enum: ['low', 'medium', 'high', 'critical'],
            default: 'medium',
            description: 'Event priority for processing'
          },
          tags: {
            type: 'array',
            items: { type: 'string' },
            description: 'Tags for event categorization and filtering'
          }
        },
        required: ['event_type', 'event_data']
      }
    }, this.trackRealtimeEvent.bind(this));
  }

  // MCP Tool Implementations
  async getAnalyticsDashboard(params) {
    try {
      const { dashboard_type, time_range = '24h', real_time = true } = params;
      
      this.logger.info(`Getting analytics dashboard: ${dashboard_type}, range: ${time_range}`);
      
      // Calculate time bounds
      const endTime = new Date();
      const startTime = new Date();
      
      switch (time_range) {
        case '1h': startTime.setHours(startTime.getHours() - 1); break;
        case '24h': startTime.setDate(startTime.getDate() - 1); break;
        case '7d': startTime.setDate(startTime.getDate() - 7); break;
        case '30d': startTime.setDate(startTime.getDate() - 30); break;
        case '90d': startTime.setDate(startTime.getDate() - 90); break;
      }

      let dashboardData = {};

      switch (dashboard_type) {
        case 'overview':
          dashboardData = await this.getOverviewDashboard(startTime, endTime, real_time);
          break;
        case 'performance':
          dashboardData = await this.getPerformanceDashboard(startTime, endTime, real_time);
          break;
        case 'usage':
          dashboardData = await this.getUsageDashboard(startTime, endTime, real_time);
          break;
        case 'quality':
          dashboardData = await this.getQualityDashboard(startTime, endTime, real_time);
          break;
        case 'security':
          dashboardData = await this.getSecurityDashboard(startTime, endTime, real_time);
          break;
        default:
          throw new Error(`Unknown dashboard type: ${dashboard_type}`);
      }

      return {
        success: true,
        dashboard_type,
        time_range,
        data: dashboardData,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error getting analytics dashboard:', error);
      throw error;
    }
  }

  async queryDocumentMetrics(params) {
    try {
      const { start_time, end_time, metric_types, filters = {}, aggregation = 'hourly' } = params;
      
      this.logger.info(`Querying document metrics from ${start_time} to ${end_time}`);
      
      // Validate time range
      const startDate = new Date(start_time);
      const endDate = new Date(end_time);
      
      if (startDate >= endDate) {
        throw new Error('Start time must be before end time');
      }

      const metrics = await this.fetchDocumentMetrics(startDate, endDate, metric_types, filters, aggregation);
      
      return {
        success: true,
        query: { start_time, end_time, metric_types, filters, aggregation },
        metrics,
        total_records: metrics.length,
        queried_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error querying document metrics:', error);
      throw error;
    }
  }

  async getSystemPerformance(params) {
    try {
      const { services = ['all'], metrics = ['response_time', 'throughput', 'error_rate'], time_range = '1h' } = params;
      
      this.logger.info(`Getting system performance for services: ${services.join(', ')}`);
      
      const performance = await this.fetchSystemPerformance(services, metrics, time_range);
      
      return {
        success: true,
        services,
        metrics,
        time_range,
        performance,
        retrieved_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error getting system performance:', error);
      throw error;
    }
  }

  async analyzeUserBehavior(params) {
    try {
      const { analysis_type, user_segment = 'all', time_period = '30d' } = params;
      
      this.logger.info(`Analyzing user behavior: ${analysis_type} for ${user_segment} over ${time_period}`);
      
      const analysis = await this.performUserBehaviorAnalysis(analysis_type, user_segment, time_period);
      
      return {
        success: true,
        analysis_type,
        user_segment,
        time_period,
        analysis,
        analyzed_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error analyzing user behavior:', error);
      throw error;
    }
  }

  async generateAnalyticsReport(params) {
    try {
      const { report_type, period, format = 'json', include_recommendations = true } = params;
      
      this.logger.info(`Generating ${report_type} report for ${period} in ${format} format`);
      
      const report = await this.createAnalyticsReport(report_type, period, format, include_recommendations);
      
      return {
        success: true,
        report_type,
        period,
        format,
        report,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error generating analytics report:', error);
      throw error;
    }
  }

  async trackRealtimeEvent(params) {
    try {
      const { event_type, event_data, priority = 'medium', tags = [] } = params;
      
      // Validate event data
      const { error } = this.validationSchemas.analyticsEvent.validate({
        event_type,
        event_data,
        timestamp: new Date()
      });
      
      if (error) {
        throw new Error(`Invalid event data: ${error.details[0].message}`);
      }

      const eventId = uuidv4();
      const timestamp = new Date();

      // Store in database
      await this.pgClient.query(
        `INSERT INTO analytics_events (id, event_type, event_data, timestamp, metadata)
         VALUES ($1, $2, $3, $4, $5)`,
        [eventId, event_type, event_data, timestamp, { priority, tags }]
      );

      // Store in real-time metrics buffer
      const metricKey = `realtime:${event_type}:${timestamp.toISOString().substring(0, 16)}`;
      this.analyticsConfig.realTimeMetrics.set(metricKey, {
        event_type,
        count: (this.analyticsConfig.realTimeMetrics.get(metricKey)?.count || 0) + 1,
        priority,
        tags,
        timestamp
      });

      // Trigger real-time processing if high/critical priority
      if (['high', 'critical'].includes(priority)) {
        await this.processHighPriorityEvent(eventId, event_type, event_data, priority);
      }

      this.logger.info(`Tracked real-time event: ${event_type} (ID: ${eventId})`);
      
      return {
        success: true,
        event_id: eventId,
        event_type,
        priority,
        tracked_at: timestamp.toISOString()
      };
    } catch (error) {
      this.logger.error('Error tracking real-time event:', error);
      throw error;
    }
  }

  // Helper method implementations
  async getOverviewDashboard(startTime, endTime, realTime) {
    // Implementation for overview dashboard
    const overview = {
      summary: await this.getDashboardSummary(startTime, endTime),
      key_metrics: await this.getKeyMetrics(startTime, endTime),
      trends: await this.getTrendAnalysis(startTime, endTime)
    };

    if (realTime) {
      overview.real_time = await this.getRealTimeMetrics();
    }

    return overview;
  }

  async getPerformanceDashboard(startTime, endTime, realTime) {
    // Implementation for performance dashboard
    return {
      response_times: await this.getResponseTimeMetrics(startTime, endTime),
      throughput: await this.getThroughputMetrics(startTime, endTime),
      error_rates: await this.getErrorRateMetrics(startTime, endTime),
      resource_usage: await this.getResourceUsageMetrics(startTime, endTime)
    };
  }

  async getUsageDashboard(startTime, endTime, realTime) {
    // Implementation for usage dashboard
    return {
      user_sessions: await this.getUserSessionMetrics(startTime, endTime),
      document_generation: await this.getDocumentGenerationMetrics(startTime, endTime),
      feature_usage: await this.getFeatureUsageMetrics(startTime, endTime),
      api_usage: await this.getAPIUsageMetrics(startTime, endTime)
    };
  }

  async getQualityDashboard(startTime, endTime, realTime) {
    // Implementation for quality dashboard
    return {
      quality_scores: await this.getQualityScoreMetrics(startTime, endTime),
      review_outcomes: await this.getReviewOutcomeMetrics(startTime, endTime),
      user_satisfaction: await this.getUserSatisfactionMetrics(startTime, endTime),
      improvement_areas: await this.getImprovementAreas(startTime, endTime)
    };
  }

  async getSecurityDashboard(startTime, endTime, realTime) {
    // Implementation for security dashboard
    return {
      auth_events: await this.getAuthenticationEvents(startTime, endTime),
      failed_attempts: await this.getFailedLoginAttempts(startTime, endTime),
      security_alerts: await this.getSecurityAlerts(startTime, endTime),
      access_patterns: await this.getAccessPatterns(startTime, endTime)
    };
  }

  // Placeholder implementations for dashboard helper methods
  async getDashboardSummary(startTime, endTime) {
    const result = await this.pgClient.query(
      `SELECT 
        COUNT(CASE WHEN event_type = 'document_generated' THEN 1 END) as documents_generated,
        COUNT(CASE WHEN event_type = 'quality_review_completed' THEN 1 END) as reviews_completed,
        COUNT(DISTINCT user_id) as active_users,
        COUNT(DISTINCT session_id) as sessions
       FROM analytics_events 
       WHERE timestamp BETWEEN $1 AND $2`,
      [startTime, endTime]
    );
    
    return result.rows[0] || {};
  }

  async getKeyMetrics(startTime, endTime) {
    // Implementation for key metrics
    return {
      total_documents: 0,
      avg_generation_time: 0,
      success_rate: 0,
      user_satisfaction: 0
    };
  }

  async getTrendAnalysis(startTime, endTime) {
    // Implementation for trend analysis
    return {
      document_trend: [],
      quality_trend: [],
      usage_trend: []
    };
  }

  async getRealTimeMetrics() {
    const metrics = {};
    for (const [key, value] of this.analyticsConfig.realTimeMetrics.entries()) {
      if (Date.now() - new Date(value.timestamp).getTime() < 300000) { // Last 5 minutes
        metrics[key] = value;
      }
    }
    return metrics;
  }

  // Implement other helper methods similarly...
  async getResponseTimeMetrics(startTime, endTime) { return {}; }
  async getThroughputMetrics(startTime, endTime) { return {}; }
  async getErrorRateMetrics(startTime, endTime) { return {}; }
  async getResourceUsageMetrics(startTime, endTime) { return {}; }
  async getUserSessionMetrics(startTime, endTime) { return {}; }
  async getDocumentGenerationMetrics(startTime, endTime) { return {}; }
  async getFeatureUsageMetrics(startTime, endTime) { return {}; }
  async getAPIUsageMetrics(startTime, endTime) { return {}; }
  async getQualityScoreMetrics(startTime, endTime) { return {}; }
  async getReviewOutcomeMetrics(startTime, endTime) { return {}; }
  async getUserSatisfactionMetrics(startTime, endTime) { return {}; }
  async getImprovementAreas(startTime, endTime) { return {}; }
  async getAuthenticationEvents(startTime, endTime) { return {}; }
  async getFailedLoginAttempts(startTime, endTime) { return {}; }
  async getSecurityAlerts(startTime, endTime) { return {}; }
  async getAccessPatterns(startTime, endTime) { return {}; }

  async fetchDocumentMetrics(startDate, endDate, metricTypes, filters, aggregation) {
    // Implementation for fetching document metrics
    return [];
  }

  async fetchSystemPerformance(services, metrics, timeRange) {
    // Implementation for fetching system performance
    return {};
  }

  async performUserBehaviorAnalysis(analysisType, userSegment, timePeriod) {
    // Implementation for user behavior analysis
    return {};
  }

  async createAnalyticsReport(reportType, period, format, includeRecommendations) {
    // Implementation for creating analytics reports
    return {};
  }

  async processHighPriorityEvent(eventId, eventType, eventData, priority) {
    // Implementation for processing high priority events
    this.logger.warn(`High priority event: ${eventType} (ID: ${eventId})`);
  }

  setupExpressRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'analytics-secure-mcp',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        uptime: process.uptime()
      });
    });

    // OAuth 2.1 endpoints will be implemented in the next part
    this.setupOAuthRoutes();
  }

  setupOAuthRoutes() {
    // OAuth 2.1 authorization endpoint
    this.app.get('/auth/authorize', async (req, res) => {
      try {
        // Implementation of OAuth 2.1 authorization endpoint
        res.json({ message: 'OAuth authorization endpoint - implementation pending' });
      } catch (error) {
        this.logger.error('OAuth authorization error:', error);
        res.status(500).json({ error: 'Authorization failed' });
      }
    });

    // OAuth 2.1 token endpoint
    this.app.post('/auth/token', async (req, res) => {
      try {
        // Implementation of OAuth 2.1 token endpoint
        res.json({ message: 'OAuth token endpoint - implementation pending' });
      } catch (error) {
        this.logger.error('OAuth token error:', error);
        res.status(500).json({ error: 'Token exchange failed' });
      }
    });
  }

  setupPerformanceMetrics() {
    // Setup Prometheus metrics collection
    this.performanceCollector = {
      httpRequestDuration: null, // Will be implemented with prometheus-client
      httpRequestTotal: null,
      systemResourceUsage: null
    };
  }

  scheduleCleanupTasks() {
    // Cleanup expired tokens every hour
    cron.schedule('0 * * * *', async () => {
      try {
        await this.cleanupExpiredTokens();
        this.logger.info('Expired tokens cleanup completed');
      } catch (error) {
        this.logger.error('Token cleanup failed:', error);
      }
    });

    // Aggregate analytics data every 5 minutes
    cron.schedule('*/5 * * * *', async () => {
      try {
        await this.aggregateAnalyticsData();
        this.logger.info('Analytics data aggregation completed');
      } catch (error) {
        this.logger.error('Analytics aggregation failed:', error);
      }
    });

    // Archive old analytics data daily
    cron.schedule('0 2 * * *', async () => {
      try {
        await this.archiveOldAnalyticsData();
        this.logger.info('Analytics data archival completed');
      } catch (error) {
        this.logger.error('Analytics archival failed:', error);
      }
    });
  }

  async cleanupExpiredTokens() {
    const now = new Date();
    
    // Delete expired authorization codes
    await this.pgClient.query(
      'DELETE FROM oauth_authorization_codes WHERE expires_at < $1',
      [now]
    );

    // Delete expired access tokens
    await this.pgClient.query(
      'DELETE FROM oauth_access_tokens WHERE expires_at < $1',
      [now]
    );

    // Delete expired refresh tokens
    await this.pgClient.query(
      'DELETE FROM oauth_refresh_tokens WHERE expires_at < $1',
      [now]
    );
  }

  async aggregateAnalyticsData() {
    // Aggregate real-time metrics into database
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    // Process unprocessed events
    const events = await this.pgClient.query(
      `SELECT * FROM analytics_events 
       WHERE processed = false AND timestamp BETWEEN $1 AND $2`,
      [fiveMinutesAgo, now]
    );

    for (const event of events.rows) {
      // Aggregate based on event type
      await this.processEventForAggregation(event);
    }

    // Mark events as processed
    await this.pgClient.query(
      `UPDATE analytics_events 
       SET processed = true 
       WHERE processed = false AND timestamp BETWEEN $1 AND $2`,
      [fiveMinutesAgo, now]
    );
  }

  async processEventForAggregation(event) {
    // Implementation for processing individual events for aggregation
    // This would create aggregated metrics based on event type and data
  }

  async archiveOldAnalyticsData() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.analyticsConfig.retentionDays);

    // Archive old events (implementation would move to archive table)
    await this.pgClient.query(
      'DELETE FROM analytics_events WHERE timestamp < $1',
      [cutoffDate]
    );

    // Archive old audit logs
    await this.pgClient.query(
      'DELETE FROM security_audit_log WHERE timestamp < $1',
      [cutoffDate]
    );
  }
}

// Initialize and start the server
const server = new SecureAnalyticsMCPServer();

async function start() {
  try {
    await server.initialize();
    await server.server.start();
    
    const port = process.env.MCP_SERVER_PORT || 8090;
    server.app.listen(port, () => {
      server.logger.info(`Secure Analytics MCP Server running on port ${port}`);
    });
    
    console.log('Secure Analytics MCP Server started successfully');
  } catch (error) {
    console.error('Failed to start Secure Analytics MCP Server:', error);
    process.exit(1);
  }
}

start();