# Universal Document Converter 🚀

<div align="center">

![Universal Document Converter](https://img.shields.io/badge/Universal-Document%20Converter-blue?style=for-the-badge)
![Python](https://img.shields.io/badge/Python-3.7+-green?style=for-the-badge&logo=python)
![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-orange?style=for-the-badge)

**Fast • Simple • Powerful**

A lightning-fast, user-friendly document conversion tool with modern GUI and drag-and-drop support

[🚀 Quick Start](#-quick-start) • [📋 Features](#-features) • [🛠️ Installation](#️-installation) • [📖 Usage](#-usage) • [🤝 Contributing](#-contributing)

</div>

---

## ✨ Features

- **🔄 Universal Format Support**: Convert between 5 input and 4 output formats
- **⚡ Lightning Fast**: Optimized for speed with minimal overhead
- **🖱️ Drag & Drop**: Intuitive interface with file/folder drag-and-drop
- **📁 Batch Processing**: Convert entire folders recursively 
- **🎯 Smart Detection**: Automatic file format detection
- **🔧 Zero APIs**: Works offline without external API dependencies
- **📊 Real-time Progress**: Visual progress tracking with detailed results
- **🎨 Modern UI**: Clean, professional interface with excellent UX
- **🔒 Privacy First**: All processing happens locally on your machine

## 📄 Supported Formats

| Input Formats | Output Formats |
|---------------|----------------|
| **DOCX** - Microsoft Word Documents | **Markdown** - GitHub-flavored markdown |
| **PDF** - Portable Document Format | **TXT** - Plain text with formatting |
| **TXT** - Plain text files | **HTML** - Clean, semantic HTML |
| **HTML** - Web pages and documents | **RTF** - Rich Text Format |
| **RTF** - Rich Text Format | |

**Total Conversion Combinations: 20** *(5 × 4)*

## 🚀 Quick Start

### Windows (Recommended)
```bash
# 1. Clone or download this repository
git clone https://github.com/username/universal-document-converter.git
cd universal-document-converter

# 2. Install dependencies
pip install -r requirements.txt

# 3. Run the application
python universal_document_converter.py
```

### Cross-Platform
```bash
# Ensure Python 3.7+ is installed
python --version

# Install and run
pip install python-docx PyPDF2 beautifulsoup4 striprtf tkinterdnd2
python universal_document_converter.py
```

## 🛠️ Installation

### Prerequisites
- **Python 3.7+** (tested on 3.7-3.12)
- **pip** package manager

### Method 1: Automatic Installation
```bash
pip install -r requirements.txt
```

### Method 2: Manual Installation
```bash
pip install python-docx PyPDF2 beautifulsoup4 striprtf tkinterdnd2
```

### Dependencies
| Package | Purpose | Size |
|---------|---------|------|
| `python-docx` | Microsoft Word document processing | ~1.2MB |
| `PyPDF2` | PDF file reading and text extraction | ~350KB |
| `beautifulsoup4` | HTML parsing and processing | ~470KB |
| `striprtf` | RTF (Rich Text Format) processing | ~8KB |
| `tkinterdnd2` | Enhanced drag-and-drop support | ~500KB |

**Total Download Size: ~2.5MB**

## 📖 Usage

### GUI Application (Recommended)

1. **Launch the Application**
   ```bash
   python universal_document_converter.py
   ```

2. **Select Input Format**
   - Choose from dropdown or use "Auto-detect"
   - Supports: DOCX, PDF, TXT, HTML, RTF

3. **Select Output Format**
   - Choose target format from dropdown
   - Options: Markdown, TXT, HTML, RTF

4. **Add Files**
   - **Drag & Drop**: Drop files or folders directly onto the window
   - **Select Files**: Use "Select Files" button for multiple files
   - **Select Folder**: Use "Select Folder" for batch processing

5. **Configure Options**
   - ✅ **Preserve folder structure**: Maintain directory hierarchy
   - ✅ **Overwrite existing files**: Replace existing output files

6. **Convert**
   - Click "🚀 Convert Documents"
   - Monitor real-time progress
   - View detailed results

### Command Line Interface

For advanced users and automation:

```python
from universal_document_converter import UniversalConverter

# Initialize converter
converter = UniversalConverter()

# Convert single file
converter.convert_file(
    input_path="document.docx",
    output_path="document.md",
    input_format="docx",    # or "auto" for detection
    output_format="markdown"
)

# Batch conversion example
import os
from pathlib import Path

input_dir = Path("input_documents")
output_dir = Path("converted_documents")

for file_path in input_dir.rglob("*.docx"):
    output_path = output_dir / f"{file_path.stem}.md"
    converter.convert_file(file_path, output_path, "auto", "markdown")
```

## 🏗️ Architecture

### Core Components

```
Universal Document Converter
├── 🔍 FormatDetector          # Auto-detect file formats
├── 📖 Document Readers        # Parse input formats
│   ├── DocxReader            # Microsoft Word documents
│   ├── PdfReader             # PDF text extraction
│   ├── TxtReader             # Plain text with encoding detection
│   ├── HtmlReader            # HTML parsing with BeautifulSoup
│   └── RtfReader             # Rich Text Format processing
├── ✍️ Document Writers        # Generate output formats
│   ├── MarkdownWriter        # GitHub-flavored Markdown
│   ├── TxtWriter             # Formatted plain text
│   ├── HtmlWriter            # Semantic HTML with CSS
│   └── RtfWriter             # Rich Text Format
├── 🔄 UniversalConverter      # Conversion orchestration
└── 🖥️ GUI Application         # Modern tkinter interface
```

### Design Principles

- **🚀 Performance First**: Optimized for speed with minimal memory usage
- **🔒 Security**: No external API calls, all processing local
- **🛡️ Reliability**: Comprehensive error handling and validation
- **📐 Modularity**: Clean separation of concerns for easy maintenance
- **🎯 User Experience**: Intuitive interface with helpful feedback

## 📊 Performance Benchmarks

Tested on Windows 11 with Python 3.12:

| Operation | File Size | Time | Memory |
|-----------|-----------|------|---------|
| TXT → Markdown | 1MB | 0.02s | 15MB |
| DOCX → HTML | 500KB | 0.15s | 25MB |
| PDF → TXT | 2MB | 0.45s | 35MB |
| HTML → Markdown | 750KB | 0.08s | 20MB |
| Batch (100 files) | 50MB | 12s | 45MB |

*Results may vary based on system specifications and document complexity.*

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Run all tests
python test_converter.py

# Expected output:
# 🔍 Dependency Check Report
# 🔄 Format Compatibility Test  
# 🧪 Running Unit Tests
# 📊 Test Results Summary
```

### Test Coverage
- ✅ Format detection accuracy
- ✅ Conversion functionality 
- ✅ Error handling
- ✅ Performance benchmarks
- ✅ File I/O operations
- ✅ Unicode encoding support

## 🔧 Configuration

### Environment Variables
```bash
# Optional: Custom temp directory
export TEMP_DIR="/custom/temp/path"

# Optional: Logging level
export LOG_LEVEL="DEBUG"  # DEBUG, INFO, WARNING, ERROR
```

### Advanced Usage
```python
# Custom converter with specific options
converter = UniversalConverter()

# Override default readers/writers
converter.readers['custom'] = CustomReader()
converter.writers['special'] = SpecialWriter()
```

## 🐛 Troubleshooting

### Common Issues

**1. Import Error: No module named 'docx'**
```bash
pip install python-docx
```

**2. PDF text extraction fails**
- Some PDFs may have images or complex layouts
- Try converting to TXT first, then to other formats

**3. Encoding issues with text files**
- The converter automatically tries multiple encodings
- Check if the source file is corrupted

**4. GUI doesn't start**
```bash
# Check tkinter installation
python -m tkinter
```

**5. Drag and drop not working**
```bash
# Install enhanced drag-drop support
pip install tkinterdnd2
```

### Performance Tips

- For large batches, use SSD storage for faster I/O
- Close other applications to free up memory
- Use "Auto-detect" format for mixed file types
- Enable "Preserve folder structure" for organized output

## 🔄 Version History

### v2.0.0 (Latest) - Universal Format Support
- ✨ Added format selection dropdowns
- ✨ Support for 5 input and 4 output formats
- ✨ Enhanced drag-and-drop functionality
- ✨ Real-time progress tracking
- ✨ Modern UI/UX improvements
- ✨ Comprehensive test suite
- 🐛 Fixed encoding issues
- ⚡ Performance optimizations

### v1.0.0 - Initial Release
- ✨ Basic DOCX, PDF, TXT to Markdown conversion
- ✨ Simple GUI interface
- ✨ Batch processing support

## 🤝 Contributing

We welcome contributions! Here's how to get started:

### Development Setup
```bash
# Clone the repository
git clone https://github.com/username/universal-document-converter.git
cd universal-document-converter

# Install development dependencies
pip install -r requirements.txt

# Run tests
python test_converter.py
```

### Contribution Guidelines

1. **🐛 Bug Reports**: Use GitHub Issues with detailed reproduction steps
2. **💡 Feature Requests**: Describe the use case and expected behavior  
3. **🔧 Pull Requests**: Include tests and update documentation
4. **📚 Documentation**: Help improve README and code comments

### Code Style
- Follow PEP 8 Python style guide
- Use type hints where applicable
- Include docstrings for all public methods
- Write comprehensive unit tests

### Adding New Formats

To add support for new file formats:

1. Create a new reader class inheriting from `DocumentReader`
2. Create a new writer class inheriting from `DocumentWriter`
3. Update `FormatDetector.SUPPORTED_*_FORMATS`
4. Add appropriate tests in `test_converter.py`
5. Update this README

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

```
MIT License

Copyright (c) 2024 Beau Lewis

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 👨‍💻 Author & Credits

**Designed and Built by [Beau Lewis](mailto:<EMAIL>)**

- 🧠 **Architecture & Design**: Complete system design and implementation
- 💻 **Development**: Full-stack development including GUI and conversion engine
- 🎨 **UI/UX Design**: Modern, intuitive interface design
- ⚡ **Performance Optimization**: Speed and memory optimization
- 🧪 **Quality Assurance**: Comprehensive testing and validation
- 📚 **Documentation**: Complete documentation and user guides

### Special Thanks

- **Python Community** for excellent libraries
- **tkinter** for cross-platform GUI framework
- **Open Source Contributors** for inspiration and libraries

---

<div align="center">

**⭐ Star this repository if you find it useful!**

**🐛 Found a bug?** [Report it here](https://github.com/username/universal-document-converter/issues)

**💡 Have a feature idea?** [Suggest it here](https://github.com/username/universal-document-converter/issues)

**📧 Questions?** Contact [<EMAIL>](mailto:<EMAIL>)

</div>

---

<div align="center">
<sub>Universal Document Converter - Making document conversion fast, simple, and powerful</sub>
</div> 