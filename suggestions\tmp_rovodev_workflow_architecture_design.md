# Political Document Processing Workflow - Architecture Design

## System Overview
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Google Drive  │    │       n8n        │    │   AI Agents     │
│   political_in  │───▶│   Workflow       │◄──▶│   via MCP       │
│   political_out │◄───│   Orchestrator   │    │   Server        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │    ChromaDB      │    │   CloudConvert  │
                       │  Vector Storage  │    │  MD → DOCX      │
                       │      (RAG)       │    │   Conversion    │
                       └──────────────────┘    └─────────────────┘
```

## Core Components

### 1. n8n Workflow Engine
- **File Monitoring**: Watch Google Drive political_in folder
- **Document Classification**: Identify instruction vs content documents
- **AI Orchestration**: Communicate with AI agents via MCP
- **Output Management**: Handle multiple document generation
- **Conversion Pipeline**: Markdown to DOCX via CloudConvert

### 2. AI Agent Integration (via n8n MCP Server)
- **Document Analysis**: Read and understand content + manifesto
- **Task Planning**: Parse instruction documents for complex workflows
- **Content Generation**: Create new documents based on analysis
- **RAG Queries**: Retrieve relevant information from ChromaDB
- **Quality Control**: Ensure consistency across outputs

### 3. ChromaDB Vector Database
- **Document Indexing**: Store all processed documents as vectors
- **Semantic Search**: Find relevant content for RAG
- **Manifesto Integration**: Your manifesto as core reference
- **Cross-Document Relationships**: Track document connections

### 4. Google Drive Integration
- **Input Monitoring**: Detect new files in political_in
- **Output Storage**: Save generated DOCX files to political_out
- **Folder Organization**: Maintain clean file structure
- **Permissions**: Handle shareable vs private access

## Workflow Types

### Type A: Simple Synthesis (5-10 documents)
```
Input Documents + Instruction → AI Analysis → Single Output → DOCX
```

### Type B: Complex Analysis (50-100+ documents)
```
Input Documents + Master Instruction → AI Planning → Multiple Outputs → Multiple DOCX
```

### Type C: Iterative Refinement
```
Draft Documents → AI Review → Revisions → Final Outputs → DOCX
```

## Document Instruction Format

### Simple Instruction Document
```markdown
# WORKFLOW_INSTRUCTION
TYPE: simple_synthesis
OUTPUT_COUNT: 1
TITLE: "Healthcare Policy Framework"
DESCRIPTION: "Synthesize key points into comprehensive policy"
WORD_COUNT: 3000-5000
TONE: professional
DEADLINE: none
SPECIAL_INSTRUCTIONS: "Focus on implementation feasibility"
```

### Complex Instruction Document
```markdown
# WORKFLOW_INSTRUCTION
TYPE: complex_analysis
OUTPUT_COUNT: 5
PROJECT_NAME: "Universal Healthcare Implementation"

## Required Outputs:
1. TITLE: "Universal Healthcare White Paper"
   DESCRIPTION: "Comprehensive policy overview"
   WORD_COUNT: 8000-10000
   
2. TITLE: "5-Year Implementation Timeline"
   DESCRIPTION: "Detailed rollout phases"
   WORD_COUNT: 4000-5000
   
3. TITLE: "Healthcare Workforce Development"
   DESCRIPTION: "Education and training requirements"
   WORD_COUNT: 3000-4000
   
4. TITLE: "Funding Models and Economics"
   DESCRIPTION: "Financial framework and cost analysis"
   WORD_COUNT: 5000-6000
   
5. TITLE: "Private to Public Transition Plan"
   DESCRIPTION: "Migration strategy and timeline"
   WORD_COUNT: 4000-5000

## Cross-References:
- Link workforce needs to education policy
- Connect funding to economic impact studies
- Reference timeline in all documents

## RAG_PRIORITIES:
- Economic data and studies
- International healthcare models
- Education system capacity
- Political feasibility studies
```

## Technical Implementation Plan

### Phase 1: Infrastructure Setup
1. Docker containers (n8n, ChromaDB, Redis)
2. Google Drive API credentials
3. CloudConvert API setup
4. n8n MCP server configuration

### Phase 2: Basic Workflow
1. File monitoring trigger
2. Document classification logic
3. Simple AI integration test
4. Basic DOCX conversion

### Phase 3: Advanced Features
1. ChromaDB integration and RAG
2. Complex instruction parsing
3. Multi-document generation
4. Cross-reference validation

### Phase 4: Optimization
1. Error handling and retries
2. Performance monitoring
3. Batch processing capabilities
4. Advanced AI prompting

## Next Steps for Design Phase

1. **Manifesto Structure**: Define your manifesto format for AI reference
2. **Instruction Templates**: Create standardized instruction formats
3. **Folder Organization**: Plan Google Drive structure
4. **AI Prompting Strategy**: Design prompts for different document types
5. **Error Handling**: Plan for various failure scenarios

Would you like me to elaborate on any of these components or move to the next design phase?