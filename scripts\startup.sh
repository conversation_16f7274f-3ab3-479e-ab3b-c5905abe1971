#!/bin/bash

# Political Document Processing System Startup Script
# This script starts all services and initializes the system

set -e

echo "🚀 Starting Political Document Processing System"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if service is healthy
check_service() {
    local service_name=$1
    local health_url=$2
    local max_retries=30
    local retry_delay=5
    
    print_status "Checking $service_name..."
    
    for i in $(seq 1 $max_retries); do
        if curl -s -f "$health_url" > /dev/null 2>&1; then
            print_status "✅ $service_name is healthy"
            return 0
        fi
        
        if [ $i -eq $max_retries ]; then
            print_error "❌ $service_name failed to start after $max_retries attempts"
            return 1
        fi
        
        echo "   Attempt $i/$max_retries - waiting ${retry_delay}s..."
        sleep $retry_delay
    done
}

# Function to wait for database
wait_for_db() {
    print_status "Waiting for PostgreSQL database..."
    
    while ! docker exec postgres-political pg_isready -U n8n_user -d n8n > /dev/null 2>&1; do
        echo "   Waiting for database..."
        sleep 2
    done
    
    print_status "✅ Database is ready"
}

# Function to wait for Redis
wait_for_redis() {
    print_status "Waiting for Redis cache..."
    
    while ! docker exec redis-political redis-cli ping > /dev/null 2>&1; do
        echo "   Waiting for Redis..."
        sleep 2
    done
    
    print_status "✅ Redis is ready"
}

# Function to wait for ChromaDB
wait_for_chromadb() {
    print_status "Waiting for ChromaDB vector database..."
    
    while ! curl -s -f "http://localhost:8000/api/v1/heartbeat" > /dev/null 2>&1; do
        echo "   Waiting for ChromaDB..."
        sleep 2
    done
    
    print_status "✅ ChromaDB is ready"
}

# Main startup sequence
main() {
    print_status "Starting Docker services..."
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        print_warning ".env file not found. Please copy .env.example to .env and configure your API keys."
        print_warning "Starting with default configuration..."
    fi
    
    # Start all services
    docker-compose up -d
    
    if [ $? -ne 0 ]; then
        print_error "Failed to start Docker services"
        exit 1
    fi
    
    print_status "Docker services started. Waiting for initialization..."
    
    # Wait for core services
    wait_for_db
    wait_for_redis
    wait_for_chromadb
    
    # Wait for all services to be healthy
    print_status "Checking service health..."
    
    # Check n8n
    check_service "n8n Workflow Engine" "http://localhost:5678/healthz"
    
    # Check MCP servers
    check_service "Vector Search MCP" "http://localhost:8089/health"
    check_service "Manifesto Context MCP" "http://localhost:8080/health"
    check_service "Political Content MCP" "http://localhost:8081/health"
    check_service "Research Integration MCP" "http://localhost:8082/health"
    check_service "Document Processing MCP" "http://localhost:8083/health"
    check_service "Quality Control MCP" "http://localhost:8084/health"
    
    # Check chat interface
    check_service "Chat Interface" "http://localhost:3001/health"
    
    print_status "All services are healthy! 🎉"
    
    # Index documents
    print_status "Starting document indexing..."
    
    cd scripts
    npm install
    
    # Wait a bit more for vector search to be fully ready
    sleep 10
    
    # Run document indexing
    VECTOR_SEARCH_URL="http://localhost:8089" node index-documents.js
    
    if [ $? -eq 0 ]; then
        print_status "✅ Document indexing completed successfully"
    else
        print_warning "⚠️  Document indexing encountered some issues, but system is ready"
    fi
    
    # Run tests
    print_status "Running system tests..."
    
    VECTOR_SEARCH_URL="http://localhost:8089" node test-vector-search.js
    
    if [ $? -eq 0 ]; then
        print_status "✅ All tests passed"
    else
        print_warning "⚠️  Some tests failed, but system is ready"
    fi
    
    cd ..
    
    print_status "🎯 Political Document Processing System is ready!"
    echo ""
    echo "📊 System URLs:"
    echo "  - n8n Workflow Engine: http://localhost:5678"
    echo "  - Chat Interface: http://localhost:3001"
    echo "  - ChromaDB: http://localhost:8000"
    echo "  - PostgreSQL: localhost:5432"
    echo "  - Redis: localhost:6379"
    echo ""
    echo "🔧 MCP Servers:"
    echo "  - Vector Search: http://localhost:8089"
    echo "  - Manifesto Context: http://localhost:8080"
    echo "  - Political Content: http://localhost:8081"
    echo "  - Research Integration: http://localhost:8082"
    echo "  - Document Processing: http://localhost:8083"
    echo "  - Quality Control: http://localhost:8084"
    echo ""
    echo "🚀 System is ready for document processing!"
    echo "📝 Use the chat interface or n8n webhooks to start generating documents."
}

# Handle script interruption
trap 'print_error "Script interrupted"; exit 1' INT

# Run main function
main "$@"