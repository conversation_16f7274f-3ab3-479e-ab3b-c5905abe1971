# Chat Interface Specifications for Political Document System

**Purpose:** Complete specifications for building a professional web-based chat interface that enables natural conversation with the political document processing system.

---

## INTERFACE OVERVIEW

### Core Functionality
- **Real-time Chat** - Instant messaging with AI agents about documents and policies
- **Session Management** - Persistent conversations with full memory
- **Document Integration** - Direct access to document content and citations
- **Visual Enhancements** - Rich formatting, citations, and document previews
- **Mobile Responsive** - Works seamlessly on all devices

### User Experience Goals
- **Natural Conversation** - Cha<PERSON> feels like talking to a knowledgeable political advisor
- **Instant Access** - Quick answers to questions about documents and policies
- **Rich Context** - Full citations, document links, and follow-up suggestions
- **Professional Appearance** - Clean, modern interface suitable for political work

---

## TECHNICAL ARCHITECTURE

### Frontend Stack
```yaml
Framework: React 18 with TypeScript
Styling: Tailwind CSS with custom political theme
State Management: Redux Toolkit with RTK Query
Real-time: WebSocket connection to n8n
Authentication: JWT tokens with session management
```

### Backend Integration
```yaml
n8n Webhook: POST /webhook/chat
Database: PostgreSQL for conversation persistence
File Storage: Google Drive API for document access
Session Management: Redis for real-time state
```

### Component Structure
```
src/
├── components/
│   ├── Chat/
│   │   ├── ChatInterface.tsx
│   │   ├── MessageList.tsx
│   │   ├── MessageInput.tsx
│   │   ├── TypingIndicator.tsx
│   │   └── SessionManager.tsx
│   ├── Documents/
│   │   ├── DocumentPreview.tsx
│   │   ├── CitationCard.tsx
│   │   ├── DocumentSearch.tsx
│   │   └── DocumentLibrary.tsx
│   ├── UI/
│   │   ├── LoadingSpinner.tsx
│   │   ├── ErrorBoundary.tsx
│   │   ├── Toast.tsx
│   │   └── Modal.tsx
│   └── Layout/
│       ├── Header.tsx
│       ├── Sidebar.tsx
│       └── Footer.tsx
├── hooks/
│   ├── useChat.ts
│   ├── useSession.ts
│   ├── useDocuments.ts
│   └── useWebSocket.ts
├── services/
│   ├── chatAPI.ts
│   ├── documentAPI.ts
│   ├── sessionAPI.ts
│   └── websocket.ts
├── store/
│   ├── chatSlice.ts
│   ├── sessionSlice.ts
│   ├── documentSlice.ts
│   └── store.ts
└── types/
    ├── chat.ts
    ├── document.ts
    └── session.ts
```

---

## USER INTERFACE DESIGN

### Main Chat Interface
```typescript
interface ChatInterfaceProps {
  sessionId: string;
  userId: string;
}

interface Message {
  id: string;
  type: 'user' | 'system';
  content: string;
  timestamp: Date;
  citations?: Citation[];
  followUpQuestions?: string[];
  confidence?: number;
  processingTime?: number;
}

interface Citation {
  document: string;
  section: string;
  quote: string;
  relevance: number;
  url?: string;
}
```

### Chat Layout
```jsx
<div className="flex h-screen bg-gray-50">
  {/* Sidebar with document library and session history */}
  <Sidebar className="w-80 bg-white border-r">
    <DocumentLibrary />
    <SessionHistory />
  </Sidebar>
  
  {/* Main chat area */}
  <div className="flex-1 flex flex-col">
    <Header />
    
    {/* Message area */}
    <div className="flex-1 overflow-y-auto p-4">
      <MessageList messages={messages} />
      <TypingIndicator visible={isTyping} />
    </div>
    
    {/* Input area */}
    <div className="border-t bg-white p-4">
      <MessageInput onSend={sendMessage} />
    </div>
  </div>
  
  {/* Document preview panel (collapsible) */}
  <DocumentPreview className="w-96 bg-white border-l" />
</div>
```

### Message Components
```jsx
// User Message
<div className="flex justify-end mb-4">
  <div className="bg-blue-600 text-white rounded-lg px-4 py-2 max-w-lg">
    <p>{message.content}</p>
    <span className="text-xs opacity-75">
      {formatTime(message.timestamp)}
    </span>
  </div>
</div>

// System Response
<div className="flex justify-start mb-4">
  <div className="bg-white border rounded-lg px-4 py-3 max-w-2xl">
    <div className="prose prose-sm">
      <ReactMarkdown>{message.content}</ReactMarkdown>
    </div>
    
    {/* Citations */}
    {message.citations && (
      <div className="mt-3 border-t pt-3">
        <h4 className="text-sm font-medium mb-2">Sources:</h4>
        {message.citations.map(citation => (
          <CitationCard key={citation.document} citation={citation} />
        ))}
      </div>
    )}
    
    {/* Follow-up questions */}
    {message.followUpQuestions && (
      <div className="mt-3 border-t pt-3">
        <h4 className="text-sm font-medium mb-2">Related questions:</h4>
        <div className="flex flex-wrap gap-2">
          {message.followUpQuestions.map(question => (
            <button 
              key={question}
              onClick={() => sendMessage(question)}
              className="text-sm bg-gray-100 hover:bg-gray-200 rounded px-3 py-1"
            >
              {question}
            </button>
          ))}
        </div>
      </div>
    )}
    
    {/* Metadata */}
    <div className="mt-2 text-xs text-gray-500 flex justify-between">
      <span>{formatTime(message.timestamp)}</span>
      {message.confidence && (
        <span>Confidence: {Math.round(message.confidence * 100)}%</span>
      )}
    </div>
  </div>
</div>
```

---

## ADVANCED FEATURES

### Document Integration
```typescript
interface DocumentPreview {
  document: string;
  content: string;
  highlights: TextHighlight[];
  relatedSections: string[];
}

interface TextHighlight {
  start: number;
  end: number;
  relevance: number;
  context: string;
}

// Document preview with highlighted relevant sections
<div className="document-preview">
  <div className="document-header">
    <h3>{document.title}</h3>
    <span className="document-category">{document.category}</span>
  </div>
  
  <div className="document-content">
    <HighlightedText 
      content={document.content}
      highlights={highlights}
      onHighlightClick={scrollToSection}
    />
  </div>
  
  <div className="document-actions">
    <button onClick={() => openFullDocument(document.id)}>
      View Full Document
    </button>
    <button onClick={() => askAboutDocument(document.id)}>
      Ask About This Document
    </button>
  </div>
</div>
```

### Smart Suggestions
```typescript
interface SmartSuggestion {
  type: 'question' | 'topic' | 'document' | 'comparison';
  content: string;
  relevance: number;
  category?: string;
}

// Smart suggestion system
<div className="suggestions-panel">
  <h4>You might want to ask:</h4>
  
  <div className="suggestion-categories">
    <div className="category">
      <h5>About Current Topic</h5>
      {topicSuggestions.map(suggestion => (
        <SuggestionButton 
          key={suggestion.content}
          suggestion={suggestion}
          onClick={sendMessage}
        />
      ))}
    </div>
    
    <div className="category">
      <h5>Document Comparisons</h5>
      {comparisonSuggestions.map(suggestion => (
        <SuggestionButton 
          key={suggestion.content}
          suggestion={suggestion}
          onClick={sendMessage}
        />
      ))}
    </div>
    
    <div className="category">
      <h5>Policy Analysis</h5>
      {policySuggestions.map(suggestion => (
        <SuggestionButton 
          key={suggestion.content}
          suggestion={suggestion}
          onClick={sendMessage}
        />
      ))}
    </div>
  </div>
</div>
```

### Session Management
```typescript
interface ChatSession {
  id: string;
  title: string;
  startTime: Date;
  lastActivity: Date;
  messageCount: number;
  mainTopics: string[];
  documentsDiscussed: string[];
}

// Session sidebar
<div className="session-history">
  <div className="session-header">
    <h3>Recent Conversations</h3>
    <button onClick={startNewSession}>New Chat</button>
  </div>
  
  <div className="session-list">
    {sessions.map(session => (
      <div 
        key={session.id}
        className={`session-item ${currentSession?.id === session.id ? 'active' : ''}`}
        onClick={() => loadSession(session.id)}
      >
        <div className="session-title">{session.title}</div>
        <div className="session-meta">
          <span>{session.messageCount} messages</span>
          <span>{formatRelativeTime(session.lastActivity)}</span>
        </div>
        <div className="session-topics">
          {session.mainTopics.slice(0, 3).map(topic => (
            <span key={topic} className="topic-tag">{topic}</span>
          ))}
        </div>
      </div>
    ))}
  </div>
</div>
```

---

## API INTEGRATION

### Chat API Service
```typescript
class ChatAPIService {
  private baseURL: string;
  private websocket: WebSocket | null = null;

  async sendMessage(sessionId: string, message: string): Promise<ChatResponse> {
    const response = await fetch(`${this.baseURL}/webhook/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`
      },
      body: JSON.stringify({
        sessionId,
        userMessage: message,
        timestamp: new Date().toISOString()
      })
    });

    if (!response.ok) {
      throw new Error(`Chat API error: ${response.statusText}`);
    }

    return response.json();
  }

  async loadSession(sessionId: string): Promise<ChatSession> {
    const response = await fetch(`${this.baseURL}/api/sessions/${sessionId}`);
    return response.json();
  }

  async searchDocuments(query: string, filters?: DocumentFilters): Promise<Document[]> {
    const params = new URLSearchParams({
      q: query,
      ...filters
    });
    
    const response = await fetch(`${this.baseURL}/api/documents/search?${params}`);
    return response.json();
  }

  connectWebSocket(sessionId: string, onMessage: (message: ChatMessage) => void) {
    this.websocket = new WebSocket(`${this.wsURL}/chat/${sessionId}`);
    
    this.websocket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      onMessage(message);
    };

    this.websocket.onclose = () => {
      // Implement reconnection logic
      setTimeout(() => this.connectWebSocket(sessionId, onMessage), 5000);
    };
  }
}
```

### React Hooks
```typescript
// Custom hook for chat functionality
export function useChat(sessionId: string) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [session, setSession] = useState<ChatSession | null>(null);

  const sendMessage = useCallback(async (content: string) => {
    const userMessage: ChatMessage = {
      id: generateId(),
      type: 'user',
      content,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    try {
      const response = await chatAPI.sendMessage(sessionId, content);
      
      const systemMessage: ChatMessage = {
        id: generateId(),
        type: 'system',
        content: response.answer,
        timestamp: new Date(),
        citations: response.citations,
        followUpQuestions: response.followUpQuestions,
        confidence: response.confidence
      };

      setMessages(prev => [...prev, systemMessage]);
    } catch (error) {
      console.error('Failed to send message:', error);
      // Handle error state
    } finally {
      setIsTyping(false);
    }
  }, [sessionId]);

  useEffect(() => {
    // Load session on mount
    chatAPI.loadSession(sessionId).then(setSession);
    
    // Connect WebSocket for real-time updates
    chatAPI.connectWebSocket(sessionId, (message) => {
      setMessages(prev => [...prev, message]);
    });
  }, [sessionId]);

  return {
    messages,
    session,
    isTyping,
    sendMessage
  };
}
```

---

## DEPLOYMENT CONFIGURATION

### Docker Configuration
```dockerfile
# Dockerfile for chat interface
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
```

### Environment Variables
```bash
# Chat Interface Environment
REACT_APP_API_URL=http://localhost:5678
REACT_APP_WS_URL=ws://localhost:5678
REACT_APP_GOOGLE_DRIVE_API_KEY=your_drive_api_key
REACT_APP_SESSION_TIMEOUT=3600000
REACT_APP_MAX_MESSAGE_LENGTH=2000

# Backend Integration
N8N_WEBHOOK_URL=http://n8n:5678/webhook/chat
DATABASE_URL=***********************************************************/political_conversations
REDIS_URL=redis://redis:6379
```

### Build and Deployment Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "test": "jest",
    "test:e2e": "playwright test",
    "docker:build": "docker build -t political-chat-interface .",
    "docker:run": "docker run -p 3000:3000 political-chat-interface"
  }
}
```

This chat interface provides a professional, user-friendly way to interact with the political document system, enabling natural conversations about policies, documents, and political strategy with full memory and context retention.