FROM node:18-alpine

# Install FFmpeg and other dependencies
RUN apk update && apk upgrade && apk add --no-cache \
    ffmpeg \
    curl \
    dumb-init \
    python3 \
    make \
    g++ \
    vips-dev \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Create logs and temp directories
RUN mkdir -p /app/logs /tmp/multimodal

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcpuser -u 1001 -G nodejs

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY . .

# Set proper permissions
RUN chown -R mcpuser:nodejs /app && \
    chown -R mcpuser:nodejs /tmp/multimodal

# Switch to non-root user
USER mcpuser

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8091/health || exit 1

# Expose port
EXPOSE 8091

# Start the application
CMD ["node", "server.js"]