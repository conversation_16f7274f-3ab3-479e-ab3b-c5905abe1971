complete-rovodev-n8n-workflow-build-prompt.md

                                                                               │
│                                        📋 Complete Document List for AI Agent                                        │
│                                                                                                                      │
│                                       ESSENTIAL DOCUMENTS (Must Provide All):                                        │
│                                                                                                                      │
│                                            Primary Implementation Guide:                                             │
│                                                                                                                      │
│  1 n8n_implementation_guide_complete.md ⭐ MOST IMPORTANT                                                            │
│                                                                                                                      │
│                                            Manifesto Integration System:                                             │
│                                                                                                                      │
│  2 manifesto_for_agents.md                                                                                           │
│  3 core_essence.md                                                                                                   │
│  4 style_guide.md                                                                                                    │
│  5 voice_guidelines_beau_lewis.md                                                                                    │
│                                                                                                                      │
│                                        Category-Specific Supplements (All 9):                                        │
│                                                                                                                      │
│   6 category_supplement_healthcare.md                                                                                │
│   7 category_supplement_education.md                                                                                 │
│   8 category_supplement_economic_policy.md                                                                           │
│   9 category_supplement_housing.md                                                                                   │
│  10 category_supplement_jobs_automation.md                                                                           │
│  11 category_supplement_constitutional_amendments.md                                                                 │
│  12 category_supplement_ethics_accountability.md                                                                     │
│  13 category_supplement_rights_repair_grow.md                                                                        │
│  14 category_supplement_funding_revenue.md                                                                           │
│                                                                                                                      │
│                                          Technical Architecture Documents:                                           │
│                                                                                                                      │
│  15 rovodev_n8n_master_design.md                                                                                     │
│  16 n8n_workflow_setup.md                                                                                            │
│  17 tmp_rovodev_ai_model_research_analysis.md                                                                        │
│  18 tmp_rovodev_docker_compose_n8n.yml                                                                               │
│                                                                                                                      │
│                                              Supporting Technical Docs:                                              │
│                                                                                                                      │
│  19 tmp_rovodev_folder_structure_design.md                                                                           │
│  20 tmp_rovodev_manifesto_integration_strategy.md                                                                    │
│  21 tmp_rovodev_professional_docx_templates.md                                                                       │
│  22 tmp_rovodev_prompt_file_format_standard.md                                                                       │
│  23 tmp_rovodev_quality_control_conversation_system.md                                                               │
│  24 tmp_rovodev_workflow_architecture_design.md                                                                      │
│                                                                                                                      │
│                                       Source Documents (For Voice Reference):                                        │
│                                                                                                                      │
│  25 rovodev_manifesto.md                                                                                             │
│  26 white_papers_markdown/others/redlines final final 1.md                                                           │
│  27 white_papers_markdown/others/2028 democratic 2.md                                                                │
│                                                                                                                      │
│ ──────────────────────────────────────────────────────────────────────────────────────────────────────────────────── │
│                                                                                                                      │
│                                                 🤖 AI Agent Prompts                                                  │
│                                                                                                                      │
│                                                Initial Setup Prompt:                                                 │
│                                                                                                                      │
│                                                                                                                      │
│  You are an expert n8n workflow builder with access to an n8n MCP server. Your task is to build a sophisticated      │
│  political document processing system for Beau Lewis that embodies his vision for economic justice and democratic    │
│  renewal.                                                                                                            │
│                                                                                                                      │
│  CRITICAL INSTRUCTIONS:                                                                                              │
│  1. READ the "n8n_implementation_guide_complete.md" document FIRST - this is your primary blueprint                  │
│  2. This system must faithfully represent Beau Lewis's political vision and voice in every output                    │
│  3. Use the 10,000-token manifesto integration system to ensure AI agents understand his vision                      │
│  4. Build ALL workflows, MCP tools, and integrations as specified in the implementation guide                        │
│                                                                                                                      │
│  SYSTEM OVERVIEW:                                                                                                    │
│  - Multi-agent AI system processing political documents via n8n workflows                                            │
│  - Manifesto-guided content generation ensuring vision fidelity                                                      │
│  - ChromaDB RAG for intelligent document retrieval                                                                   │
│  - Professional DOCX output with quality control                                                                     │
│  - Conversational refinement system                                                                                  │
│                                                                                                                      │
│  START BY:                                                                                                           │
│  1. Reading the complete implementation guide                                                                        │
│  2. Understanding the manifesto integration system                                                                   │
│  3. Setting up the MCP tool definitions                                                                              │
│  4. Building the main document processing workflow                                                                   │
│                                                                                                                      │
│  Do you understand the scope and are you ready to begin implementation?                                              │
│                                                                                                                      │
│                                                                                                                      │
│                                                MCP Tool Setup Prompt:                                                │
│                                                                                                                      │
│                                                                                                                      │
│  Now configure the MCP tools on the n8n MCP server. The implementation guide contains exact JSON schemas for these   │
│  tools:                                                                                                              │
│                                                                                                                      │
│  REQUIRED MCP TOOLS:                                                                                                 │
│  1. document_intent_analyzer - Analyzes documents and determines processing strategy                                 │
│  2. content_generator - Creates political content aligned with manifesto                                             │
│  3. quality_control_agent - Reviews content for manifesto alignment                                                  │
│  4. retrieve_relevant_documents - RAG queries to ChromaDB                                                            │
│  5. web_research_agent - Conducts supporting research                                                                │
│                                                                                                                      │
│  CRITICAL REQUIREMENTS:                                                                                              │
│  - Each tool must support 10,000-token manifesto context                                                             │
│  - Tools must use the specified AI models (o3, Claude 4 Sonnet, Gemini 2.5 Pro)                                      │
│  - Include proper error handling and fallback options                                                                │
│  - Ensure tools can access and load manifesto documents                                                              │
│                                                                                                                      │
│  Use the exact JSON schemas provided in the implementation guide. Confirm each tool is properly configured before    │
│  proceeding.                                                                                                         │
│                                                                                                                      │
│                                                                                                                      │
│                                              Workflow Building Prompt:                                               │
│                                                                                                                      │
│                                                                                                                      │
│  Build the main n8n workflow "AI Document Processor" using the detailed specifications in the implementation guide.  │
│                                                                                                                      │
│  WORKFLOW REQUIREMENTS:                                                                                              │
│  1. Google Drive file monitoring (political_in folder)                                                               │
│  2. Document classification and manifesto context loading                                                            │
│  3. AI agent analysis via MCP tools                                                                                  │
│  4. Task routing (edit/combine/generate/create)                                                                      │
│  5. Specialized processing branches                                                                                  │
│  6. Quality control review                                                                                           │
│  7. CloudConvert MD→DOCX conversion                                                                                  │
│  8. Google Drive output (political_out folder)                                                                       │
│  9. Email notifications                                                                                              │
│                                                                                                                      │
│  MANIFESTO INTEGRATION:                                                                                              │
│  - Load core_essence.md + style_guide.md + manifesto_for_agents.md (3,000 tokens)                                    │
│  - Add relevant category supplements (2,000-3,000 tokens)                                                            │
│  - Include voice guidelines and examples (4,000-5,000 tokens)                                                        │
│  - Total: 10,000 tokens per AI agent                                                                                 │
│                                                                                                                      │
│  Follow the exact node configurations provided in the implementation guide. Test each node as you build it.          │
│                                                                                                                      │
│                                                                                                                      │
│                                            Quality Control Setup Prompt:                                             │
│                                                                                                                      │
│                                                                                                                      │
│  Implement the quality control and conversational refinement system:                                                 │
│                                                                                                                      │
│  REQUIREMENTS:                                                                                                       │
│  1. Quality control agent that reviews all outputs for manifesto alignment                                           │
│  2. Conversational refinement workflow for user feedback                                                             │
│  3. Version control system (never overwrite originals)                                                               │
│  4. Quality metrics tracking and reporting                                                                           │
│                                                                                                                      │
│  KEY FEATURES:                                                                                                       │
│  - AI assessment of manifesto alignment (score 1-10)                                                                 │
│  - Voice consistency checking against Beau Lewis's style                                                             │
│  - Factual accuracy verification                                                                                     │
│  - Professional formatting validation                                                                                │
│  - Interactive revision capability                                                                                   │
│                                                                                                                      │
│  Use the quality control specifications in the implementation guide and ensure the system maintains Beau Lewis's     │
│  authentic voice throughout.                                                                                         │
│                                                                                                                      │
│                                                                                                                      │
│                                            Testing and Validation Prompt:                                            │
│                                                                                                                      │
│                                                                                                                      │
│  Test the complete system end-to-end:                                                                                │
│                                                                                                                      │
│  TESTING CHECKLIST:                                                                                                  │
│  1. Upload a test document to political_in folder                                                                    │
│  2. Verify manifesto context loads properly (10,000 tokens)                                                          │
│  3. Confirm AI agents produce content aligned with Beau Lewis's vision                                               │
│  4. Check quality control catches and fixes issues                                                                   │
│  5. Validate DOCX output is professionally formatted                                                                 │
│  6. Ensure email notifications work correctly                                                                        │
│                                                                                                                      │
│  VALIDATION CRITERIA:                                                                                                │
│  - Content reflects Beau Lewis's passionate but professional voice                                                   │
│  - Uses signature phrases like "It's not red vs. blue, it's the 1% vs. you"                                          │
│  - Focuses on economic justice and democratic renewal themes                                                         │
│  - Maintains moral clarity and empathetic understanding                                                              │
│  - Provides concrete policy solutions connected to ASTF                                                              │
│                                                                                                                      │
│  Document any issues and provide recommendations for optimization.                                                   │
│                                                                                                                      │
│                                                                                                                      │
│                                               Final Deployment Prompt:                                               │
│                                                                                                                      │
│                                                                                                                      │
│  Complete the production deployment:                                                                                 │
│                                                                                                                      │
│  DEPLOYMENT TASKS:                                                                                                   │
│  1. Set up monitoring and error handling workflows                                                                   │
│  2. Configure backup and disaster recovery                                                                           │
│  3. Implement security measures and access controls                                                                  │
│  4. Create user documentation and training materials                                                                 │
│  5. Establish maintenance and update procedures                                                                      │
│                                                                                                                      │
│  ONGOING OPERATIONS:                                                                                                 │
│  - Monitor system performance and quality metrics                                                                    │
│  - Update manifesto context as Beau Lewis's vision evolves                                                           │
│  - Optimize AI agent performance and token usage                                                                     │
│  - Maintain ChromaDB with new documents                                                                              │
│  - Provide regular system health reports                                                                             │
│                                                                                                                      │
│  Ensure the system is ready for production use and can reliably process political documents while maintaining        │
│  fidelity to Beau Lewis's vision for economic justice and democratic renewal.                                        │
│                                                                                                                      │
│                                                                                                                      │
│ ──────────────────────────────────────────────────────────────────────────────────────────────────────────────────── │
│                                                                                                                      │
│                                             🎯 Critical Success Factors:                                             │
│                                                                                                                      │
│                                                  For the AI Agent:                                                   │
│                                                                                                                      │
│  1 Read implementation guide FIRST - It contains everything needed                                                   │
│  2 Understand the political mission - This isn't just a technical system                                             │
│  3 Maintain manifesto fidelity - Every output must reflect Beau Lewis's vision                                       │
│  4 Test thoroughly - Quality and voice consistency are critical                                                      │
│  5 Document everything - Provide clear handoff documentation                                                         │
│                                                                                                                      │
│                                               Key Integration Points:                                                │
│                                                                                                                      │
│  • 10,000-token manifesto system ensures vision alignment                                                            │
│  • MCP server tools enable sophisticated AI agent coordination                                                       │
│  • ChromaDB RAG provides intelligent document context                                                                │
│  • Quality control maintains standards and voice consistency                                                         │
│  • Professional output creates publication-ready documents                                                           │
│                                                                                                                      │
│ This complete package gives the AI agent everything needed to build a sophisticated political document processing    │
│ system that faithfully represents Beau Lewis's vision for economic justice and democratic renewal.