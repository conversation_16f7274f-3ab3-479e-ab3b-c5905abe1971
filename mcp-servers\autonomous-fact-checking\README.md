# Autonomous Fact-Checking MCP Server

A comprehensive fact-checking server with multi-source verification, AI consensus analysis, and real-time monitoring capabilities for the political document processing system.

## Overview

The Autonomous Fact-Checking MCP Server provides:
- Multi-source fact verification using trusted news sources, government APIs, and academic databases
- AI consensus analysis using GPT-4, Claude, and Gemini
- Real-time claim monitoring and verification
- Source credibility assessment and scoring
- Automated evidence gathering and synthesis
- Political claim classification and verification

## Features

### 🔍 Multi-Source Verification
- **Trusted News Sources**: Reuters, AP, BBC, NPR, and other tier-1 sources
- **Government Sources**: Official government websites and databases
- **Academic Sources**: Research institutions and scholarly articles
- **Fact-Check APIs**: Integration with Google Fact Check and other services

### 🤖 AI Consensus Analysis
- **Multi-Model Verification**: GPT-4, <PERSON>, and Gemini Pro
- **Confidence Scoring**: Weighted consensus with variance analysis
- **Reasoning Generation**: Clear explanations for fact-check verdicts
- **Bias Detection**: Cross-model analysis to reduce individual AI biases

### 🌐 Real-Time Monitoring
- **Live Claim Detection**: Monitor RSS feeds and social media
- **Automated Processing**: Immediate fact-checking of detected claims
- **WebSocket Notifications**: Real-time alerts for high-priority claims
- **Custom Monitoring**: Configurable sources and keywords

### 📊 Source Credibility
- **Tiered Source Classification**: Automatic credibility scoring
- **Historical Accuracy**: Track source reliability over time
- **Bias Assessment**: Political lean and reliability analysis
- **Blacklist Management**: Automated filtering of unreliable sources

## Installation

### Prerequisites
- Node.js 16+
- Redis 6+
- PostgreSQL 12+
- SQLite 3+
- Docker (optional)
- AI API Keys (OpenAI, Anthropic, Google)

### Environment Variables

```bash
# Server Configuration
MCP_SERVER_PORT=8094
LOG_LEVEL=info

# AI API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key

# Database Configuration
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432
POSTGRES_DB=political_conversations
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=your_postgres_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# External APIs (Optional)
NEWS_API_KEY=your_news_api_key
GOOGLE_FACT_CHECK_API_KEY=your_google_fact_check_api_key

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5678
```

### Docker Installation

```bash
docker run -d \
  --name autonomous-fact-checking-mcp \
  -p 8094:8094 \
  --env-file .env \
  --network n8n-network \
  -v /app/data:/app/data \
  autonomous-fact-checking-mcp:latest
```

### Manual Installation

```bash
# Clone the repository
git clone <repository-url>
cd mcp-servers/autonomous-fact-checking

# Install dependencies
npm install

# Install system dependencies
apt-get update && apt-get install -y sqlite3

# Initialize database
npm run init-db

# Start the server
npm start
```

## MCP Tools

### 1. fact_check_claim
Perform comprehensive fact-checking on a political claim with multi-source verification.

**Input Schema:**
```json
{
  "claim": "The federal budget deficit increased by 25% in 2023",
  "priority": "high",
  "includeRealTimeNews": true
}
```

**Response:**
```json
{
  "id": "fact-check-uuid-1234",
  "claim": "The federal budget deficit increased by 25% in 2023",
  "verdict": "MOSTLY_TRUE",
  "confidence": 0.85,
  "evidence": [
    {
      "type": "trusted_sources",
      "confidence": 0.9,
      "results": [
        {
          "source": "reuters.com",
          "title": "U.S. Budget Deficit Rises 23% in Fiscal 2023",
          "url": "https://reuters.com/...",
          "credibilityScore": 0.95
        }
      ]
    },
    {
      "type": "government_sources",
      "confidence": 0.95,
      "results": [
        {
          "source": "treasury.gov",
          "title": "Monthly Treasury Statement",
          "url": "https://treasury.gov/...",
          "credibilityScore": 0.98
        }
      ]
    }
  ],
  "reasoning": "Government data shows a 23% increase in the federal budget deficit, which is close to the claimed 25%. The slight discrepancy may be due to different measurement periods or methodologies.",
  "sources": ["reuters.com", "treasury.gov", "cbo.gov"],
  "timestamp": "2024-01-20T15:30:00Z"
}
```

### 2. batch_fact_check
Fact-check multiple claims in batch for efficiency.

**Input Schema:**
```json
{
  "claims": [
    "Unemployment rate dropped to 3.5% in December 2023",
    "Infrastructure spending increased by 40% this year",
    "Climate change funding was cut by 15%"
  ],
  "maxConcurrent": 3
}
```

**Response:**
```json
{
  "totalClaims": 3,
  "successfulChecks": 3,
  "results": [
    {
      "claim": "Unemployment rate dropped to 3.5% in December 2023",
      "verdict": "TRUE",
      "confidence": 0.95
    },
    {
      "claim": "Infrastructure spending increased by 40% this year",
      "verdict": "MOSTLY_TRUE",
      "confidence": 0.78
    },
    {
      "claim": "Climate change funding was cut by 15%",
      "verdict": "FALSE",
      "confidence": 0.92
    }
  ]
}
```

### 3. verify_source_credibility
Assess the credibility and reliability of information sources.

**Input Schema:**
```json
{
  "source": "example-news-site.com",
  "includeHistoricalAnalysis": true
}
```

**Response:**
```json
{
  "source": "example-news-site.com",
  "credibilityScore": 0.75,
  "tier": "tier3",
  "recommendation": "trusted",
  "analysis": {
    "factualAccuracy": 0.82,
    "bias": "slight_left",
    "transparencyScore": 0.88,
    "historicalReliability": 0.79
  }
}
```

### 4. get_fact_check_history
Retrieve historical fact-checking results with filtering.

**Input Schema:**
```json
{
  "verdict": "MOSTLY_TRUE",
  "minConfidence": 0.8,
  "dateFrom": "2024-01-01T00:00:00Z",
  "limit": 50
}
```

### 5. real_time_monitoring
Start real-time monitoring of claims and statements for immediate fact-checking.

**Input Schema:**
```json
{
  "sources": [
    "https://feeds.reuters.com/reuters/topNews",
    "https://rss.cnn.com/rss/edition.rss"
  ],
  "keywords": ["budget", "deficit", "spending", "economy"],
  "webhookUrl": "https://your-server.com/webhook"
}
```

**Response:**
```json
{
  "sessionId": "monitor-session-uuid",
  "status": "monitoring_started",
  "message": "Real-time fact-checking monitoring activated",
  "monitoredSources": 2,
  "keywords": 4
}
```

## Fact-Checking Process

### 1. Claim Extraction
- **Entity Recognition**: Extract people, organizations, dates, numbers
- **Factual Statement Identification**: AI-powered claim detection
- **Context Analysis**: Understand claim context and implications

### 2. Multi-Source Verification
- **Trusted News Search**: Query tier-1 news sources
- **Government Data**: Check official statistics and reports
- **Academic Sources**: Research papers and institutional reports
- **Fact-Check APIs**: Cross-reference with existing fact-checks

### 3. AI Consensus Analysis
- **GPT-4 Analysis**: Detailed reasoning and evidence assessment
- **Claude Verification**: Independent analysis for consensus
- **Gemini Cross-Check**: Third opinion to reduce bias
- **Confidence Calculation**: Weighted average with variance boost

### 4. Evidence Synthesis
- **Credibility Weighting**: Weight evidence by source reliability
- **Conflict Resolution**: Handle contradictory information
- **Verdict Determination**: Final truth assessment
- **Reasoning Generation**: Clear explanation of decision

## Source Credibility Tiers

### Tier 1 (0.95 Credibility)
- Reuters, Associated Press, BBC, NPR, PBS
- FactCheck.org, Snopes, PolitiFact
- Washington Post (fact-checking section)

### Tier 2 (0.85 Credibility)
- CNN, New York Times, Wall Street Journal
- The Guardian, The Economist, Axios
- Bloomberg, CBS News, ABC News

### Tier 3 (0.75 Credibility)
- USA Today, Time, Newsweek
- The Hill, Politico

### Government Sources (0.90 Credibility)
- All .gov domains
- Congressional websites
- Supreme Court, GAO, CBO

### Academic Sources (0.88 Credibility)
- .edu domains
- Scholarly databases
- Research institutions

### Unreliable Sources (0.20 Credibility)
- Known misinformation sites
- Conspiracy theory platforms
- Heavily biased sources

## Verdict Categories

- **TRUE**: Claim is accurate and well-supported (confidence ≥ 0.8)
- **MOSTLY_TRUE**: Claim is largely accurate with minor issues (confidence ≥ 0.6)
- **MIXED**: Claim has both accurate and inaccurate elements (confidence ≥ 0.4)
- **MOSTLY_FALSE**: Claim is largely inaccurate (confidence ≥ 0.2)
- **FALSE**: Claim is demonstrably false (confidence < 0.2)

## API Endpoints

### Health & Monitoring
- `GET /health` - Service health check
- `GET /metrics` - Prometheus metrics

### MCP Tool Endpoint
- `POST /mcp/call` - Execute MCP tools

## Database Schema

### fact_check_results
Stores all fact-checking results:
```sql
CREATE TABLE fact_check_results (
  id UUID PRIMARY KEY,
  claim TEXT NOT NULL,
  verdict VARCHAR(20) NOT NULL,
  confidence FLOAT NOT NULL,
  evidence JSONB,
  sources JSONB,
  reasoning TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Performance Metrics

The server tracks:
- **Fact Check Counters**: Total checks by type, status, and verdict
- **Processing Duration**: Time spent on verification
- **Source Validation**: Source credibility assessments
- **Real-time Sessions**: Active monitoring sessions

## Real-Time Monitoring

### WebSocket Events
- **claim-detected**: New claim found in monitored sources
- **fact-check-complete**: Verification completed
- **high-priority-alert**: Critical claims requiring immediate attention

### Monitoring Sources
- RSS feeds from trusted news sources
- Social media APIs (Twitter, Facebook)
- Government announcement feeds
- Press release services

## Security Features

### Rate Limiting
- 200 requests/15min general limit
- Stricter limits for batch operations
- DDoS protection

### Data Privacy
- No personal data storage
- Claim anonymization options
- GDPR compliance features

### Source Verification
- Automatic URL validation
- Content authenticity checking
- Malicious link detection

## Troubleshooting

### Common Issues

1. **API Key Errors**
   - Verify all AI API keys are valid
   - Check API usage limits and billing
   - Ensure proper environment variable setup

2. **Source Access Issues**
   - Check internet connectivity
   - Verify source websites are accessible
   - Monitor for rate limiting from external sources

3. **Database Connection Problems**
   - Ensure PostgreSQL and Redis are running
   - Check database credentials and permissions
   - Monitor database connection pool

### Debug Mode
Enable detailed logging:
```bash
LOG_LEVEL=debug npm start
```

## Monitoring and Alerts

### Key Metrics
- `fact_checks_total` - Total fact checks by verdict
- `fact_check_duration_seconds` - Processing time distribution
- `source_validations_total` - Source credibility checks
- `realtime_fact_checks_active` - Active monitoring sessions

### Alerting
Set up alerts for:
- High error rates in fact-checking
- Unusual verdict patterns
- Source credibility changes
- API rate limit approaches

## Best Practices

1. **Claim Formatting**: Provide clear, specific claims for best results
2. **Source Diversity**: Use multiple source types for verification
3. **Context Provision**: Include relevant context for complex claims
4. **Regular Updates**: Keep source lists and credibility scores updated
5. **Human Review**: Use AI results as starting points, not final verdicts

## License

This MCP server is part of the political document processing system. See the main project license for details.