{"name": "political-chat-interface", "version": "1.0.0", "description": "Conversational interface for political document processing system", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "npm run build:client", "build:client": "cd client && npm run build"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.0", "pg": "^8.11.3", "redis": "^4.6.10", "axios": "^1.6.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "marked": "^9.1.6", "dompurify": "^3.0.5", "jsdom": "^23.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}