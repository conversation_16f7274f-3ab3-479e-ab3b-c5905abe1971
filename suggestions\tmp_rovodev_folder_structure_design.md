# Google Drive Folder Structure Design for Political Document Processing

## Recommended Hierarchical Structure

### Input Folder: `political_in/`
```
political_in/
├── 01_healthcare/
│   ├── universal_healthcare/
│   ├── medicare_expansion/
│   ├── prescription_drugs/
│   └── mental_health/
├── 02_education/
│   ├── free_higher_education/
│   ├── k12_reform/
│   ├── student_debt/
│   └── workforce_training/
├── 03_economic_policy/
│   ├── wealth_inequality/
│   ├── tax_reform/
│   ├── minimum_wage/
│   └── worker_rights/
├── 04_climate_environment/
│   ├── green_new_deal/
│   ├── renewable_energy/
│   ├── environmental_justice/
│   └── carbon_pricing/
├── 05_social_justice/
│   ├── criminal_justice_reform/
│   ├── voting_rights/
│   ├── civil_rights/
│   └── immigration/
├── 06_foreign_policy/
│   ├── diplomacy/
│   ├── military_policy/
│   ├── trade/
│   └── international_cooperation/
├── 07_technology_privacy/
│   ├── data_privacy/
│   ├── ai_regulation/
│   ├── digital_rights/
│   └── tech_monopolies/
├── 08_housing_urban/
│   ├── affordable_housing/
│   ├── urban_planning/
│   ├── homelessness/
│   └── community_development/
├── 09_transportation/
│   ├── public_transit/
│   ├── infrastructure/
│   ├── electric_vehicles/
│   └── transportation_equity/
├── 10_agriculture_food/
│   ├── food_security/
│   ├── sustainable_farming/
│   ├── nutrition_policy/
│   └── rural_development/
├── _instructions/
│   ├── simple_synthesis/
│   ├── complex_analysis/
│   └── templates/
└── _manifesto/
    ├── current_version/
    ├── drafts/
    └── archived_versions/
```

### Output Folder: `political_out/`
```
political_out/
├── 01_healthcare/
│   ├── white_papers/
│   ├── policy_briefs/
│   ├── implementation_plans/
│   └── talking_points/
├── 02_education/
│   ├── white_papers/
│   ├── policy_briefs/
│   ├── implementation_plans/
│   └── talking_points/
├── [same structure for all categories]
├── _cross_category/
│   ├── comprehensive_platforms/
│   ├── integrated_policies/
│   └── master_documents/
├── _archives/
│   ├── 2024/
│   ├── 2025/
│   └── older/
└── _drafts/
    ├── in_progress/
    ├── review_needed/
    └── ready_for_final/
```

## File Naming Convention

### Input Files
```
[CATEGORY]_[SUBCATEGORY]_[TYPE]_[DATE]_[VERSION].md

Examples:
healthcare_universal_research_2024-01-15_v1.md
education_higher_ed_policy_2024-01-15_v2.md
economic_tax_reform_analysis_2024-01-15_v1.md
```

### Instruction Files
```
INSTRUCTION_[PROJECT_NAME]_[TYPE]_[DATE].md

Examples:
INSTRUCTION_healthcare_reform_simple_2024-01-15.md
INSTRUCTION_comprehensive_platform_complex_2024-01-15.md
```

### Output Files
```
[PROJECT]_[DOCUMENT_TYPE]_[DATE]_[STATUS].docx

Examples:
Healthcare_Reform_WhitePaper_2024-01-15_FINAL.docx
Education_Policy_Brief_2024-01-15_DRAFT.docx
Economic_Platform_Implementation_2024-01-15_REVIEW.docx
```

### Updated Workflow Trigger System

### Folder Monitoring Logic
1. **Primary Triggers**: New files in category subfolders
2. **Instruction Detection**: Files starting with "PROMPT" (user preference)
3. **Batch Processing**: Process all documents when PROMPT file detected
4. **Document Preservation**: Never delete or overwrite original documents

### Processing Rules
```
IF new_files in category_folder:
    IF any_file.startswith("PROMPT"):
        prompt_file = find_prompt_file()
        related_docs = gather_all_other_files_in_folder()
        trigger_lead_agent_workflow(prompt_file, related_docs)
        
        AFTER processing_complete:
            move_originals_to_archive_subfolder()
            keep_originals_intact()
    ELSE:
        wait_for_prompt_file()

workflow_complete:
    create_new_versions_only()
    apply_beautiful_docx_formatting()
    rename_according_to_conventions()
    move_to_appropriate_output_folder()
```

### Document Movement Strategy
```
Before Processing:
political_in/healthcare/
├── PROMPT_universal_healthcare_analysis.md
├── medicare_research_2024.md
├── international_models_2024.md
├── cost_analysis_2024.md
└── implementation_study_2024.md

After Processing:
political_in/healthcare/
└── _processed_2024-01-15/
    ├── PROMPT_universal_healthcare_analysis.md
    ├── medicare_research_2024.md
    ├── international_models_2024.md
    ├── cost_analysis_2024.md
    └── implementation_study_2024.md

political_out/healthcare/white_papers/
├── Universal_Healthcare_Comprehensive_Analysis_2024-01-15_v1.docx
├── Medicare_Expansion_Policy_Brief_2024-01-15_v1.docx
└── Healthcare_Implementation_Timeline_2024-01-15_v1.docx
```

## Document Type Templates

### White Paper Structure
```
1. Executive Summary
2. Problem Statement
3. Current Landscape Analysis
4. Proposed Solution
5. Implementation Framework
6. Cost-Benefit Analysis
7. Timeline and Milestones
8. Potential Challenges
9. Conclusion and Next Steps
10. Appendices
```

### Policy Brief Structure
```
1. Issue Overview (1 page)
2. Key Recommendations (bullet points)
3. Supporting Evidence (1-2 pages)
4. Implementation Steps
5. Resource Requirements
6. Expected Outcomes
```

### Implementation Plan Structure
```
1. Objectives and Goals
2. Phase 1: Immediate Actions (0-6 months)
3. Phase 2: Short-term Implementation (6-18 months)
4. Phase 3: Medium-term Goals (1.5-3 years)
5. Phase 4: Long-term Vision (3-10 years)
6. Resource Allocation
7. Success Metrics
8. Risk Mitigation
```

## Advanced Features

### Cross-Category Integration
- **Linked Projects**: Healthcare + Education workforce development
- **Policy Intersections**: Climate + Economic policy integration
- **Comprehensive Platforms**: All categories synthesized

### Version Control
- **Automatic Versioning**: Track document evolution
- **Change Tracking**: What changed between versions
- **Rollback Capability**: Revert to previous versions

### Collaboration Features
- **Review Workflows**: Draft → Review → Final
- **Comment Integration**: Feedback and suggestions
- **Approval Processes**: Multi-stage document approval

## Benefits of This Structure

1. **Scalability**: Easy to add new categories and subcategories
2. **Organization**: Clear hierarchy prevents confusion
3. **Automation**: Predictable structure enables smart workflows
4. **Flexibility**: Can handle simple and complex projects
5. **Collaboration**: Clear review and approval processes
6. **Archival**: Systematic storage of historical documents

## Questions for Refinement

1. **Category Completeness**: Are there policy areas I missed?
2. **Subcategory Depth**: Do you need more granular subcategories?
3. **Document Types**: Are there other output formats you need?
4. **Workflow Complexity**: Should we add more sophisticated routing?
5. **Integration Needs**: Any external systems to connect with?

Would you like me to expand on any particular aspect of this folder structure, or shall we move to the next design component?