-- Political Document Processing System - Database Initialization
-- This script sets up the required databases and tables

-- Create additional databases
CREATE DATABASE political_conversations;
CREATE DATABASE manifesto_db;

-- Connect to political_conversations database
\c political_conversations;

-- ============================================
-- CONVERSATION MANAGEMENT TABLES
-- ============================================

-- Conversation Sessions Table
CREATE TABLE conversation_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL DEFAULT 'beau_lewis',
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    conversation_summary TEXT,
    active_documents JSONB DEFAULT '[]',
    conversation_theme VARCHAR(255),
    context_summary TEXT,
    user_preferences JSONB DEFAULT '{}',
    session_metadata JSONB DEFAULT '{}'
);

-- Conversation History Table
CREATE TABLE conversation_history (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES conversation_sessions(session_id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_message TEXT NOT NULL,
    system_response TEXT NOT NULL,
    documents_referenced JSONB DEFAULT '[]',
    confidence_score FLOAT DEFAULT 0.0,
    follow_up_suggestions JSONB DEFAULT '[]',
    token_usage JSONB DEFAULT '{}',
    processing_time_ms INTEGER DEFAULT 0
);

-- Document Interactions Table
CREATE TABLE document_interactions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES conversation_sessions(session_id) ON DELETE CASCADE,
    document_id VARCHAR(255) NOT NULL,
    document_name VARCHAR(255) NOT NULL,
    interaction_type VARCHAR(50) NOT NULL, -- 'question', 'reference', 'analysis'
    interaction_count INTEGER DEFAULT 1,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relevance_score FLOAT DEFAULT 0.0
);

-- ============================================
-- DOCUMENT PROCESSING TABLES
-- ============================================

-- Document Processing Jobs Table
CREATE TABLE document_processing_jobs (
    job_id VARCHAR(255) PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    document_type VARCHAR(50) NOT NULL, -- 'instruction', 'content'
    processing_status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    task_type VARCHAR(50), -- 'edit', 'combine', 'generate_whitepaper', 'create_new_document'
    manifesto_category VARCHAR(100),
    token_tier INTEGER, -- 1, 2, 3, 4
    tokens_used INTEGER DEFAULT 0,
    processing_start TIMESTAMP WITH TIME ZONE,
    processing_end TIMESTAMP WITH TIME ZONE,
    output_filename VARCHAR(255),
    output_path TEXT,
    quality_score FLOAT,
    error_message TEXT,
    processing_metadata JSONB DEFAULT '{}'
);

-- Quality Control Reviews Table
CREATE TABLE quality_control_reviews (
    id SERIAL PRIMARY KEY,
    job_id VARCHAR(255) REFERENCES document_processing_jobs(job_id) ON DELETE CASCADE,
    reviewer_agent VARCHAR(100) NOT NULL,
    review_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    manifesto_alignment_score FLOAT NOT NULL,
    voice_consistency_score FLOAT NOT NULL,
    factual_accuracy_score FLOAT NOT NULL,
    overall_quality_score FLOAT NOT NULL,
    review_comments TEXT,
    suggestions JSONB DEFAULT '[]',
    approved BOOLEAN DEFAULT FALSE
);

-- ============================================
-- MANIFESTO AND CONTEXT TABLES
-- ============================================

-- Manifesto Context Cache Table
CREATE TABLE manifesto_context_cache (
    id SERIAL PRIMARY KEY,
    context_key VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100),
    token_tier INTEGER,
    context_content TEXT NOT NULL,
    token_count INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 0
);

-- Category Supplements Table
CREATE TABLE category_supplements (
    id SERIAL PRIMARY KEY,
    category_name VARCHAR(100) UNIQUE NOT NULL,
    supplement_content TEXT NOT NULL,
    token_count INTEGER NOT NULL,
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============================================
-- ANALYTICS AND MONITORING TABLES
-- ============================================

-- System Performance Metrics Table
CREATE TABLE system_performance_metrics (
    id SERIAL PRIMARY KEY,
    metric_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metric_type VARCHAR(50) NOT NULL, -- 'processing_time', 'token_usage', 'quality_score', 'error_rate'
    metric_value FLOAT NOT NULL,
    job_id VARCHAR(255),
    session_id VARCHAR(255),
    additional_context JSONB DEFAULT '{}'
);

-- User Feedback Table
CREATE TABLE user_feedback (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES conversation_sessions(session_id) ON DELETE CASCADE,
    job_id VARCHAR(255) REFERENCES document_processing_jobs(job_id) ON DELETE CASCADE,
    feedback_type VARCHAR(50) NOT NULL, -- 'rating', 'comment', 'correction'
    feedback_value TEXT NOT NULL,
    rating INTEGER, -- 1-10 rating
    feedback_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE
);

-- Error Logs Table
CREATE TABLE error_logs (
    id SERIAL PRIMARY KEY,
    error_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    job_id VARCHAR(255),
    session_id VARCHAR(255),
    component VARCHAR(100), -- 'n8n', 'mcp-server', 'chat-interface'
    severity VARCHAR(20) DEFAULT 'error', -- 'info', 'warning', 'error', 'critical'
    resolved BOOLEAN DEFAULT FALSE,
    resolution_notes TEXT
);

-- ============================================
-- INDEXES FOR PERFORMANCE
-- ============================================

-- Conversation-related indexes
CREATE INDEX idx_conversation_sessions_user_id ON conversation_sessions(user_id);
CREATE INDEX idx_conversation_sessions_last_activity ON conversation_sessions(last_activity);
CREATE INDEX idx_conversation_history_session_id ON conversation_history(session_id);
CREATE INDEX idx_conversation_history_timestamp ON conversation_history(timestamp);
CREATE INDEX idx_document_interactions_session_id ON document_interactions(session_id);
CREATE INDEX idx_document_interactions_document_id ON document_interactions(document_id);

-- Document processing indexes
CREATE INDEX idx_document_processing_jobs_status ON document_processing_jobs(processing_status);
CREATE INDEX idx_document_processing_jobs_type ON document_processing_jobs(document_type);
CREATE INDEX idx_document_processing_jobs_category ON document_processing_jobs(manifesto_category);
CREATE INDEX idx_quality_control_reviews_job_id ON quality_control_reviews(job_id);

-- Performance and monitoring indexes
CREATE INDEX idx_system_performance_metrics_timestamp ON system_performance_metrics(metric_timestamp);
CREATE INDEX idx_system_performance_metrics_type ON system_performance_metrics(metric_type);
CREATE INDEX idx_error_logs_timestamp ON error_logs(error_timestamp);
CREATE INDEX idx_error_logs_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_severity ON error_logs(severity);

-- ============================================
-- VIEWS FOR COMMON QUERIES
-- ============================================

-- Active Sessions View
CREATE VIEW active_sessions AS
SELECT 
    session_id,
    user_id,
    conversation_theme,
    last_activity,
    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_activity))/60 as minutes_since_last_activity
FROM conversation_sessions
WHERE last_activity > CURRENT_TIMESTAMP - INTERVAL '24 hours'
ORDER BY last_activity DESC;

-- Processing Performance View
CREATE VIEW processing_performance AS
SELECT 
    DATE(processing_start) as processing_date,
    COUNT(*) as total_jobs,
    AVG(EXTRACT(EPOCH FROM (processing_end - processing_start))) as avg_processing_time_seconds,
    AVG(tokens_used) as avg_tokens_used,
    AVG(quality_score) as avg_quality_score,
    COUNT(CASE WHEN processing_status = 'failed' THEN 1 END) as failed_jobs
FROM document_processing_jobs
WHERE processing_start IS NOT NULL
GROUP BY DATE(processing_start)
ORDER BY processing_date DESC;

-- Quality Metrics View
CREATE VIEW quality_metrics AS
SELECT 
    qcr.reviewer_agent,
    COUNT(*) as total_reviews,
    AVG(qcr.manifesto_alignment_score) as avg_manifesto_alignment,
    AVG(qcr.voice_consistency_score) as avg_voice_consistency,
    AVG(qcr.factual_accuracy_score) as avg_factual_accuracy,
    AVG(qcr.overall_quality_score) as avg_overall_quality,
    COUNT(CASE WHEN qcr.approved = true THEN 1 END) as approved_count
FROM quality_control_reviews qcr
GROUP BY qcr.reviewer_agent;

-- ============================================
-- FUNCTIONS FOR COMMON OPERATIONS
-- ============================================

-- Function to update session activity
CREATE OR REPLACE FUNCTION update_session_activity(p_session_id VARCHAR(255))
RETURNS VOID AS $$
BEGIN
    UPDATE conversation_sessions 
    SET last_activity = CURRENT_TIMESTAMP 
    WHERE session_id = p_session_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get conversation context
CREATE OR REPLACE FUNCTION get_conversation_context(p_session_id VARCHAR(255), p_limit INTEGER DEFAULT 5)
RETURNS TABLE(
    user_message TEXT,
    system_response TEXT,
    timestamp TIMESTAMP WITH TIME ZONE,
    documents_referenced JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ch.user_message,
        ch.system_response,
        ch.timestamp,
        ch.documents_referenced
    FROM conversation_history ch
    WHERE ch.session_id = p_session_id
    ORDER BY ch.timestamp DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate quality metrics
CREATE OR REPLACE FUNCTION calculate_daily_quality_metrics(p_date DATE)
RETURNS TABLE(
    processing_date DATE,
    total_documents INTEGER,
    avg_quality_score FLOAT,
    avg_manifesto_alignment FLOAT,
    avg_processing_time FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p_date as processing_date,
        COUNT(dpj.job_id)::INTEGER as total_documents,
        AVG(dpj.quality_score) as avg_quality_score,
        AVG(qcr.manifesto_alignment_score) as avg_manifesto_alignment,
        AVG(EXTRACT(EPOCH FROM (dpj.processing_end - dpj.processing_start))) as avg_processing_time
    FROM document_processing_jobs dpj
    LEFT JOIN quality_control_reviews qcr ON dpj.job_id = qcr.job_id
    WHERE DATE(dpj.processing_start) = p_date
    AND dpj.processing_status = 'completed';
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- INITIAL DATA
-- ============================================

-- Insert default categories
INSERT INTO category_supplements (category_name, supplement_content, token_count, priority) VALUES
('healthcare', 'Universal healthcare policy supplement content will be loaded here', 0, 1),
('education', 'Free education policy supplement content will be loaded here', 0, 2),
('economic_policy', 'Economic policy and ASTF supplement content will be loaded here', 0, 3),
('housing', 'Public housing policy supplement content will be loaded here', 0, 4),
('jobs_automation', 'Automation and worker dignity supplement content will be loaded here', 0, 5),
('constitutional_amendments', 'Constitutional amendment supplement content will be loaded here', 0, 6),
('ethics_accountability', 'Government integrity supplement content will be loaded here', 0, 7),
('rights_repair_grow', 'Rights to repair and grow supplement content will be loaded here', 0, 8),
('funding_revenue', 'Funding mechanisms supplement content will be loaded here', 0, 9);

-- ============================================
-- PERMISSIONS AND SECURITY
-- ============================================

-- Create roles for different components
CREATE ROLE n8n_worker;
CREATE ROLE mcp_server;
CREATE ROLE chat_interface;
CREATE ROLE analytics_readonly;

-- Grant appropriate permissions
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO n8n_worker;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO n8n_worker;

GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO mcp_server;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO mcp_server;

GRANT SELECT, INSERT, UPDATE ON conversation_sessions, conversation_history, document_interactions, user_feedback TO chat_interface;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO chat_interface;

GRANT SELECT ON ALL TABLES IN SCHEMA public TO analytics_readonly;

-- Connect back to main n8n database
\c n8n;

-- Ensure n8n user has proper permissions
GRANT ALL PRIVILEGES ON DATABASE n8n TO n8n_user;
GRANT ALL PRIVILEGES ON DATABASE political_conversations TO n8n_user;
GRANT ALL PRIVILEGES ON DATABASE manifesto_db TO n8n_user;

-- Set up manifesto database
\c manifesto_db;

-- Manifesto Documents Table
CREATE TABLE manifesto_documents (
    id SERIAL PRIMARY KEY,
    document_name VARCHAR(255) UNIQUE NOT NULL,
    document_type VARCHAR(50) NOT NULL, -- 'core_essence', 'style_guide', 'category_supplement', 'voice_guidelines'
    category VARCHAR(100),
    content TEXT NOT NULL,
    token_count INTEGER NOT NULL,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Manifesto Relationships Table
CREATE TABLE manifesto_relationships (
    id SERIAL PRIMARY KEY,
    parent_document_id INTEGER REFERENCES manifesto_documents(id),
    child_document_id INTEGER REFERENCES manifesto_documents(id),
    relationship_type VARCHAR(50) NOT NULL, -- 'extends', 'references', 'complements'
    strength FLOAT DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for manifesto database
CREATE INDEX idx_manifesto_documents_type ON manifesto_documents(document_type);
CREATE INDEX idx_manifesto_documents_category ON manifesto_documents(category);
CREATE INDEX idx_manifesto_documents_active ON manifesto_documents(is_active);

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO n8n_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO n8n_user;