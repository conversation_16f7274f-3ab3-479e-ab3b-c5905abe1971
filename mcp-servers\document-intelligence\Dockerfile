FROM node:18-alpine

# Install system dependencies for document processing
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    curl \
    poppler-utils \
    unoconv \
    libreoffice

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p /app/logs

# Create directories for document processing
RUN mkdir -p /app/temp /app/processed

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port for health checks
EXPOSE 8089

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8089/health || exit 1

# Start the server
CMD ["node", "server.js"]