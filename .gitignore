# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# claude mcp severs
.mcp.json
.clude.json

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Claude and AI development configuration
.claude.json
.claude/
.cursor/
.windsurf/
.mcp.json
.clinerules-*
CLAUDE-CODE-*.md
*-setup-info.md
*-validation-report.md

# Logs
logs
*.log

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Build outputs
dist/
build/
out/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Database
*.db
*.sqlite
*.sqlite3

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Taskmaster specific
.taskmaster/cache/
.taskmaster/logs/
.taskmaster/temp/

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar
*.tar.gz
*.rar

# Document converter specific
convertor/__pycache__/
convertor/*.pyc
convertor/temp/
convertor/output/
convertor/cache/

# n8n specific
.n8n/
n8n-config/
n8n-data/
database-data/
redis-data/
chroma-data/
n8n*.log

# API Keys and secrets
secrets.json
api-keys.json
.env.production

# Docker volumes and data
postgres-data/
chromadb-data/
redis-data/
n8n-userdata/

# Monitoring and metrics
grafana-data/
prometheus-data/

# MCP Server data
mcp-servers/political-content/data/
mcp-servers/research-integration/cache/
mcp-servers/manifesto-context/contexts/
mcp-servers/quality-control/reports/
mcp-servers/document-processing/temp/
mcp-servers/conversation-memory/sessions/
mcp-servers/voice-generation/outputs/
mcp-servers/workflow-orchestration/logs/

# AI model cache and temporary files
.cache/
model-cache/
embeddings-cache/

# ChromaDB files
chroma.sqlite3
chroma-collections/

# Vector database files
*.index
*.faiss
*.ann

# Sensitive political content (if any)
sensitive/
private/
confidential/

# White papers and document processing
white_papers_markdown/
white_papers_markdown*/
"n8n docs/"
"white papers/"
*.docx
*.pdf
*.txt

# Development and testing
dev-data/
test-data/
mock-data/

# chat logs folder
/n8n_workflow_windows\claude-code-chat-logs/
