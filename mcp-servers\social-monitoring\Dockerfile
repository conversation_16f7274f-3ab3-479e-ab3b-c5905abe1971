FROM node:18-alpine

# Create app directory
WORKDIR /app

# Create logs directory
RUN mkdir -p /app/logs

# Install security updates and required packages
RUN apk update && apk upgrade && apk add --no-cache \
    dumb-init \
    curl \
    python3 \
    make \
    g++ \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    && rm -rf /var/cache/apk/*

# Tell Puppeteer to skip installing Chromium since we already have it
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcpuser -u 1001 -G nodejs

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY . .

# Create scripts directory and trend refresh script
RUN mkdir -p scripts && \
    echo '#!/usr/bin/env node\nconsole.log("Trend refresh script placeholder");' > scripts/refresh-trends.js && \
    chmod +x scripts/refresh-trends.js

# Set proper permissions
RUN chown -R mcpuser:nodejs /app && \
    chmod +x server.js

# Switch to non-root user
USER mcpuser

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8092/health || exit 1

# Expose port
EXPOSE 8092

# Environment variables documentation
ENV MCP_SERVER_PORT=8092 \
    LOG_LEVEL=info \
    NODE_ENV=production \
    CACHE_TTL=1800 \
    MAX_POSTS_PER_QUERY=1000

# Labels for metadata
LABEL maintainer="Beau Lewis" \
      description="Social Monitoring MCP Server" \
      version="1.0.0" \
      org.opencontainers.image.title="Social Monitoring MCP Server" \
      org.opencontainers.image.description="Comprehensive social media monitoring, sentiment analysis, and influence network analysis" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.authors="Beau Lewis" \
      org.opencontainers.image.source="https://github.com/beau-lewis/political-document-system" \
      org.opencontainers.image.licenses="MIT"

# Start the application
CMD ["node", "server.js"]