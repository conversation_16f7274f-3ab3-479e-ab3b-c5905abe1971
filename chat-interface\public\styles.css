/* Political Document Processing System Styles */

:root {
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --accent-red: #dc2626;
    --success-green: #059669;
    --warning-yellow: #d97706;
    --neutral-gray: #6b7280;
    --light-gray: #f3f4f6;
    --white: #ffffff;
    --text-dark: #1f2937;
    --border-color: #d1d5db;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-gray);
    color: var(--text-dark);
    line-height: 1.6;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--white);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.header h2 {
    font-size: 1.1rem;
    font-weight: 400;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--warning-yellow);
}

.status-indicator.connected {
    background-color: var(--success-green);
}

.status-indicator.disconnected {
    background-color: var(--accent-red);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    gap: 1rem;
    padding: 1rem;
}

/* Sidebar */
.sidebar {
    width: 350px;
    background: var(--white);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.sidebar-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-section:last-child {
    border-bottom: none;
    flex: 1;
}

.sidebar-section h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Form Styles */
.document-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--secondary-blue);
}

/* Buttons */
.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-primary:hover {
    background: var(--secondary-blue);
}

.btn-send {
    background: var(--secondary-blue);
    color: var(--white);
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-send:hover {
    background: var(--primary-blue);
}

/* Active Jobs */
.active-jobs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.no-jobs {
    color: var(--neutral-gray);
    font-style: italic;
    text-align: center;
    padding: 1rem;
}

.job-item {
    background: var(--light-gray);
    padding: 0.75rem;
    border-radius: 4px;
    border-left: 3px solid var(--secondary-blue);
}

.job-item.processing {
    border-left-color: var(--warning-yellow);
}

.job-item.completed {
    border-left-color: var(--success-green);
}

.job-item.failed {
    border-left-color: var(--accent-red);
}

/* Chat Area */
.chat-area {
    flex: 1;
    background: var(--white);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    color: var(--primary-blue);
    font-size: 1.1rem;
    font-weight: 600;
}

.typing-indicator {
    color: var(--neutral-gray);
    font-style: italic;
    font-size: 0.9rem;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: calc(100vh - 300px);
}

/* Messages */
.message {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    max-width: 80%;
}

.message.user-message {
    align-self: flex-end;
}

.message.user-message .message-content {
    background: var(--secondary-blue);
    color: var(--white);
    border-radius: 18px 18px 4px 18px;
}

.message.ai-message .message-content {
    background: var(--light-gray);
    color: var(--text-dark);
    border-radius: 18px 18px 18px 4px;
}

.message.system-message .message-content {
    background: #fef3c7;
    color: var(--text-dark);
    border-radius: 8px;
    border-left: 3px solid var(--warning-yellow);
}

.message-content {
    padding: 0.75rem 1rem;
    line-height: 1.5;
}

.message-content p {
    margin-bottom: 0.5rem;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-time {
    font-size: 0.75rem;
    color: var(--neutral-gray);
    text-align: right;
}

.message.user-message .message-time {
    text-align: right;
}

.message.ai-message .message-time,
.message.system-message .message-time {
    text-align: left;
}

/* Chat Input */
.chat-input-area {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
}

.chat-input-container {
    display: flex;
    gap: 0;
}

#chatInput {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    font-size: 0.9rem;
}

#chatInput:focus {
    outline: none;
    border-color: var(--secondary-blue);
}

/* Status Bar */
.status-bar {
    background: var(--white);
    padding: 0.75rem 2rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: var(--neutral-gray);
}

.status-item {
    display: flex;
    gap: 0.25rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--white);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--primary-blue);
    font-size: 1.1rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--neutral-gray);
}

.close:hover {
    color: var(--accent-red);
}

.modal-body {
    padding: 1.5rem;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: var(--secondary-blue);
    width: 0%;
    transition: width 0.3s ease;
}

.processing-status p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.processing-steps {
    margin-top: 1rem;
}

.step-item {
    padding: 0.5rem;
    border-left: 3px solid var(--light-gray);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.step-item.active {
    border-left-color: var(--warning-yellow);
    background: #fef3c7;
}

.step-item.completed {
    border-left-color: var(--success-green);
    background: #d1fae5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .chat-area {
        order: 1;
        min-height: 400px;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 0.5rem;
    }
}