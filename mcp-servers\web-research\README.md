# Web Research MCP Server

A comprehensive Model Context Protocol (MCP) server for web research functionality, providing advanced web scraping, search, content summarization, and RAG (Retrieval-Augmented Generation) integration capabilities.

## Overview

The Web Research MCP Server is designed to be a critical component of the n8n workflow system, providing essential tools for:

- **Web Search**: Multi-engine web search capabilities
- **Content Scraping**: Advanced website content extraction with multiple methods
- **Content Summarization**: AI-powered content summarization with various formats
- **Research Aggregation**: Intelligent aggregation of research from multiple sources
- **RAG Integration**: Vector search for document retrieval using ChromaDB

## Features

### 🔍 Web Search
- Multiple search engines support (DuckDuckGo, Google, Bing)
- Configurable result limits and filtering
- Safe search options
- Language and region targeting
- Rate limiting and caching

### 🌐 Website Scraping
- Multiple scraping methods:
  - **Cheerio**: Fast HTML parsing for static content
  - **Puppeteer**: JavaScript-enabled scraping for dynamic content
  - **Readability**: Clean content extraction focusing on main text
- Robots.txt compliance
- Custom headers support
- Link and image extraction
- Automatic rate limiting

### 📝 Content Summarization
- Multiple summary types:
  - **Extractive**: Key sentence extraction
  - **Abstractive**: AI-generated summaries
  - **Bullet Points**: Structured key points
  - **Key Facts**: Factual information extraction
- Configurable summary length
- Focus area targeting
- Metadata inclusion

### 🔬 Research Aggregation
- Comprehensive research reports from multiple sources
- Report types:
  - **Comprehensive**: Full analysis with introduction, findings, and conclusion
  - **Comparative**: Side-by-side analysis of different viewpoints
  - **Factual**: Focus on verifiable facts and statistics
  - **Analytical**: Cause-effect analysis with insights
- Citation management
- Fact-checking integration
- Source credibility scoring

### 🧠 RAG Integration
- ChromaDB vector storage for scraped content
- Semantic similarity search
- Document type filtering
- Date range and domain filtering
- Automatic content chunking and embedding
- Integration with existing political document collections

## MCP Tools

### 1. `web_search`
Search the web using multiple search engines.

**Parameters:**
- `query` (required): Search query
- `search_engine`: duckduckgo (default), google, bing
- `max_results`: Maximum results (1-50, default: 10)
- `language`: Language code (default: en)
- `region`: Region code (default: us)
- `safe_search`: Enable safe search (default: true)

### 2. `website_scraper`
Scrape and extract content from websites.

**Parameters:**
- `url` (required): URL to scrape
- `scraping_method`: cheerio (default), puppeteer, readability
- `extract_links`: Extract all page links (default: false)
- `extract_images`: Extract image URLs (default: false)
- `wait_for_selector`: CSS selector to wait for (Puppeteer only)
- `custom_headers`: Custom HTTP headers
- `respect_robots`: Respect robots.txt (default: true)

### 3. `content_summarizer`
Summarize scraped web content using AI.

**Parameters:**
- `content` (required): Content to summarize
- `summary_type`: extractive (default), abstractive, bullet_points, key_facts
- `max_length`: Maximum summary length in words (100-2000, default: 500)
- `focus_area`: Specific topic to focus on
- `include_metadata`: Include analysis metadata (default: true)

### 4. `research_aggregator`
Aggregate research from multiple sources.

**Parameters:**
- `topic` (required): Research topic or question
- `sources` (required): Array of source materials
- `report_type`: comprehensive (default), comparative, factual, analytical
- `include_citations`: Include source references (default: true)
- `fact_check`: Perform fact-checking (default: false)

### 5. `retrieve_relevant_documents`
RAG vector search for document retrieval.

**Parameters:**
- `query` (required): Search query
- `document_type`: web_content, research_report, scraped_article, summary
- `limit`: Maximum results (1-20, default: 5)
- `similarity_threshold`: Minimum similarity score (0.0-1.0, default: 0.7)
- `date_range`: Filter by date range
- `domain_filter`: Filter by domain names

### 6. `index_web_content`
Index scraped content for future RAG retrieval.

**Parameters:**
- `url` (required): Source URL
- `title`: Content title
- `content` (required): Full text content
- `metadata`: Additional metadata (author, publish_date, etc.)

### 7. `bulk_web_research`
Perform bulk research operations.

**Parameters:**
- `queries`: Array of search queries
- `urls`: Array of URLs to scrape
- `auto_summarize`: Auto-summarize content (default: true)
- `auto_index`: Auto-index for RAG (default: true)

## Installation

### Docker Deployment (Recommended)

```bash
# Build the container
docker build -t web-research-mcp .

# Run with environment variables
docker run -d \
  --name web-research-mcp \
  --network n8n-network \
  -p 8089:8089 \
  -e OPENAI_API_KEY=your_openai_key \
  -e CHROMADB_URL=http://chromadb:8000 \
  -e POSTGRES_HOST=postgresql \
  -e POSTGRES_DB=political_conversations \
  -e POSTGRES_USER=n8n_user \
  -e POSTGRES_PASSWORD=secure_password \
  -e REDIS_HOST=redis \
  -v /app/logs:/app/logs \
  web-research-mcp
```

### Local Development

```bash
# Install dependencies
npm install

# Set environment variables
export OPENAI_API_KEY=your_openai_key
export CHROMADB_URL=http://localhost:8000
export POSTGRES_HOST=localhost
export POSTGRES_DB=political_conversations
export POSTGRES_USER=n8n_user
export POSTGRES_PASSWORD=secure_password
export REDIS_HOST=localhost

# Start the server
npm start

# Or development mode with auto-reload
npm run dev
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENAI_API_KEY` | OpenAI API key for embeddings and summarization | Required |
| `CHROMADB_URL` | ChromaDB server URL | `http://chromadb:8000` |
| `POSTGRES_HOST` | PostgreSQL host | `postgresql` |
| `POSTGRES_PORT` | PostgreSQL port | `5432` |
| `POSTGRES_DB` | PostgreSQL database | `political_conversations` |
| `POSTGRES_USER` | PostgreSQL user | `n8n_user` |
| `POSTGRES_PASSWORD` | PostgreSQL password | `secure_password` |
| `REDIS_HOST` | Redis host for caching | `redis` |
| `REDIS_PORT` | Redis port | `6379` |
| `REDIS_PASSWORD` | Redis password | - |
| `MCP_SERVER_PORT` | Health endpoint port | `8089` |

## Architecture

### Components

1. **Web Search Engine**: Multi-provider search with result aggregation
2. **Content Scraper**: Multiple scraping strategies for different content types
3. **AI Summarizer**: OpenAI-powered content summarization
4. **Research Aggregator**: Intelligent multi-source research compilation
5. **Vector Store**: ChromaDB integration for semantic search
6. **Cache Layer**: Redis for performance optimization
7. **Database Layer**: PostgreSQL for metadata and tracking

### Data Flow

```
Web Search → Content Scraping → Summarization → Vector Indexing → RAG Retrieval
     ↓              ↓               ↓              ↓              ↓
   Cache         Cache         AI Processing   ChromaDB      Search Results
```

### Security Features

- Robots.txt compliance
- Rate limiting
- User-Agent rotation
- Request timeout protection
- Input validation and sanitization
- Non-root container execution

## Integration with n8n Workflow

This server provides the critical `retrieve_relevant_documents` tool mentioned in the n8n workflow guide, enabling:

- **Context-aware document retrieval** for political content generation
- **Research-backed policy analysis** with source tracking
- **Multi-source fact verification** for content accuracy
- **Automated content curation** from web sources

### Example Workflow Integration

```javascript
// In n8n workflow
const webResearch = await mcp.callTool('web_search', {
  query: 'climate change policy 2024',
  max_results: 10
});

const scrapedContent = await mcp.callTool('website_scraper', {
  url: webResearch.results[0].url,
  scraping_method: 'puppeteer'
});

const summary = await mcp.callTool('content_summarizer', {
  content: scrapedContent.content,
  summary_type: 'key_facts',
  focus_area: 'climate policy'
});

await mcp.callTool('index_web_content', {
  url: scrapedContent.url,
  title: scrapedContent.title,
  content: scrapedContent.content,
  metadata: {
    topic: 'climate_policy',
    source_type: 'government'
  }
});
```

## Performance Considerations

- **Concurrent Request Limiting**: Configured rate limiting prevents overwhelming target sites
- **Caching Strategy**: Redis caching for frequently accessed content
- **Chunked Processing**: Large documents are automatically chunked for vector storage
- **Memory Management**: Puppeteer instances are properly managed and cleaned up
- **Database Optimization**: Indexed queries for fast document retrieval

## Monitoring and Health Checks

The server provides a health endpoint at `/health` that returns:

```json
{
  "status": "healthy",
  "service": "web-research-mcp",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "chromadb": "connected",
  "browser": "ready"
}
```

## Error Handling

- Graceful degradation when external services are unavailable
- Comprehensive logging with Winston
- Automatic retry logic for transient failures
- Detailed error responses with context
- Fallback strategies for different scraping methods

## Development

### Running Tests

```bash
npm test
```

### Code Structure

```
src/
├── server.js              # Main MCP server implementation
├── package.json           # Dependencies and scripts
├── Dockerfile             # Container configuration
└── README.md              # This documentation
```

### Contributing

1. Follow existing code style and patterns
2. Add comprehensive error handling
3. Include appropriate logging
4. Update tests for new functionality
5. Document new tools and parameters

## License

MIT License - See LICENSE file for details.

## Support

For issues and questions:
- Check the health endpoint: `http://localhost:8089/health`
- Review logs: `/app/logs/web-research.log`
- Verify environment variables
- Ensure required services (ChromaDB, PostgreSQL, Redis) are running