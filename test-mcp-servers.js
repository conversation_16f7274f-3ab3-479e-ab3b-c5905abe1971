#!/usr/bin/env node

console.log('Testing MCP Server Configurations...\n');

const fs = require('fs');
const path = require('path');

// Test n8n MCP Server
console.log('1. n8n MCP Server:');
const n8nPath = path.join(__dirname, 'mcp-servers', 'n8n-mcp-server', 'build', 'index.js');
console.log('   Path:', n8nPath);
if (fs.existsSync(n8nPath)) {
    console.log('   ✓ Build file exists');
    console.log('   Note: Requires N8N_API_URL and N8N_API_KEY environment variables');
} else {
    console.log('   ✗ Build file not found');
}

console.log('\n2. Vibe Coder MCP:');
const vibePath = '/mnt/c/vibe-coder-mcp/build/index.js';
console.log('   Path:', vibePath);
if (fs.existsSync(vibePath)) {
    console.log('   ✓ Build file exists');
    // Check for .env file
    const envPath = '/mnt/c/vibe-coder-mcp/.env';
    if (fs.existsSync(envPath)) {
        console.log('   ✓ .env file exists with API keys');
    }
} else {
    console.log('   ✗ Build file not found');
}

console.log('\n3. MCP Configuration Files:');
const configs = [
    path.join(__dirname, '.cursor', 'mcp.json'),
    path.join(__dirname, '.windsurf', 'mcp.json')
];

configs.forEach(configPath => {
    const configName = path.relative(__dirname, configPath);
    if (fs.existsSync(configPath)) {
        console.log(`   ✓ ${configName} exists`);
        try {
            const content = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            if (content.mcpServers && content.mcpServers['vibe-coder-mcp']) {
                console.log(`     - Vibe Coder MCP configured`);
            }
            if (content.mcpServers && content.mcpServers['n8n-mcp-server']) {
                console.log(`     - n8n MCP Server configured`);
            }
        } catch (e) {
            console.log(`     ✗ Error reading ${configName}: ${e.message}`);
        }
    } else {
        console.log(`   ✗ ${configName} not found`);
    }
});

console.log('\n✅ Setup complete! Both MCP servers are configured.');
console.log('\nNext steps:');
console.log('1. Update N8N_API_KEY in the MCP configuration files');
console.log('2. Restart your IDE (Cursor or Windsurf) to load the MCP servers');
console.log('3. The servers will appear in the MCP panel of your IDE');