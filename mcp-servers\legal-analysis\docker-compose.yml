version: '3.8'

services:
  legal-analysis:
    build: .
    ports:
      - "8091:8091"
    environment:
      - NODE_ENV=development
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=legal_analysis
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=n8n_secure_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - HEALTH_PORT=8091
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - ./temp:/app/temp
      - ./processed:/app/processed
    depends_on:
      - postgres
      - redis
    networks:
      - legal-analysis-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=legal_analysis
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=n8n_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - legal-analysis-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - legal-analysis-network
    restart: unless-stopped

  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - legal-analysis-network
    profiles:
      - tools

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - legal-analysis-network
    profiles:
      - tools

volumes:
  postgres_data:
  redis_data:

networks:
  legal-analysis-network:
    driver: bridge