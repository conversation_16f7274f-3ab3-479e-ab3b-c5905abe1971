#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import { withCircuitBreaker, logStructuredError, getHealthCheck } from '../shared/error-handling.js';

/**
 * Manifesto Context MCP Server
 * Provides dynamic context loading and token allocation for Beau Lewis political documents
 */

class ManifestoContextServer {
  constructor() {
    this.server = new MCPServer({
      name: 'manifesto-context',
      version: '1.0.0'
    });
    
    this.manifestoPath = process.env.MANIFESTO_PATH || '/app/manifesto';
    this.tokenizer = encoding_for_model('gpt-4');
    this.contextCache = new Map();
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    this.setupTools();
    this.setupResources();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'manifesto_db',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      // Connect to PostgreSQL with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.pgClient.connect();
      });
      
      // Connect to Redis with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.redisClient.connect();
      });
      
      console.log('Connected to PostgreSQL and Redis');
      
      await this.loadManifestoDocuments();
    } catch (error) {
      logStructuredError(error, 'ManifestoContextServer.initialize', {
        component: 'database_connection'
      });
      throw error;
    }
  }

  setupTools() {
    // Tool: Get Context by Token Tier
    this.server.addTool({
      name: 'get_context_by_tier',
      description: 'Get appropriate manifesto context based on token tier (1=5K, 2=10K, 3=25K, 4=50K)',
      inputSchema: {
        type: 'object',
        properties: {
          tier: {
            type: 'integer',
            minimum: 1,
            maximum: 4,
            description: 'Token tier: 1 (5K), 2 (10K), 3 (25K), 4 (50K)'
          },
          category: {
            type: 'string',
            description: 'Specific policy category (healthcare, education, etc.)',
            enum: ['healthcare', 'education', 'economic_policy', 'housing', 'jobs_automation', 'constitutional_amendments', 'ethics_accountability', 'rights_repair_grow', 'funding_revenue']
          },
          include_voice_guide: {
            type: 'boolean',
            default: true,
            description: 'Include voice guidelines in context'
          }
        },
        required: ['tier']
      }
    }, this.getContextByTier.bind(this));

    // Tool: Get Category Supplement
    this.server.addTool({
      name: 'get_category_supplement',
      description: 'Get detailed policy supplement for specific category',
      inputSchema: {
        type: 'object',
        properties: {
          category: {
            type: 'string',
            description: 'Policy category',
            enum: ['healthcare', 'education', 'economic_policy', 'housing', 'jobs_automation', 'constitutional_amendments', 'ethics_accountability', 'rights_repair_grow', 'funding_revenue']
          }
        },
        required: ['category']
      }
    }, this.getCategorySupplement.bind(this));

    // Tool: Search Manifesto Content
    this.server.addTool({
      name: 'search_manifesto_content',
      description: 'Search across all manifesto content for specific topics or phrases',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query'
          },
          max_results: {
            type: 'integer',
            default: 5,
            description: 'Maximum number of results to return'
          }
        },
        required: ['query']
      }
    }, this.searchManifestoContent.bind(this));

    // Tool: Get Voice Guidelines
    this.server.addTool({
      name: 'get_voice_guidelines',
      description: 'Get Beau Lewis voice and writing style guidelines',
      inputSchema: {
        type: 'object',
        properties: {
          document_type: {
            type: 'string',
            description: 'Type of document being created',
            enum: ['white_paper', 'policy_brief', 'speech', 'manifesto', 'talking_points']
          }
        }
      }
    }, this.getVoiceGuidelines.bind(this));

    // Tool: Calculate Context Tokens
    this.server.addTool({
      name: 'calculate_context_tokens',
      description: 'Calculate token count for given context combination',
      inputSchema: {
        type: 'object',
        properties: {
          components: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['core_essence', 'style_guide', 'voice_guidelines', 'category_supplement']
            },
            description: 'Context components to include'
          },
          category: {
            type: 'string',
            description: 'Category for supplement calculation'
          }
        },
        required: ['components']
      }
    }, this.calculateContextTokens.bind(this));
  }

  setupResources() {
    // Resource: Core Manifesto Documents
    this.server.addResource({
      uri: 'manifesto://core_essence',
      name: 'Core Political Essence',
      description: 'Fundamental beliefs and vision of Beau Lewis',
      mimeType: 'text/markdown'
    });

    this.server.addResource({
      uri: 'manifesto://style_guide',
      name: 'Writing Style Guide',
      description: 'Concise writing guidelines for political documents',
      mimeType: 'text/markdown'
    });

    this.server.addResource({
      uri: 'manifesto://voice_guidelines',
      name: 'Voice Guidelines',
      description: 'Detailed voice and tone guidance for authentic writing',
      mimeType: 'text/markdown'
    });

    this.server.addResource({
      uri: 'manifesto://manifesto_for_agents',
      name: 'Agent Manifesto',
      description: 'Complete guidance for AI agents working on political content',
      mimeType: 'text/markdown'
    });
  }

  async loadManifestoDocuments() {
    const manifestoFiles = [
      'core_essence.md',
      'style_guide.md', 
      'voice_guidelines_beau_lewis.md',
      'manifesto_for_agents.md'
    ];

    const categoryFiles = [
      'category_supplement_healthcare.md',
      'category_supplement_education.md',
      'category_supplement_economic_policy.md',
      'category_supplement_housing.md',
      'category_supplement_jobs_automation.md',
      'category_supplement_constitutional_amendments.md',
      'category_supplement_ethics_accountability.md',
      'category_supplement_rights_repair_grow.md',
      'category_supplement_funding_revenue.md'
    ];

    try {
      // Load core manifesto documents
      for (const file of manifestoFiles) {
        const filePath = path.join(this.manifestoPath, file);
        if (await fs.pathExists(filePath)) {
          const content = await fs.readFile(filePath, 'utf8');
          const tokens = this.tokenizer.encode(content).length;
          
          const docName = file.replace('.md', '').replace('_beau_lewis', '');
          this.contextCache.set(docName, {
            content,
            tokens,
            type: 'core'
          });

          // Store in database with circuit breaker protection
          await this.storeDocument(docName, 'core', content, tokens);
        }
      }

      // Load category supplements
      for (const file of categoryFiles) {
        const filePath = path.join(this.manifestoPath, file);
        if (await fs.pathExists(filePath)) {
          const content = await fs.readFile(filePath, 'utf8');
          const tokens = this.tokenizer.encode(content).length;
          
          const category = file.replace('category_supplement_', '').replace('.md', '');
          this.contextCache.set(`category_${category}`, {
            content,
            tokens,
            type: 'category',
            category
          });

          // Store in database with circuit breaker protection
          await this.storeDocument(`category_${category}`, 'category_supplement', content, tokens, category);
        }
      }

      console.log(`Loaded ${this.contextCache.size} manifesto documents`);
    } catch (error) {
      logStructuredError(error, 'ManifestoContextServer.loadManifestoDocuments', {
        manifestoPath: this.manifestoPath,
        totalFiles: manifestoFiles.length + categoryFiles.length
      });
      throw error;
    }
  }

  async storeDocument(name, type, content, tokens, category = null) {
    try {
      await withCircuitBreaker('database', async () => {
        await this.pgClient.query(`
          INSERT INTO manifesto_documents (document_name, document_type, category, content, token_count)
          VALUES ($1, $2, $3, $4, $5)
          ON CONFLICT (document_name) 
          DO UPDATE SET content = $4, token_count = $5, updated_at = CURRENT_TIMESTAMP
        `, [name, type, category, content, tokens]);
      });
    } catch (error) {
      logStructuredError(error, 'ManifestoContextServer.storeDocument', {
        document_name: name,
        document_type: type,
        category: category
      });
    }
  }

  async getContextByTier({ tier, category, include_voice_guide = true }) {
    const tokenLimits = {
      1: 5000,   // Tier 1: 5K tokens
      2: 10000,  // Tier 2: 10K tokens  
      3: 25000,  // Tier 3: 25K tokens
      4: 50000   // Tier 4: 50K tokens
    };

    const tokenLimit = tokenLimits[tier];
    if (!tokenLimit) {
      throw new Error(`Invalid tier: ${tier}. Must be 1-4.`);
    }

    let context = '';
    let totalTokens = 0;

    // Always include core essence and style guide (essential context)
    const coreEssence = this.contextCache.get('core_essence');
    const styleGuide = this.contextCache.get('style_guide');

    if (coreEssence && styleGuide) {
      context += `${coreEssence.content}\n\n---\n\n${styleGuide.content}`;
      totalTokens += coreEssence.tokens + styleGuide.tokens;
    }

    // Include voice guidelines if requested and space allows
    if (include_voice_guide) {
      const voiceGuide = this.contextCache.get('voice_guidelines');
      if (voiceGuide && (totalTokens + voiceGuide.tokens) <= tokenLimit) {
        context += `\n\n---\n\n${voiceGuide.content}`;
        totalTokens += voiceGuide.tokens;
      }
    }

    // Include category supplement if specified and space allows
    if (category) {
      const categorySupplement = this.contextCache.get(`category_${category}`);
      if (categorySupplement && (totalTokens + categorySupplement.tokens) <= tokenLimit) {
        context += `\n\n---\n\n${categorySupplement.content}`;
        totalTokens += categorySupplement.tokens;
      }
    }

    // For higher tiers, include additional context
    if (tier >= 3) {
      const agentManifesto = this.contextCache.get('manifesto_for_agents');
      if (agentManifesto && (totalTokens + agentManifesto.tokens) <= tokenLimit) {
        context += `\n\n---\n\n${agentManifesto.content}`;
        totalTokens += agentManifesto.tokens;
      }
    }

    return {
      context,
      totalTokens,
      tier,
      tokenLimit,
      components: this.getIncludedComponents(context)
    };
  }

  async getCategorySupplement({ category }) {
    const supplement = this.contextCache.get(`category_${category}`);
    if (!supplement) {
      throw new Error(`Category supplement not found: ${category}`);
    }

    return {
      category,
      content: supplement.content,
      tokens: supplement.tokens
    };
  }

  async searchManifestoContent({ query, max_results = 5 }) {
    const results = [];
    const queryLower = query.toLowerCase();

    for (const [name, doc] of this.contextCache.entries()) {
      const contentLower = doc.content.toLowerCase();
      if (contentLower.includes(queryLower)) {
        // Find all matches with context
        const lines = doc.content.split('\n');
        const matchingLines = lines
          .map((line, index) => ({ line, number: index + 1 }))
          .filter(({ line }) => line.toLowerCase().includes(queryLower));

        if (matchingLines.length > 0) {
          results.push({
            document: name,
            type: doc.type,
            category: doc.category || null,
            matches: matchingLines.slice(0, 3), // Limit to 3 matches per document
            totalMatches: matchingLines.length
          });
        }
      }
    }

    return results.slice(0, max_results);
  }

  async getVoiceGuidelines({ document_type }) {
    const voiceGuide = this.contextCache.get('voice_guidelines');
    if (!voiceGuide) {
      throw new Error('Voice guidelines not found');
    }

    // Extract relevant sections based on document type
    let relevantSections = voiceGuide.content;
    
    if (document_type) {
      // Could add logic to extract specific sections for document types
      // For now, return full guidelines with document type context
    }

    return {
      document_type,
      guidelines: relevantSections,
      tokens: voiceGuide.tokens
    };
  }

  async calculateContextTokens({ components, category }) {
    let totalTokens = 0;
    const breakdown = {};

    for (const component of components) {
      let doc;
      if (component === 'category_supplement' && category) {
        doc = this.contextCache.get(`category_${category}`);
      } else {
        doc = this.contextCache.get(component);
      }

      if (doc) {
        breakdown[component] = doc.tokens;
        totalTokens += doc.tokens;
      } else {
        breakdown[component] = 0;
      }
    }

    return {
      totalTokens,
      breakdown,
      recommendedTier: this.getRecommendedTier(totalTokens)
    };
  }

  getRecommendedTier(tokens) {
    if (tokens <= 5000) return 1;
    if (tokens <= 10000) return 2;
    if (tokens <= 25000) return 3;
    return 4;
  }

  getIncludedComponents(context) {
    const components = [];
    if (context.includes('# Core Political Essence')) components.push('core_essence');
    if (context.includes('# Style Guide')) components.push('style_guide');
    if (context.includes('# Voice Guidelines')) components.push('voice_guidelines');
    if (context.includes('# Manifesto for AI Agents')) components.push('manifesto_for_agents');
    return components;
  }

  async start() {
    await this.initialize();
    await this.server.start();
    console.log('Manifesto Context MCP Server started');
  }

  async stop() {
    try {
      if (this.pgClient) {
        await withCircuitBreaker('database', async () => {
          await this.pgClient.end();
        });
      }
      if (this.redisClient) {
        await withCircuitBreaker('database', async () => {
          await this.redisClient.quit();
        });
      }
      await this.server.stop();
    } catch (error) {
      logStructuredError(error, 'ManifestoContextServer.stop', {
        component: 'shutdown'
      });
    }
  }
}

// Start the server
const server = new ManifestoContextServer();

process.on('SIGINT', async () => {
  console.log('Shutting down...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);