{"dashboard": {"id": null, "title": "Political Document Analytics - Detailed Metrics", "tags": ["analytics", "documents", "mcp", "detailed"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "Real-time Document Generation Metrics", "type": "graph", "targets": [{"expr": "rate(political_documents_generated_total[1m])", "legendFormat": "Generation Rate (docs/min)", "refId": "A"}, {"expr": "political_documents_queue_size", "legendFormat": "<PERSON><PERSON> Size", "refId": "B"}, {"expr": "political_document_generation_errors_total", "legendFormat": "Generation Errors", "refId": "C"}], "yAxes": [{"label": "Documents", "show": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}}, {"id": 2, "title": "Document Type Distribution", "type": "piechart", "targets": [{"expr": "sum by (document_type) (political_documents_by_type_total)", "legendFormat": "{{document_type}}", "refId": "A"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "list", "placement": "right"}}}, {"id": 3, "title": "Vector Search Analytics", "type": "graph", "targets": [{"expr": "rate(chromadb_search_requests_total[5m])", "legendFormat": "Search Rate", "refId": "A"}, {"expr": "chromadb_collection_documents_count", "legendFormat": "Total Documents", "refId": "B"}, {"expr": "histogram_quantile(0.95, rate(chromadb_search_duration_seconds_bucket[5m]))", "legendFormat": "95th Percentile Search Time", "refId": "C"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "AI Model Performance Metrics", "type": "graph", "targets": [{"expr": "rate(ai_model_requests_total[5m])", "legendFormat": "{{model}} - Requests/sec", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(ai_model_response_time_seconds_bucket[5m]))", "legendFormat": "{{model}} - 95th Response Time", "refId": "B"}, {"expr": "ai_model_token_usage_total", "legendFormat": "{{model}} - Token Usage", "refId": "C"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Fact-Checking System Metrics", "type": "stat", "targets": [{"expr": "rate(fact_checks_performed_total[5m])", "legendFormat": "Fact Checks/min", "refId": "A"}, {"expr": "fact_check_accuracy_score", "legendFormat": "Accuracy Score", "refId": "B"}, {"expr": "sum(rate(fact_check_results_total[5m])) by (result)", "legendFormat": "{{result}}", "refId": "C"}], "gridPos": {"h": 6, "w": 8, "x": 0, "y": 16}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.9}]}}}}, {"id": 6, "title": "Security Metrics", "type": "graph", "targets": [{"expr": "rate(oauth_authentication_requests_total[5m])", "legendFormat": "OAuth Requests", "refId": "A"}, {"expr": "rate(security_violations_total[5m])", "legendFormat": "Security Violations", "refId": "B"}, {"expr": "rate(rate_limit_exceeded_total[5m])", "legendFormat": "Rate Limit Violations", "refId": "C"}], "gridPos": {"h": 6, "w": 8, "x": 8, "y": 16}}, {"id": 7, "title": "System Performance Summary", "type": "table", "targets": [{"expr": "avg(rate(container_cpu_usage_seconds_total{name=~\".*political.*\"}[5m])) by (name)", "legendFormat": "{{name}}", "refId": "A", "format": "table"}], "gridPos": {"h": 6, "w": 8, "x": 16, "y": 16}, "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"name": "Service", "Value": "CPU Usage (%)"}}}]}], "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m"]}, "refresh": "10s", "version": 0}}