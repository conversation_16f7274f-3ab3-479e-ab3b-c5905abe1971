#!/usr/bin/env node

import axios from 'axios';

/**
 * Vector Search Test Script
 * Tests the vector search functionality to ensure it's working properly
 */

class VectorSearchTester {
  constructor() {
    this.vectorSearchUrl = process.env.VECTOR_SEARCH_URL || 'http://localhost:8089';
    this.testPassed = 0;
    this.testFailed = 0;
  }

  async runTests() {
    console.log('🧪 Starting Vector Search Tests');
    console.log(`Vector Search URL: ${this.vectorSearchUrl}`);

    try {
      // Test 1: Health check
      await this.testHealthCheck();

      // Test 2: Collection stats
      await this.testCollectionStats();

      // Test 3: Search similar content
      await this.testSearchSimilarContent();

      // Test 4: Get document context
      await this.testGetDocumentContext();

      // Test 5: Index and search a test document
      await this.testIndexAndSearch();

      console.log(`\n🎯 Test Summary:`);
      console.log(`✅ Tests Passed: ${this.testPassed}`);
      console.log(`❌ Tests Failed: ${this.testFailed}`);
      
      if (this.testFailed === 0) {
        console.log('🎉 All tests passed!');
        process.exit(0);
      } else {
        console.log('💥 Some tests failed!');
        process.exit(1);
      }
    } catch (error) {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    }
  }

  async testHealthCheck() {
    try {
      console.log('\n🔍 Test 1: Health Check');
      const response = await axios.get(`${this.vectorSearchUrl}/health`);
      
      if (response.data.status === 'healthy') {
        console.log('✅ Health check passed');
        this.testPassed++;
      } else {
        console.log('❌ Health check failed: Service not healthy');
        this.testFailed++;
      }
    } catch (error) {
      console.log(`❌ Health check failed: ${error.message}`);
      this.testFailed++;
    }
  }

  async testCollectionStats() {
    try {
      console.log('\n🔍 Test 2: Collection Stats');
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_collection_stats',
        parameters: {}
      });
      
      if (response.data.success) {
        console.log(`✅ Collection stats retrieved: ${response.data.total_documents} documents`);
        this.testPassed++;
      } else {
        console.log('❌ Collection stats failed');
        this.testFailed++;
      }
    } catch (error) {
      console.log(`❌ Collection stats failed: ${error.message}`);
      this.testFailed++;
    }
  }

  async testSearchSimilarContent() {
    try {
      console.log('\n🔍 Test 3: Search Similar Content');
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'search_similar_content',
        parameters: {
          query: 'healthcare reform and universal coverage',
          category: 'healthcare',
          limit: 3
        }
      });
      
      if (response.data.success && response.data.results) {
        console.log(`✅ Search found ${response.data.results.length} similar documents`);
        if (response.data.results.length > 0) {
          console.log(`   Top result: ${response.data.results[0].metadata.title || 'Untitled'}`);
        }
        this.testPassed++;
      } else {
        console.log('❌ Search similar content failed');
        this.testFailed++;
      }
    } catch (error) {
      console.log(`❌ Search similar content failed: ${error.message}`);
      this.testFailed++;
    }
  }

  async testGetDocumentContext() {
    try {
      console.log('\n🔍 Test 4: Get Document Context');
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_document_context',
        parameters: {
          topic: 'education reform and student debt',
          category: 'education',
          token_limit: 5000
        }
      });
      
      if (response.data.success && response.data.context) {
        console.log(`✅ Document context retrieved: ${response.data.metadata.token_count} tokens`);
        console.log(`   Documents included: ${response.data.metadata.documents_included}`);
        this.testPassed++;
      } else {
        console.log('❌ Get document context failed');
        this.testFailed++;
      }
    } catch (error) {
      console.log(`❌ Get document context failed: ${error.message}`);
      this.testFailed++;
    }
  }

  async testIndexAndSearch() {
    try {
      console.log('\n🔍 Test 5: Index and Search Test Document');
      
      // Index a test document
      const testDocId = `test_doc_${Date.now()}`;
      const testContent = `
# Test Political Document

This is a test document for the political document processing system.

## Healthcare Policy

We believe in universal healthcare coverage for all Americans. This includes:
- Single-payer healthcare system
- Reduced prescription drug costs
- Mental health coverage
- Preventive care focus

## Education Reform

Our education policy focuses on:
- Free public education through college
- Student debt forgiveness
- Teacher pay increases
- Technology in classrooms

This test document should be indexed and searchable in the vector database.
      `;

      const indexResponse = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'index_document',
        parameters: {
          document_id: testDocId,
          content: testContent,
          metadata: {
            title: 'Test Political Document',
            category: 'healthcare',
            document_type: 'test',
            author: 'Test System'
          }
        }
      });

      if (indexResponse.data.success) {
        console.log(`✅ Test document indexed: ${indexResponse.data.chunk_count} chunks`);
        
        // Now search for the test document
        const searchResponse = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
          tool: 'search_similar_content',
          parameters: {
            query: 'universal healthcare coverage single-payer',
            limit: 5
          }
        });

        if (searchResponse.data.success && searchResponse.data.results) {
          const foundTest = searchResponse.data.results.some(result => 
            result.document_id === testDocId
          );
          
          if (foundTest) {
            console.log('✅ Test document found in search results');
            this.testPassed++;
          } else {
            console.log('❌ Test document not found in search results');
            this.testFailed++;
          }
          
          // Clean up: delete the test document
          await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
            tool: 'delete_document',
            parameters: {
              document_id: testDocId
            }
          });
          console.log('🧹 Test document cleaned up');
        } else {
          console.log('❌ Search after indexing failed');
          this.testFailed++;
        }
      } else {
        console.log('❌ Test document indexing failed');
        this.testFailed++;
      }
    } catch (error) {
      console.log(`❌ Index and search test failed: ${error.message}`);
      this.testFailed++;
    }
  }
}

// Main execution
async function main() {
  const tester = new VectorSearchTester();
  await tester.runTests();
}

// Run if this file is executed directly
if (process.argv[1] === import.meta.url) {
  main();
}

export default VectorSearchTester;