{"name": "document-intelligence-mcp-server", "version": "1.0.0", "description": "MCP server for advanced document analysis and intelligence extraction", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "crypto": "^1.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "tiktoken": "^1.0.15", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "winston": "^3.11.0", "natural": "^6.12.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}