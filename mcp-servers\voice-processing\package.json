{"name": "mcp-voice-processing", "version": "1.0.0", "description": "Secure Voice Processing MCP Server with real-time speech-to-text and political content analysis", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint ."}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "express": "^4.18.3", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-oauth-server": "^2.0.0", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-oauth2": "^1.8.0", "winston": "^3.11.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.0", "socket.io": "^4.7.4", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "wav-decoder": "^1.3.0", "audio-buffer": "^4.0.3", "speex": "^1.0.0", "opus-decoder": "^0.1.1", "webrtc-vad": "^1.0.0", "speaker-diarization": "^1.0.0", "sentiment": "^5.0.2", "natural": "^6.12.0", "compromise": "^14.11.0", "political-keywords": "^1.0.0", "uuid": "^9.0.1", "mime": "^4.0.1", "redis": "^4.6.12", "pg": "^8.11.3", "prom-client": "^15.1.0", "express-prometheus-middleware": "^1.2.0", "bcryptjs": "^2.4.3", "crypto": "^1.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "voice-processing", "speech-to-text", "real-time", "political-analysis", "openai-whisper", "oauth2", "security"], "author": "Political Document Processing System", "license": "MIT"}