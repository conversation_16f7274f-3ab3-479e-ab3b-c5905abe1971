{"name": "research-integration-mcp-server", "version": "1.0.0", "description": "MCP server for research integration and fact-checking", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "axios": "^1.6.0", "axios-retry": "^3.8.0", "cheerio": "^1.0.0-rc.12", "circuit-breaker-js": "^1.2.0", "fs-extra": "^11.2.0", "openai": "^4.20.0", "pg": "^8.11.3", "puppeteer": "^21.5.0", "redis": "^4.6.10", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}