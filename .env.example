# Political Document Processing System - Environment Configuration
# Copy this file to .env and fill in your actual values

# ============================================
# CORE SYSTEM CONFIGURATION
# ============================================

# n8n Configuration
N8N_PASSWORD=your_secure_n8n_password
N8N_ENCRYPTION_KEY=your_32_char_encryption_key_here

# Database Configuration
POSTGRES_PASSWORD=your_secure_postgres_password
REDIS_PASSWORD=your_secure_redis_password

# MCP Server Configuration
MCP_SERVER_API_KEY=your_mcp_server_api_key

# ============================================
# AI MODEL API KEYS
# ============================================

# API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="************************************************************************************************************"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="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"     # Optional: Format: pplx-...
OPENAI_API_KEY="********************************************************************************************************************************************************************"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="AIzaSyAgB9zX2MGfuhz74K_MFlyeDMKwiuSu9Rg"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmaster/config.json).
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.
GITHUB_API_KEY="*********************************************************************************************"             # Optional: For GitHub import/export features. Format: ghp_... or github_pat_...

# ============================================
# GOOGLE DRIVE INTEGRATION
# ============================================

# Google Drive API Credentials
GOOGLE_DRIVE_CLIENT_ID=your_google_drive_client_id
GOOGLE_DRIVE_CLIENT_SECRET=your_google_drive_client_secret
GOOGLE_DRIVE_REFRESH_TOKEN=your_google_drive_refresh_token

# ============================================
# DOCUMENT PROCESSING SERVICES
# ============================================

# CloudConvert API (for DOCX conversion)
CLOUDCONVERT_API_KEY=*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# ============================================
# EMAIL CONFIGURATION
# ============================================

# SMTP Configuration for notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_specific_password

# ============================================
# RESEARCH AND DATA APIS
# ============================================

# Web Search APIs
BRAVE_API_KEY=your_brave_search_api_key
SERP_API_KEY=your_serp_api_key

# Economic Data APIs
FRED_API_KEY=your_fed_reserve_api_key
BLS_API_KEY=your_bureau_labor_stats_api_key
WORLD_BANK_API_KEY=your_world_bank_api_key

# Legal Research APIs
LEGAL_DATABASE_URL=your_legal_database_connection
WESTLAW_API_KEY=your_westlaw_api_key

# Translation APIs
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
DEEPL_API_KEY=your_deepl_api_key

# International Data APIs
OECD_API_KEY=your_oecd_api_key

# ============================================
# MONITORING AND ANALYTICS
# ============================================

# Grafana Configuration
GRAFANA_PASSWORD=your_grafana_admin_password