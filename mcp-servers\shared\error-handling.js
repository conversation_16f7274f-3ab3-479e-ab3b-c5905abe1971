#!/usr/bin/env node

import CircuitBreaker from 'circuit-breaker-js';
import winston from 'winston';
import axiosRetry from 'axios-retry';
import axios from 'axios';

/**
 * Shared error handling utilities with circuit breaker patterns
 * for MCP servers
 */

// Configure Winston logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'mcp-server' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Configure axios retry with exponential backoff
axiosRetry(axios, {
  retries: 3,
  retryDelay: axiosRetry.exponentialDelay,
  retryCondition: (error) => {
    return axiosRetry.isNetworkOrIdempotentRequestError(error) ||
           error.response?.status >= 500;
  }
});

/**
 * Circuit breaker configuration profiles for different services
 */
const CIRCUIT_BREAKER_CONFIGS = {
  openai: {
    windowDuration: 30000,     // 30 seconds
    numBuckets: 10,
    timeoutDuration: 15000,    // 15 seconds
    errorThreshold: 50,        // 50% error rate
    volumeThreshold: 5         // Minimum 5 requests
  },
  anthropic: {
    windowDuration: 30000,
    numBuckets: 10,
    timeoutDuration: 20000,    // 20 seconds for longer responses
    errorThreshold: 50,
    volumeThreshold: 5
  },
  perplexity: {
    windowDuration: 30000,
    numBuckets: 10,
    timeoutDuration: 30000,    // 30 seconds for research queries
    errorThreshold: 60,        // More lenient for research APIs
    volumeThreshold: 3
  },
  database: {
    windowDuration: 30000,
    numBuckets: 10,
    timeoutDuration: 5000,     // 5 seconds for DB queries
    errorThreshold: 30,        // Stricter for database operations
    volumeThreshold: 10
  },
  external_api: {
    windowDuration: 60000,     // 1 minute window
    numBuckets: 12,
    timeoutDuration: 10000,    // 10 seconds
    errorThreshold: 70,        // More lenient for external APIs
    volumeThreshold: 3
  }
};

/**
 * Circuit breaker factory to create service-specific circuit breakers
 */
class CircuitBreakerFactory {
  constructor() {
    this.breakers = new Map();
    this.metrics = new Map();
  }

  /**
   * Get or create a circuit breaker for a specific service
   * @param {string} serviceName - Name of the service (openai, anthropic, etc.)
   * @param {Object} customConfig - Optional custom configuration
   * @returns {CircuitBreaker} Circuit breaker instance
   */
  getBreaker(serviceName, customConfig = {}) {
    if (!this.breakers.has(serviceName)) {
      const config = { ...CIRCUIT_BREAKER_CONFIGS[serviceName] || CIRCUIT_BREAKER_CONFIGS.external_api, ...customConfig };
      
      const breaker = new CircuitBreaker(config);
      
      // Add event listeners for monitoring
      breaker.on('open', () => {
        logger.warn(`Circuit breaker opened for ${serviceName}`, { service: serviceName });
        this.updateMetrics(serviceName, 'circuit_open');
      });
      
      breaker.on('halfOpen', () => {
        logger.info(`Circuit breaker half-open for ${serviceName}`, { service: serviceName });
        this.updateMetrics(serviceName, 'circuit_half_open');
      });
      
      breaker.on('close', () => {
        logger.info(`Circuit breaker closed for ${serviceName}`, { service: serviceName });
        this.updateMetrics(serviceName, 'circuit_closed');
      });
      
      breaker.on('failure', (error) => {
        logger.error(`Circuit breaker failure for ${serviceName}`, { 
          service: serviceName, 
          error: error.message 
        });
        this.updateMetrics(serviceName, 'failure');
      });

      this.breakers.set(serviceName, breaker);
      this.initializeMetrics(serviceName);
    }
    
    return this.breakers.get(serviceName);
  }

  /**
   * Initialize metrics for a service
   * @param {string} serviceName - Name of the service
   */
  initializeMetrics(serviceName) {
    this.metrics.set(serviceName, {
      total_requests: 0,
      successful_requests: 0,
      failed_requests: 0,
      circuit_open_count: 0,
      circuit_half_open_count: 0,
      circuit_closed_count: 0,
      last_failure_time: null,
      average_response_time: 0
    });
  }

  /**
   * Update metrics for a service
   * @param {string} serviceName - Name of the service
   * @param {string} eventType - Type of event (success, failure, etc.)
   */
  updateMetrics(serviceName, eventType) {
    const metrics = this.metrics.get(serviceName);
    if (!metrics) return;

    switch (eventType) {
      case 'success':
        metrics.successful_requests++;
        break;
      case 'failure':
        metrics.failed_requests++;
        metrics.last_failure_time = new Date().toISOString();
        break;
      case 'circuit_open':
        metrics.circuit_open_count++;
        break;
      case 'circuit_half_open':
        metrics.circuit_half_open_count++;
        break;
      case 'circuit_closed':
        metrics.circuit_closed_count++;
        break;
    }
    
    metrics.total_requests = metrics.successful_requests + metrics.failed_requests;
  }

  /**
   * Get metrics for a service
   * @param {string} serviceName - Name of the service
   * @returns {Object} Service metrics
   */
  getMetrics(serviceName) {
    return this.metrics.get(serviceName) || {};
  }

  /**
   * Get all metrics
   * @returns {Object} All service metrics
   */
  getAllMetrics() {
    return Object.fromEntries(this.metrics);
  }
}

// Global circuit breaker factory instance
const circuitBreakerFactory = new CircuitBreakerFactory();

/**
 * Enhanced error handling wrapper for AI service calls
 * @param {string} serviceName - Name of the service (openai, anthropic, etc.)
 * @param {Function} operation - The operation to execute
 * @param {Object} fallbackOptions - Fallback options for graceful degradation
 * @returns {Promise} Result of the operation or fallback
 */
export async function withCircuitBreaker(serviceName, operation, fallbackOptions = {}) {
  const breaker = circuitBreakerFactory.getBreaker(serviceName);
  const startTime = Date.now();
  
  try {
    // Execute the operation through the circuit breaker
    const result = await breaker.run(operation);
    
    // Update success metrics
    const responseTime = Date.now() - startTime;
    circuitBreakerFactory.updateMetrics(serviceName, 'success');
    
    logger.info(`${serviceName} operation succeeded`, {
      service: serviceName,
      responseTime: responseTime
    });
    
    return result;
    
  } catch (error) {
    // Update failure metrics
    circuitBreakerFactory.updateMetrics(serviceName, 'failure');
    
    logger.error(`${serviceName} operation failed`, {
      service: serviceName,
      error: error.message,
      stack: error.stack
    });
    
    // Handle graceful degradation
    if (fallbackOptions.enableFallback && fallbackOptions.fallbackFunction) {
      logger.info(`Executing fallback for ${serviceName}`, { service: serviceName });
      try {
        return await fallbackOptions.fallbackFunction(error);
      } catch (fallbackError) {
        logger.error(`Fallback failed for ${serviceName}`, {
          service: serviceName,
          error: fallbackError.message
        });
      }
    }
    
    // Re-throw the error if no fallback succeeded
    throw new EnhancedError(error.message, serviceName, error);
  }
}

/**
 * Enhanced error class with structured information
 */
export class EnhancedError extends Error {
  constructor(message, serviceName, originalError = null) {
    super(message);
    this.name = 'EnhancedError';
    this.serviceName = serviceName;
    this.timestamp = new Date().toISOString();
    this.originalError = originalError;
    
    if (originalError) {
      this.stack = originalError.stack;
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      serviceName: this.serviceName,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Structured error logging function
 * @param {Error} error - The error to log
 * @param {string} context - Additional context about where the error occurred
 * @param {Object} metadata - Additional metadata to include
 */
export function logStructuredError(error, context = '', metadata = {}) {
  const errorInfo = {
    message: error.message,
    name: error.name,
    context: context,
    timestamp: new Date().toISOString(),
    ...metadata
  };

  if (error instanceof EnhancedError) {
    errorInfo.serviceName = error.serviceName;
    errorInfo.originalError = error.originalError?.message;
  }

  if (error.stack) {
    errorInfo.stack = error.stack;
  }

  logger.error('Structured error log', errorInfo);
}

/**
 * Health check function that reports circuit breaker status
 * @returns {Object} Health check report
 */
export function getHealthCheck() {
  const metrics = circuitBreakerFactory.getAllMetrics();
  const timestamp = new Date().toISOString();
  
  const healthStatus = {
    timestamp,
    status: 'healthy',
    services: {},
    overall_metrics: {
      total_services: Object.keys(metrics).length,
      healthy_services: 0,
      degraded_services: 0,
      unhealthy_services: 0
    }
  };

  for (const [serviceName, serviceMetrics] of Object.entries(metrics)) {
    const breaker = circuitBreakerFactory.breakers.get(serviceName);
    const state = breaker ? breaker.getState() : 'unknown';
    
    let serviceStatus = 'healthy';
    if (state === 'OPEN') {
      serviceStatus = 'unhealthy';
      healthStatus.overall_metrics.unhealthy_services++;
    } else if (state === 'HALF_OPEN') {
      serviceStatus = 'degraded';
      healthStatus.overall_metrics.degraded_services++;
    } else {
      healthStatus.overall_metrics.healthy_services++;
    }
    
    // Calculate error rate
    const errorRate = serviceMetrics.total_requests > 0 
      ? (serviceMetrics.failed_requests / serviceMetrics.total_requests) * 100 
      : 0;
    
    healthStatus.services[serviceName] = {
      status: serviceStatus,
      circuit_breaker_state: state,
      error_rate: Math.round(errorRate * 100) / 100,
      total_requests: serviceMetrics.total_requests,
      successful_requests: serviceMetrics.successful_requests,
      failed_requests: serviceMetrics.failed_requests,
      last_failure_time: serviceMetrics.last_failure_time
    };
  }

  // Determine overall status
  if (healthStatus.overall_metrics.unhealthy_services > 0) {
    healthStatus.status = 'degraded';
  }

  return healthStatus;
}

/**
 * Retry wrapper with exponential backoff
 * @param {Function} operation - Operation to retry
 * @param {Object} options - Retry options
 * @returns {Promise} Result of the operation
 */
export async function withRetry(operation, options = {}) {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    jitter = true
  } = options;

  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break; // No more retries
      }
      
      // Calculate delay with exponential backoff
      let delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
      
      // Add jitter to prevent thundering herd
      if (jitter) {
        delay = delay * (0.5 + Math.random() * 0.5);
      }
      
      logger.warn(`Retry attempt ${attempt + 1}/${maxRetries + 1} after ${delay}ms`, {
        error: error.message,
        delay: delay
      });
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

/**
 * Timeout wrapper for long-running operations
 * @param {Function} operation - Operation to execute
 * @param {number} timeoutMs - Timeout in milliseconds
 * @param {string} operationName - Name of the operation for logging
 * @returns {Promise} Result of the operation
 */
export async function withTimeout(operation, timeoutMs, operationName = 'operation') {
  return new Promise(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      const error = new Error(`${operationName} timed out after ${timeoutMs}ms`);
      logger.error('Operation timeout', {
        operation: operationName,
        timeout: timeoutMs
      });
      reject(error);
    }, timeoutMs);

    try {
      const result = await operation();
      clearTimeout(timeoutId);
      resolve(result);
    } catch (error) {
      clearTimeout(timeoutId);
      reject(error);
    }
  });
}

// Export the factory instance for direct access if needed
export { circuitBreakerFactory, logger };

export default {
  withCircuitBreaker,
  withRetry,
  withTimeout,
  getHealthCheck,
  logStructuredError,
  EnhancedError,
  circuitBreakerFactory,
  logger
};