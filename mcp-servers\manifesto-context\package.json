{"name": "manifesto-context-mcp-server", "version": "1.0.0", "description": "MCP server for <PERSON> political manifesto context and guidance", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "crypto": "^1.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "tiktoken": "^1.0.15", "yaml": "^2.3.4", "circuit-breaker-js": "^0.0.6", "winston": "^3.11.0", "axios-retry": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}