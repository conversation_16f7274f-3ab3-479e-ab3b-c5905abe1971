{"dashboard": {"id": null, "title": "Political Document Processing - System Overview", "tags": ["political", "documents", "n8n", "mcp", "overview"], "style": "dark", "timezone": "browser", "panels": [{"id": 1, "title": "System Health Overview", "type": "stat", "targets": [{"expr": "up{job=~\"n8n|mcp-.*\"}", "legendFormat": "{{job}} - {{instance}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [{"options": {"0": {"text": "DOWN"}, "1": {"text": "UP"}}, "type": "value"}], "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "pluginVersion": "8.0.0"}, {"id": 2, "title": "Document Processing Rate", "type": "graph", "targets": [{"expr": "rate(political_documents_processed_total[5m])", "legendFormat": "Documents/sec", "refId": "A"}], "yAxes": [{"label": "Documents per second", "show": true}, {"show": true}], "xAxis": {"show": true}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "fill": 1, "linewidth": 1, "points": false, "pointradius": 2, "bars": false, "stack": false, "percentage": false, "nullPointMode": "null", "steppedLine": false, "tooltip": {"value_type": "individual"}, "timeFrom": null, "timeShift": null}, {"id": 3, "title": "MCP Server Response Times", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(mcp_request_duration_seconds_bucket[5m])) * 1000", "legendFormat": "95th percentile", "refId": "A"}, {"expr": "histogram_quantile(0.50, rate(mcp_request_duration_seconds_bucket[5m])) * 1000", "legendFormat": "50th percentile", "refId": "B"}], "yAxes": [{"label": "Response time (ms)", "show": true}, {"show": true}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}}, {"id": 4, "title": "ChromaDB Vector Search Performance", "type": "graph", "targets": [{"expr": "rate(chromadb_search_requests_total[5m])", "legendFormat": "Search requests/sec", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(chromadb_search_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile search time", "refId": "B"}], "yAxes": [{"label": "Requests/sec", "show": true}, {"label": "Search time (s)", "show": true}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "N8N Workflow Execution Status", "type": "stat", "targets": [{"expr": "n8n_workflow_executions_total", "legendFormat": "Total Executions", "refId": "A"}, {"expr": "rate(n8n_workflow_executions_total{status=\"success\"}[5m])", "legendFormat": "Success Rate", "refId": "B"}, {"expr": "rate(n8n_workflow_executions_total{status=\"error\"}[5m])", "legendFormat": "Error Rate", "refId": "C"}], "gridPos": {"h": 6, "w": 8, "x": 0, "y": 16}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "unit": "short"}}}, {"id": 6, "title": "Database Connection Pool", "type": "graph", "targets": [{"expr": "postgresql_connections_active", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "postgresql_connections_idle", "legendFormat": "Idle Connections", "refId": "B"}], "gridPos": {"h": 6, "w": 8, "x": 8, "y": 16}}, {"id": 7, "title": "Memory Usage by Service", "type": "graph", "targets": [{"expr": "container_memory_usage_bytes{name=~\".*political.*\"} / 1024 / 1024", "legendFormat": "{{name}}", "refId": "A"}], "yAxes": [{"label": "Memory (MB)", "show": true}], "gridPos": {"h": 6, "w": 8, "x": 16, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "refresh": "30s", "version": 0}}