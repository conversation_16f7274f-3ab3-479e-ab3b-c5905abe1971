# PROMPT File Format Standard

## Standard PROMPT File Structure

### Basic Template
```markdown
# PROMPT_[PROJECT_NAME]_[DATE].md

## PROJECT OVERVIEW
**Project Name:** [Descriptive name for this analysis/creation]
**Category:** [healthcare/education/economic/etc.]
**Priority Level:** [High/Medium/Low]
**Deadline:** [Date or "No rush"]

## OUTPUT REQUIREMENTS
**Document Types Needed:**
- [ ] White Paper (comprehensive analysis)
- [ ] Policy Brief (executive summary style)
- [ ] Implementation Plan (step-by-step roadmap)
- [ ] Talking Points (bullet-point summaries)
- [ ] Research Summary (data and statistics)
- [ ] Comparative Analysis (vs other countries/policies)
- [ ] Cost-Benefit Analysis (economic impact)
- [ ] Timeline Document (phases and milestones)
- [ ] FAQ Document (common questions/answers)
- [ ] Other: [specify]

**Target Length for Each:**
- White Paper: [word count, e.g., 5000-8000 words]
- Policy Brief: [word count, e.g., 1500-2000 words]
- [etc. for each selected type]

## CONTENT INSTRUCTIONS
**Main Focus:** [What is the primary goal/question this project addresses?]

**Key Points to Address:**
1. [Specific point or question]
2. [Specific point or question]
3. [Specific point or question]
4. [Add more as needed]

**Research Requirements:**
- [ ] Current US policy landscape
- [ ] International examples (specify countries if known)
- [ ] Historical precedents
- [ ] Economic data and projections
- [ ] Expert opinions and studies
- [ ] Opposition arguments and counterpoints
- [ ] Implementation challenges
- [ ] Success stories from other regions
- [ ] Recent news and developments
- [ ] Academic research and papers

**Specific Research Targets:**
- [e.g., "Bernie Sanders' Medicare for All plan"]
- [e.g., "Nordic healthcare systems transition history"]
- [e.g., "UK NHS implementation timeline"]
- [Add specific sources, people, or examples to research]

## TONE AND STYLE
**Target Audience:** [General public/Policymakers/Academics/Activists/Mixed]
**Writing Tone:** [Professional/Passionate/Academic/Accessible/Persuasive]
**Complexity Level:** [High school/College/Graduate/Expert]
**Evidence Standards:** [Data-heavy/Balanced/Story-focused]

## SPECIAL INSTRUCTIONS
**Cross-References:** [Should this connect to other documents/projects?]
**Avoid:** [Topics, arguments, or approaches to avoid]
**Emphasize:** [Key themes or arguments to highlight]
**Format Preferences:** [Any specific formatting requests]

## QUALITY CONTROL NOTES
**Review Focus:** [What should the quality agent pay special attention to?]
**Fact-Check Priority:** [Which claims need extra verification?]
**Consistency Check:** [Which other documents should this align with?]

---
*End of PROMPT Template*
```

## Example PROMPT Files

### Example 1: Simple Healthcare Analysis
```markdown
# PROMPT_Universal_Healthcare_Analysis_2024-01-15.md

## PROJECT OVERVIEW
**Project Name:** Universal Healthcare Policy Framework
**Category:** healthcare
**Priority Level:** High
**Deadline:** No rush

## OUTPUT REQUIREMENTS
**Document Types Needed:**
- [x] White Paper (comprehensive analysis)
- [x] Policy Brief (executive summary style)
- [x] Implementation Plan (step-by-step roadmap)
- [ ] Talking Points (bullet-point summaries)
- [x] Comparative Analysis (vs other countries/policies)

**Target Length for Each:**
- White Paper: 6000-8000 words
- Policy Brief: 1500-2000 words
- Implementation Plan: 3000-4000 words
- Comparative Analysis: 2000-3000 words

## CONTENT INSTRUCTIONS
**Main Focus:** How can the US transition to universal healthcare within 10 years?

**Key Points to Address:**
1. Current healthcare system problems and costs
2. International models that work (Nordic, UK, Canada)
3. Transition timeline and phases
4. Economic impact and funding mechanisms
5. Political feasibility and coalition building
6. Addressing opposition arguments

**Research Requirements:**
- [x] Current US policy landscape
- [x] International examples (Nordic countries, UK, Canada)
- [x] Historical precedents
- [x] Economic data and projections
- [x] Expert opinions and studies
- [x] Opposition arguments and counterpoints
- [x] Implementation challenges
- [x] Success stories from other regions

**Specific Research Targets:**
- "Bernie Sanders' Medicare for All plan details"
- "Nordic healthcare systems transition history 1960s-1980s"
- "UK NHS implementation timeline 1945-1950"
- "Taiwan's National Health Insurance implementation"
- "Current US healthcare spending vs other developed nations"

## TONE AND STYLE
**Target Audience:** Policymakers and educated general public
**Writing Tone:** Professional but passionate, evidence-based
**Complexity Level:** College level
**Evidence Standards:** Data-heavy with real-world examples

## SPECIAL INSTRUCTIONS
**Cross-References:** Connect to education workforce development (need more doctors/nurses)
**Avoid:** Partisan language, unrealistic timelines
**Emphasize:** Economic benefits, moral imperative, international success stories
**Format Preferences:** Include charts/data tables in appendices

## QUALITY CONTROL NOTES
**Review Focus:** Fact-check all international statistics and timelines
**Fact-Check Priority:** Economic projections and cost savings claims
**Consistency Check:** Align with manifesto values on healthcare as human right
```

### Example 2: Complex Multi-Category Project
```markdown
# PROMPT_Comprehensive_Progressive_Platform_2024-01-15.md

## PROJECT OVERVIEW
**Project Name:** Comprehensive Progressive Policy Platform
**Category:** multi-category (healthcare, education, economic)
**Priority Level:** High
**Deadline:** 2024-03-01

## OUTPUT REQUIREMENTS
**Document Types Needed:**
- [x] White Paper (comprehensive analysis) - ONE master document
- [x] Policy Brief (executive summary style) - For each major category
- [x] Implementation Plan (step-by-step roadmap) - Integrated timeline
- [x] Talking Points (bullet-point summaries) - For campaign use
- [x] Cost-Benefit Analysis (economic impact) - Overall economic framework

**Target Length for Each:**
- Master White Paper: 15000-20000 words
- Policy Briefs: 2000 words each (healthcare, education, economic)
- Implementation Plan: 8000-10000 words
- Talking Points: 500 words per category
- Cost-Benefit Analysis: 5000-6000 words

## CONTENT INSTRUCTIONS
**Main Focus:** How do universal healthcare, free education, and economic justice work together as integrated policy framework?

**Key Points to Address:**
1. Healthcare as foundation for economic productivity
2. Education investment creates healthcare workforce
3. Economic justice funds both healthcare and education
4. International examples of integrated progressive policies
5. 20-year implementation timeline with phases
6. Political coalition building across all three areas

**Research Requirements:**
- [x] All categories checked
- [x] Focus on countries with integrated progressive policies (Nordic model)
- [x] Economic modeling for combined implementation
- [x] Workforce development connections between education and healthcare

**Specific Research Targets:**
- "Nordic model comprehensive analysis - Sweden, Norway, Denmark"
- "Germany's dual education system and healthcare workforce"
- "Economic multiplier effects of public investment in health/education"
- "Political coalitions that achieved major progressive reforms"

## TONE AND STYLE
**Target Audience:** Mixed - policymakers, activists, general public
**Writing Tone:** Visionary but practical, inspiring but evidence-based
**Complexity Level:** Accessible to college-educated readers
**Evidence Standards:** Strong data foundation with compelling narratives

## SPECIAL INSTRUCTIONS
**Cross-References:** This is the master document - all future projects should reference this
**Avoid:** Overwhelming complexity, partisan attacks
**Emphasize:** Interconnected benefits, long-term vision, practical steps
**Format Preferences:** Executive summary at beginning, detailed appendices

## QUALITY CONTROL NOTES
**Review Focus:** Ensure all three policy areas are properly integrated
**Fact-Check Priority:** All economic projections and international comparisons
**Consistency Check:** This becomes the reference document for future projects
```

## PROMPT File Naming Convention
```
PROMPT_[ProjectName]_[Category]_[Date].md

Examples:
PROMPT_Universal_Healthcare_Analysis_healthcare_2024-01-15.md
PROMPT_Education_Reform_Comprehensive_education_2024-01-15.md
PROMPT_Progressive_Platform_Master_multicategory_2024-01-15.md
```

## Validation Checklist for PROMPT Files
- [ ] Clear project name and category
- [ ] Specific output requirements with word counts
- [ ] Detailed research instructions
- [ ] Target audience and tone specified
- [ ] Quality control guidance provided
- [ ] Special instructions for unique requirements
- [ ] Cross-reference notes for related projects

## Benefits of This Format
1. **Consistency**: Every project has same structure
2. **Completeness**: Agents get all necessary information
3. **Flexibility**: Can handle simple or complex projects
4. **Quality Control**: Built-in guidance for review process
5. **Scalability**: Easy to add new document types or requirements
6. **Clarity**: No ambiguity about what's needed

Would you like me to create the professional DOCX templates next, or refine this PROMPT format further?