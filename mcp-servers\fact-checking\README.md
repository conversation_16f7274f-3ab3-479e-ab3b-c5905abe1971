# Fact-Checking MCP Server

A comprehensive Model Context Protocol (MCP) server for advanced fact-checking, source validation, and claim analysis. This server provides robust tools for verifying claims, checking source credibility, extracting claims from text, gathering evidence, and detecting bias in content.

## Features

### Core Tools

1. **fact_verify** - Verify claims against reliable sources using comprehensive fact-checking methodology
2. **source_credibility_check** - Evaluate the credibility, reliability, and bias of information sources  
3. **claim_extraction** - Extract verifiable claims from text for fact-checking analysis
4. **evidence_gathering** - Gather supporting and contradicting evidence for claims from multiple sources
5. **bias_detection** - Detect and analyze potential bias in content, sources, and claims

### Advanced Capabilities

- **Multi-source verification** using trusted fact-checking databases
- **AI-powered analysis** with OpenAI GPT-4 integration
- **Source credibility assessment** with domain reputation analysis
- **Comprehensive bias detection** across multiple bias types
- **Perplexity integration** for real-time web research
- **PostgreSQL storage** for fact-check results and source validations
- **Redis caching** for improved performance
- **Health monitoring** with Express.js endpoint

## Installation

### Prerequisites

- Node.js >= 18.0.0
- PostgreSQL database
- Redis server
- OpenAI API key
- Perplexity API key (optional)

### Environment Variables

```bash
# Database Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=fact_checking
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=n8n_secure_password

# Redis Configuration  
REDIS_HOST=redis
REDIS_PORT=6379

# API Keys
OPENAI_API_KEY=your_openai_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
```

### Docker Installation

```bash
# Build the container
docker build -t fact-checking-mcp-server .

# Run with environment variables
docker run -d \
  --name fact-checking-server \
  -p 8090:8090 \
  -e OPENAI_API_KEY=your_key \
  -e POSTGRES_HOST=postgres \
  -e REDIS_HOST=redis \
  fact-checking-mcp-server
```

### Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test
```

## API Reference

### Tool: fact_verify

Verifies claims against reliable sources using comprehensive methodology.

**Parameters:**
- `claim` (string, required) - The claim to verify
- `context` (string) - Additional context about the claim  
- `political_figure` (string) - Person who made the claim
- `urgency_level` (enum) - Urgency: low, medium, high, critical
- `check_depth` (enum) - Depth: quick, standard, comprehensive

**Returns:**
```json
{
  "claim_id": "abc123",
  "original_claim": "...",
  "verification_status": "verified_true|likely_true|mixed_evidence|likely_false|verified_false",
  "credibility_score": 0.85,
  "evidence": [...],
  "sources_checked": 12,
  "summary": "..."
}
```

### Tool: source_credibility_check

Evaluates source credibility and bias.

**Parameters:**
- `source_url` (string) - URL of the source to check
- `source_content` (string) - Content from the source (alternative to URL)
- `check_factors` (array) - Factors to evaluate: domain_reputation, author_expertise, bias_analysis, factual_accuracy, publication_quality

**Returns:**
```json
{
  "domain": "example.com",
  "overall_rating": "high|medium|low|unknown",
  "factors": {
    "domain_reputation": {...},
    "bias_analysis": {...},
    "factual_accuracy": {...}
  }
}
```

### Tool: claim_extraction

Extracts verifiable claims from text.

**Parameters:**
- `text_content` (string, required) - Text to analyze
- `claim_types` (array) - Types: statistical, policy, historical, comparative, predictive
- `min_confidence` (number) - Minimum confidence threshold (0.0-1.0)

**Returns:**
```json
{
  "extracted_claims": [
    {
      "claim_text": "...",
      "claim_type": "statistical", 
      "confidence_score": 0.85,
      "context": "..."
    }
  ],
  "total_claims": 5,
  "claim_types_found": ["statistical", "policy"]
}
```

### Tool: evidence_gathering

Gathers supporting and contradicting evidence.

**Parameters:**
- `claims` (array, required) - List of claims to research
- `reference_databases` (array) - Databases: factcheck_org, politifact, snopes, reuters_fact_check, all
- `similarity_threshold` (number) - Similarity threshold for matching (0.0-1.0)

**Returns:**
```json
{
  "cross_references": [
    {
      "original_claim": "...",
      "matches": [...],
      "match_count": 3,
      "highest_similarity": 0.92
    }
  ],
  "total_matches": 8,
  "databases_checked": ["factcheck_org", "politifact"]
}
```

### Tool: bias_detection

Detects and analyzes potential bias in content.

**Parameters:**
- `content` (string, required) - Content to analyze
- `content_type` (enum) - Type: article, claim, source, speech, social_media
- `bias_types` (array) - Types: political, ideological, confirmation, selection, framing, language, statistical
- `detailed_analysis` (boolean) - Provide detailed analysis with examples

**Returns:**
```json
{
  "content_type": "article",
  "bias_detected": true,
  "overall_bias_score": 0.7,
  "bias_indicators": [
    {
      "type": "political",
      "detected": true,
      "bias_score": 0.75,
      "confidence": 0.9,
      "explanation": "...",
      "examples": ["..."]
    }
  ],
  "confidence_score": 0.85,
  "summary": "..."
}
```

## Trusted Sources

The server uses a curated list of trusted sources for verification:

- factcheck.org
- snopes.com  
- politifact.com
- reuters.com
- ap.org
- bbc.com
- npr.org
- cnn.com
- nytimes.com
- washingtonpost.com

Additional domain-based credibility assessment:
- `.edu` and `.gov` domains rated as high credibility
- Blog and personal sites rated as lower credibility
- Comprehensive bias analysis using AI

## Database Schema

### fact_checks
- `id` - Primary key
- `claim_id` - Unique claim identifier
- `original_claim` - The claim text
- `verification_result` - JSON results
- `credibility_score` - Numerical credibility score
- `verification_status` - Status enum
- `created_at/updated_at` - Timestamps

### source_validations
- `id` - Primary key
- `claim_id` - Associated claim
- `source_url` - Source URL
- `credibility_rating` - Rating
- `relevance_score` - Relevance score
- `content_snippet` - Content sample

### political_claims
- `id` - Primary key
- `claim_text` - Claim content
- `political_figure` - Who made the claim
- `fact_check_id` - Associated fact check

## Health Monitoring

Health check endpoint available at `http://localhost:8090/health`

Returns:
```json
{
  "status": "healthy",
  "service": "fact-checking-mcp-server", 
  "timestamp": "2024-01-01T00:00:00.000Z",
  "checks": {
    "database": "connected",
    "redis": "connected", 
    "openai": "configured",
    "perplexity": "configured"
  }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.