#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import { OpenAI } from 'openai';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
import winston from 'winston';
import natural from 'natural';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';

/**
 * Document Intelligence MCP Server
 * Advanced document analysis, processing, and intelligence extraction for political content
 */

class DocumentIntelligenceServer {
  constructor() {
    this.server = new MCPServer({
      name: 'document-intelligence',
      version: '1.0.0'
    });
    
    // Initialize AI clients
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.tokenizer = encoding_for_model('gpt-4');
    this.documentCache = new Map();
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    // NLP tools
    this.stemmer = natural.PorterStemmer;
    this.tokenizer_nlp = new natural.WordTokenizer();
    this.sentimentAnalyzer = new natural.SentimentAnalyzer('English', 
      natural.PorterStemmer, 'afinn');
    
    // Setup logging
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: '/app/logs/document-intelligence.log' })
      ]
    });

    this.setupTools();
    this.setupResources();
    this.setupHealthEndpoint();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'document_intelligence',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      await this.pgClient.connect();
      await this.redisClient.connect();
      this.logger.info('Document Intelligence MCP Server database connections established');
      
      await this.initializeTables();
    } catch (error) {
      this.logger.error('Failed to initialize:', error);
      throw error;
    }
  }

  async initializeTables() {
    try {
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS document_analysis (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255) UNIQUE,
          filename VARCHAR(255),
          file_type VARCHAR(50),
          content_hash VARCHAR(64),
          analysis_result JSONB,
          sentiment_score DECIMAL(3,2),
          key_topics TEXT[],
          political_themes TEXT[],
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS document_entities (
          id SERIAL PRIMARY KEY,
          document_id VARCHAR(255),
          entity_type VARCHAR(100),
          entity_text VARCHAR(500),
          confidence DECIMAL(3,2),
          start_position INTEGER,
          end_position INTEGER,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
    } catch (error) {
      this.logger.error('Error initializing tables:', error);
      throw error;
    }
  }

  setupTools() {
    // Tool: Document Intent Analyzer  
    this.server.addTool({
      name: 'document_intent_analyzer', 
      description: 'Analyze documents and suggest actions based on content intent and purpose',
      inputSchema: {
        type: 'object',
        properties: {
          document_path: {
            type: 'string',
            description: 'Path to the document file'
          },
          document_content: {
            type: 'string',
            description: 'Raw text content (alternative to file path)'
          },
          analysis_type: {
            type: 'string',
            enum: ['basic', 'comprehensive', 'political', 'sentiment', 'entities'],
            description: 'Type of analysis to perform',
            default: 'comprehensive'
          },
          extract_entities: {
            type: 'boolean',
            default: true,
            description: 'Extract named entities from the document'
          }
        }
      }
    }, this.documentIntentAnalyzer.bind(this));

    // Tool: Document Structure Analyzer
    this.server.addTool({
      name: 'document_structure_analyzer',
      description: 'Analyze document structure and format to understand organization and layout',
      inputSchema: {
        type: 'object',
        properties: {
          document_path: {
            type: 'string',
            description: 'Path to the document file'
          },
          document_content: {
            type: 'string',
            description: 'Raw text content (alternative to file path)'
          },
          structure_analysis_type: {
            type: 'string',
            enum: ['sections', 'hierarchy', 'formatting', 'comprehensive'],
            description: 'Type of structure analysis to perform',
            default: 'comprehensive'
          }
        }
      }
    }, this.documentStructureAnalyzer.bind(this));

    // Tool: Content Extractor
    this.server.addTool({
      name: 'content_extractor',
      description: 'Extract specific content from documents including text, data, and structured information',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Document identifier'
          },
          content: {
            type: 'string',
            description: 'Text content to analyze'
          },
          extraction_targets: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['entities', 'key_phrases', 'statistics', 'quotes', 'references', 'dates', 'numbers']
            },
            description: 'Types of content to extract'
          },
          extraction_format: {
            type: 'string',
            enum: ['structured', 'text', 'json'],
            description: 'Format for extracted content',
            default: 'structured'
          }
        },
        required: ['content']
      }
    }, this.contentExtractor.bind(this));

    // Tool: Summarize Document
    this.server.addTool({
      name: 'summarize_document',
      description: 'Generate intelligent summaries of political documents',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to summarize'
          },
          summary_type: {
            type: 'string',
            enum: ['executive', 'detailed', 'bullet_points', 'political_analysis'],
            description: 'Type of summary to generate',
            default: 'executive'
          },
          max_length: {
            type: 'integer',
            minimum: 100,
            maximum: 2000,
            default: 500,
            description: 'Maximum length of summary in words'
          },
          focus_areas: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific areas to focus on in the summary'
          }
        },
        required: ['document_content']
      }
    }, this.summarizeDocument.bind(this));

    // Tool: Compare Documents
    this.server.addTool({
      name: 'compare_documents',
      description: 'Compare two documents for similarities, differences, and policy alignment',
      inputSchema: {
        type: 'object',
        properties: {
          document_a: {
            type: 'string',
            description: 'First document content'
          },
          document_b: {
            type: 'string',
            description: 'Second document content'
          },
          comparison_type: {
            type: 'string',
            enum: ['semantic', 'policy_alignment', 'sentiment', 'comprehensive'],
            description: 'Type of comparison to perform',
            default: 'comprehensive'
          }
        },
        required: ['document_a', 'document_b']
      }
    }, this.compareDocuments.bind(this));

    // Tool: Extract Key Insights
    this.server.addTool({
      name: 'extract_key_insights',
      description: 'Extract key political insights, policy positions, and recommendations from documents',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to analyze'
          },
          insight_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['policy_positions', 'recommendations', 'criticisms', 'proposals', 'statistics']
            },
            description: 'Types of insights to extract'
          },
          political_context: {
            type: 'string',
            description: 'Political context for better insight extraction'
          }
        },
        required: ['document_content']
      }
    }, this.extractKeyInsights.bind(this));

    // Tool: Document Classifier
    this.server.addTool({
      name: 'document_classifier',
      description: 'Classify documents by type, political leaning, and topic categories',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to classify'
          },
          classification_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['document_type', 'political_leaning', 'topic_category', 'urgency_level']
            },
            description: 'Types of classification to perform'
          }
        },
        required: ['document_content']
      }
    }, this.documentClassifier.bind(this));

    // Tool: Metadata Extractor
    this.server.addTool({
      name: 'metadata_extractor',
      description: 'Extract metadata from documents including author, date, format, and structural information',
      inputSchema: {
        type: 'object',
        properties: {
          document_path: {
            type: 'string',
            description: 'Path to the document file'
          },
          document_content: {
            type: 'string',
            description: 'Raw text content (alternative to file path)'
          },
          metadata_types: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['basic', 'structural', 'semantic', 'technical', 'comprehensive']
            },
            description: 'Types of metadata to extract',
            default: ['basic', 'structural']
          }
        }
      }
    }, this.metadataExtractor.bind(this));
  }

  setupResources() {
    // Resource: Analysis Results
    this.server.addResource({
      uri: 'intelligence://analysis_results',
      name: 'Document Analysis Results',
      description: 'Comprehensive analysis results for processed documents',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'intelligence://entity_extraction',
      name: 'Extracted Entities',
      description: 'Named entities extracted from documents',
      mimeType: 'application/json'
    });
  }

  async documentIntentAnalyzer(params) {
    try {
      const { document_path, document_content, analysis_type = 'comprehensive', extract_entities = true } = params;
      
      let content;
      let filename = 'unknown';
      
      if (document_path) {
        const fullPath = path.resolve(document_path);
        if (!await fs.pathExists(fullPath)) {
          throw new Error(`Document not found: ${document_path}`);
        }
        
        filename = path.basename(fullPath);
        const fileExt = path.extname(fullPath).toLowerCase();
        
        // Extract content based on file type
        if (fileExt === '.pdf') {
          const pdfBuffer = await fs.readFile(fullPath);
          const pdfData = await pdfParse(pdfBuffer);
          content = pdfData.text;
        } else if (fileExt === '.docx') {
          const docxBuffer = await fs.readFile(fullPath);
          const result = await mammoth.extractRawText({ buffer: docxBuffer });
          content = result.value;
        } else {
          content = await fs.readFile(fullPath, 'utf8');
        }
      } else if (document_content) {
        content = document_content;
      } else {
        throw new Error('Either document_path or document_content must be provided');
      }

      const documentId = crypto.createHash('sha256').update(content).digest('hex').substring(0, 16);
      
      // Check cache first
      const cacheKey = `doc_analysis:${documentId}:${analysis_type}`;
      const cachedResult = await this.redisClient.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }

      const analysisResult = await this.performDocumentAnalysis(content, analysis_type, extract_entities);
      
      // Store results in database
      await this.storeAnalysisResult(documentId, filename, analysisResult);
      
      // Cache results
      await this.redisClient.setex(cacheKey, 3600, JSON.stringify(analysisResult));
      
      return {
        document_id: documentId,
        filename,
        analysis_type,
        ...analysisResult
      };

    } catch (error) {
      this.logger.error('Error analyzing document:', error);
      throw error;
    }
  }

  async performDocumentAnalysis(content, analysisType, extractEntities) {
    const tokens = this.tokenizer_nlp.tokenize(content.toLowerCase());
    const stemmedTokens = tokens.map(token => this.stemmer.stem(token));
    
    const analysis = {
      word_count: tokens.length,
      character_count: content.length,
      sentiment_analysis: this.analyzeSentiment(tokens),
      readability_score: this.calculateReadability(content, tokens),
      token_count: this.tokenizer.encode(content).length
    };

    if (analysisType === 'comprehensive' || analysisType === 'political') {
      // Political theme analysis using AI
      const politicalAnalysis = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a political analyst. Analyze the following document for political themes, policy positions, and key arguments. Return a structured analysis."
          },
          {
            role: "user",
            content: `Analyze this political document:\n\n${content.substring(0, 8000)}`
          }
        ],
        max_tokens: 1000
      });

      analysis.political_analysis = politicalAnalysis.choices[0].message.content;
      analysis.key_topics = await this.extractTopics(content);
      analysis.political_themes = await this.extractPoliticalThemes(content);
    }

    if (extractEntities) {
      analysis.entities = await this.extractNamedEntities(content);
    }

    return analysis;
  }

  async documentStructureAnalyzer(params) {
    try {
      const { document_path, document_content, structure_analysis_type = 'comprehensive' } = params;
      
      let content;
      let filename = 'unknown';
      
      if (document_path) {
        const fullPath = path.resolve(document_path);
        if (!await fs.pathExists(fullPath)) {
          throw new Error(`Document not found: ${document_path}`);
        }
        
        filename = path.basename(fullPath);
        const fileExt = path.extname(fullPath).toLowerCase();
        
        // Extract content based on file type
        if (fileExt === '.pdf') {
          const pdfBuffer = await fs.readFile(fullPath);
          const pdfData = await pdfParse(pdfBuffer);
          content = pdfData.text;
        } else if (fileExt === '.docx') {
          const docxBuffer = await fs.readFile(fullPath);
          const result = await mammoth.extractRawText({ buffer: docxBuffer });
          content = result.value;
        } else {
          content = await fs.readFile(fullPath, 'utf8');
        }
      } else if (document_content) {
        content = document_content;
      } else {
        throw new Error('Either document_path or document_content must be provided');
      }

      // Analyze document structure
      const structureAnalysis = await this.analyzeDocumentStructure(content, structure_analysis_type);
      
      return {
        filename,
        structure_analysis_type,
        ...structureAnalysis
      };

    } catch (error) {
      this.logger.error('Error analyzing document structure:', error);
      throw error;
    }
  }

  async contentExtractor(params) {
    try {
      const { document_id, content, entity_types = ['PERSON', 'ORGANIZATION', 'LOCATION'] } = params;
      
      const entities = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `Extract named entities of types: ${entity_types.join(', ')} from the following text. Return as JSON array with fields: type, text, start_position, confidence.`
          },
          {
            role: "user",
            content: content.substring(0, 8000)
          }
        ],
        max_tokens: 800
      });

      const extractedEntities = JSON.parse(entities.choices[0].message.content);
      
      if (document_id) {
        // Store entities in database
        for (const entity of extractedEntities) {
          await this.pgClient.query(
            'INSERT INTO document_entities (document_id, entity_type, entity_text, confidence, start_position) VALUES ($1, $2, $3, $4, $5)',
            [document_id, entity.type, entity.text, entity.confidence, entity.start_position]
          );
        }
      }

      return {
        entities: extractedEntities,
        total_entities: extractedEntities.length,
        types_found: [...new Set(extractedEntities.map(e => e.type))]
      };

    } catch (error) {
      this.logger.error('Error extracting entities:', error);
      throw error;
    }
  }

  async summarizeDocument(params) {
    try {
      const { document_content, summary_type = 'executive', max_length = 500, focus_areas = [] } = params;
      
      let systemPrompt = "You are an expert document summarizer.";
      
      switch (summary_type) {
        case 'executive':
          systemPrompt += " Create a concise executive summary highlighting key points and conclusions.";
          break;
        case 'political_analysis':
          systemPrompt += " Focus on political implications, policy positions, and strategic considerations.";
          break;
        case 'bullet_points':
          systemPrompt += " Create a bullet-point summary of main topics and key facts.";
          break;
      }

      if (focus_areas.length > 0) {
        systemPrompt += ` Pay special attention to: ${focus_areas.join(', ')}.`;
      }

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: `Summarize this document (max ${max_length} words):\n\n${document_content}`
          }
        ],
        max_tokens: Math.min(max_length * 2, 2000)
      });

      return {
        summary: response.choices[0].message.content,
        summary_type,
        word_count: response.choices[0].message.content.split(' ').length,
        focus_areas
      };

    } catch (error) {
      this.logger.error('Error summarizing document:', error);
      throw error;
    }
  }

  async compareDocuments(params) {
    try {
      const { document_a, document_b, comparison_type = 'comprehensive' } = params;
      
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `Compare these two documents for ${comparison_type} analysis. Identify similarities, differences, and provide insights.`
          },
          {
            role: "user",
            content: `Document A:\n${document_a.substring(0, 4000)}\n\nDocument B:\n${document_b.substring(0, 4000)}`
          }
        ],
        max_tokens: 1200
      });

      // Calculate semantic similarity using simple word overlap
      const tokensA = new Set(this.tokenizer_nlp.tokenize(document_a.toLowerCase()));
      const tokensB = new Set(this.tokenizer_nlp.tokenize(document_b.toLowerCase()));
      const intersection = new Set([...tokensA].filter(x => tokensB.has(x)));
      const union = new Set([...tokensA, ...tokensB]);
      const similarity = intersection.size / union.size;

      return {
        comparison_analysis: response.choices[0].message.content,
        semantic_similarity: similarity,
        comparison_type,
        word_overlap: intersection.size,
        unique_to_a: tokensA.size - intersection.size,
        unique_to_b: tokensB.size - intersection.size
      };

    } catch (error) {
      this.logger.error('Error comparing documents:', error);
      throw error;
    }
  }

  async extractKeyInsights(params) {
    try {
      const { document_content, insight_types = ['policy_positions', 'recommendations'], political_context = '' } = params;
      
      let systemPrompt = "You are a political analyst. Extract key insights from this document.";
      if (political_context) {
        systemPrompt += ` Context: ${political_context}`;
      }
      
      systemPrompt += ` Focus on: ${insight_types.join(', ')}. Return structured insights.`;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: document_content.substring(0, 8000)
          }
        ],
        max_tokens: 1000
      });

      return {
        insights: response.choices[0].message.content,
        insight_types,
        extracted_at: new Date().toISOString(),
        political_context
      };

    } catch (error) {
      this.logger.error('Error extracting insights:', error);
      throw error;
    }
  }

  async documentClassifier(params) {
    try {
      const { document_content, classification_types = ['document_type', 'topic_category'] } = params;
      
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `Classify this document based on: ${classification_types.join(', ')}. Return JSON with classification results.`
          },
          {
            role: "user",
            content: document_content.substring(0, 6000)
          }
        ],
        max_tokens: 400
      });

      return {
        classification: JSON.parse(response.choices[0].message.content),
        classification_types,
        confidence_score: 0.85, // Would implement proper confidence scoring
        classified_at: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('Error classifying document:', error);
      throw error;
    }
  }

  async metadataExtractor(params) {
    try {
      const { document_path, document_content, metadata_types = ['basic', 'structural'] } = params;
      
      let content;
      let filename = 'unknown';
      let fileStats = null;
      
      if (document_path) {
        const fullPath = path.resolve(document_path);
        if (!await fs.pathExists(fullPath)) {
          throw new Error(`Document not found: ${document_path}`);
        }
        
        filename = path.basename(fullPath);
        fileStats = await fs.stat(fullPath);
        const fileExt = path.extname(fullPath).toLowerCase();
        
        // Extract content based on file type
        if (fileExt === '.pdf') {
          const pdfBuffer = await fs.readFile(fullPath);
          const pdfData = await pdfParse(pdfBuffer);
          content = pdfData.text;
        } else if (fileExt === '.docx') {
          const docxBuffer = await fs.readFile(fullPath);
          const result = await mammoth.extractRawText({ buffer: docxBuffer });
          content = result.value;
        } else {
          content = await fs.readFile(fullPath, 'utf8');
        }
      } else if (document_content) {
        content = document_content;
      } else {
        throw new Error('Either document_path or document_content must be provided');
      }

      // Extract different types of metadata
      const metadata = await this.extractDocumentMetadata(content, filename, fileStats, metadata_types);
      
      return {
        filename,
        metadata_types,
        ...metadata
      };

    } catch (error) {
      this.logger.error('Error extracting metadata:', error);
      throw error;
    }
  }

  // Helper methods
  async analyzeDocumentStructure(content, analysisType) {
    const lines = content.split('\n');
    const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0);
    
    // Basic structure analysis
    const structure = {
      total_lines: lines.length,
      total_paragraphs: paragraphs.length,
      average_paragraph_length: paragraphs.reduce((sum, p) => sum + p.split(' ').length, 0) / paragraphs.length,
      has_headers: this.detectHeaders(lines),
      has_lists: this.detectLists(lines),
      has_numbered_sections: this.detectNumberedSections(lines)
    };

    if (analysisType === 'comprehensive' || analysisType === 'hierarchy') {
      // Analyze document hierarchy
      structure.hierarchy = this.analyzeDocumentHierarchy(lines);
    }

    if (analysisType === 'comprehensive' || analysisType === 'sections') {
      // Detect sections
      structure.sections = this.detectDocumentSections(content);
    }

    if (analysisType === 'comprehensive' || analysisType === 'formatting') {
      // Analyze formatting patterns
      structure.formatting = this.analyzeFormatting(content);
    }

    return structure;
  }

  async extractDocumentMetadata(content, filename, fileStats, metadataTypes) {
    const metadata = {};

    if (metadataTypes.includes('basic')) {
      metadata.basic = {
        filename,
        word_count: content.split(/\s+/).length,
        character_count: content.length,
        line_count: content.split('\n').length,
        created_at: fileStats?.birthtime || null,
        modified_at: fileStats?.mtime || null,
        size_bytes: fileStats?.size || content.length
      };
    }

    if (metadataTypes.includes('structural')) {
      metadata.structural = await this.analyzeDocumentStructure(content, 'comprehensive');
    }

    if (metadataTypes.includes('semantic')) {
      const tokens = this.tokenizer_nlp.tokenize(content.toLowerCase());
      metadata.semantic = {
        sentiment: this.analyzeSentiment(tokens),
        readability: this.calculateReadability(content, tokens),
        key_topics: await this.extractTopics(content),
        language: 'en' // Could be enhanced with language detection
      };
    }

    if (metadataTypes.includes('technical')) {
      metadata.technical = {
        encoding: 'utf-8',
        file_type: path.extname(filename).toLowerCase(),
        token_count: this.tokenizer.encode(content).length,
        hash: crypto.createHash('sha256').update(content).digest('hex').substring(0, 16)
      };
    }

    if (metadataTypes.includes('comprehensive')) {
      // Include all metadata types
      const allTypes = ['basic', 'structural', 'semantic', 'technical'];
      for (const type of allTypes) {
        if (!metadataTypes.includes(type)) {
          const typeMetadata = await this.extractDocumentMetadata(content, filename, fileStats, [type]);
          metadata[type] = typeMetadata[type];
        }
      }
    }

    return metadata;
  }

  detectHeaders(lines) {
    return lines.some(line => 
      line.match(/^#{1,6}\s/) || // Markdown headers
      line.match(/^[A-Z][A-Z\s]{5,}$/) || // All caps headers
      line.match(/^\d+\.\s[A-Z]/) // Numbered headers
    );
  }

  detectLists(lines) {
    return lines.some(line => 
      line.match(/^\s*[-*+]\s/) || // Bullet lists
      line.match(/^\s*\d+\.\s/) // Numbered lists
    );
  }

  detectNumberedSections(lines) {
    return lines.some(line => 
      line.match(/^\d+(\.\d+)*\.?\s/)
    );
  }

  analyzeDocumentHierarchy(lines) {
    const hierarchy = [];
    let currentLevel = 0;
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.match(/^#{1,6}\s/)) {
        // Markdown header
        const level = (trimmed.match(/^#+/) || [''])[0].length;
        hierarchy.push({ level, text: trimmed.replace(/^#+\s*/, ''), type: 'header' });
      } else if (trimmed.match(/^\d+(\.\d+)*\.?\s/)) {
        // Numbered section
        const numbers = trimmed.match(/^\d+(\.\d+)*/)[0];
        const level = numbers.split('.').length;
        hierarchy.push({ level, text: trimmed, type: 'numbered_section' });
      }
    }
    
    return hierarchy;
  }

  detectDocumentSections(content) {
    const sections = [];
    const lines = content.split('\n');
    let currentSection = { title: 'Introduction', content: '', start_line: 0 };
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (this.isLikelySection(line)) {
        // Save current section
        if (currentSection.content.trim()) {
          currentSection.end_line = i - 1;
          sections.push({ ...currentSection });
        }
        
        // Start new section
        currentSection = {
          title: line.replace(/^#+\s*/, '').replace(/^\d+(\.\d+)*\.?\s*/, ''),
          content: '',
          start_line: i
        };
      } else {
        currentSection.content += line + '\n';
      }
    }
    
    // Add final section
    if (currentSection.content.trim()) {
      currentSection.end_line = lines.length - 1;
      sections.push(currentSection);
    }
    
    return sections;
  }

  isLikelySection(line) {
    return line.match(/^#{1,6}\s/) || // Markdown header
           line.match(/^[A-Z][A-Z\s]{5,}$/) || // All caps
           line.match(/^\d+(\.\d+)*\.?\s[A-Z]/) || // Numbered section
           line.match(/^[IVX]+\.\s/) || // Roman numerals
           line.match(/^[A-Z]\.\s/); // Letter sections
  }

  analyzeFormatting(content) {
    return {
      has_bold: content.includes('**') || content.includes('__'),
      has_italic: content.includes('*') || content.includes('_'),
      has_code: content.includes('`'),
      has_links: content.includes('http') || content.includes('['),
      has_tables: content.includes('|'),
      indentation_style: this.detectIndentationStyle(content),
      line_ending_style: content.includes('\r\n') ? 'CRLF' : 'LF'
    };
  }

  detectIndentationStyle(content) {
    const lines = content.split('\n');
    let tabCount = 0;
    let spaceCount = 0;
    
    for (const line of lines) {
      if (line.startsWith('\t')) tabCount++;
      if (line.startsWith('  ')) spaceCount++;
    }
    
    if (tabCount > spaceCount) return 'tabs';
    if (spaceCount > tabCount) return 'spaces';
    return 'mixed';
  }

  analyzeSentiment(tokens) {
    const sentiment = this.sentimentAnalyzer.getSentiment(tokens);
    return {
      score: sentiment,
      classification: sentiment > 0.1 ? 'positive' : sentiment < -0.1 ? 'negative' : 'neutral'
    };
  }

  calculateReadability(text, tokens) {
    const sentences = text.split(/[.!?]+/).length;
    const avgWordsPerSentence = tokens.length / sentences;
    const avgSyllablesPerWord = 1.5; // Simplified estimate
    
    // Flesch Reading Ease Score approximation
    const fleschScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    
    return {
      flesch_score: Math.max(0, Math.min(100, fleschScore)),
      avg_words_per_sentence: avgWordsPerSentence,
      difficulty: fleschScore > 60 ? 'easy' : fleschScore > 30 ? 'moderate' : 'difficult'
    };
  }

  async extractTopics(content) {
    // Simplified topic extraction using keyword frequency
    const tokens = this.tokenizer_nlp.tokenize(content.toLowerCase());
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    const filteredTokens = tokens.filter(token => !stopWords.has(token) && token.length > 3);
    
    const frequency = {};
    filteredTokens.forEach(token => {
      frequency[token] = (frequency[token] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ topic: word, frequency: count }));
  }

  async extractPoliticalThemes(content) {
    const politicalKeywords = {
      'healthcare': ['health', 'medical', 'insurance', 'medicare', 'medicaid'],
      'education': ['school', 'university', 'student', 'education', 'learning'],
      'economy': ['economic', 'financial', 'budget', 'tax', 'fiscal'],
      'environment': ['climate', 'environmental', 'green', 'renewable', 'carbon'],
      'security': ['security', 'defense', 'military', 'safety', 'protection']
    };
    
    const themes = {};
    const contentLower = content.toLowerCase();
    
    for (const [theme, keywords] of Object.entries(politicalKeywords)) {
      const matches = keywords.filter(keyword => contentLower.includes(keyword));
      if (matches.length > 0) {
        themes[theme] = matches.length;
      }
    }
    
    return Object.entries(themes)
      .sort(([,a], [,b]) => b - a)
      .map(([theme, score]) => ({ theme, relevance_score: score }));
  }

  async extractNamedEntities(content) {
    // Simplified NER - in production would use proper NER models
    const entities = [];
    const patterns = {
      'PERSON': /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g,
      'LOCATION': /\b(?:United States|America|Washington|California|Texas|New York)\b/g,
      'DATE': /\b\d{1,2}\/\d{1,2}\/\d{4}\b|\b\d{4}\b/g
    };
    
    for (const [type, pattern] of Object.entries(patterns)) {
      const matches = content.match(pattern) || [];
      matches.forEach(match => {
        entities.push({
          type,
          text: match,
          confidence: 0.8
        });
      });
    }
    
    return entities;
  }

  async storeAnalysisResult(documentId, filename, analysis) {
    try {
      await this.pgClient.query(`
        INSERT INTO document_analysis 
        (document_id, filename, analysis_result, sentiment_score, key_topics, political_themes)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT (document_id) 
        DO UPDATE SET analysis_result = $3, sentiment_score = $4, updated_at = CURRENT_TIMESTAMP
      `, [
        documentId,
        filename,
        JSON.stringify(analysis),
        analysis.sentiment_analysis?.score || 0,
        analysis.key_topics?.map(t => t.topic) || [],
        analysis.political_themes?.map(t => t.theme) || []
      ]);
    } catch (error) {
      this.logger.error('Error storing analysis result:', error);
    }
  }

  setupHealthEndpoint() {
    const app = express();
    app.use(helmet());
    app.use(cors());

    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'document-intelligence-mcp',
        timestamp: new Date().toISOString(),
        database: this.pgClient ? 'connected' : 'not connected',
        redis: this.redisClient ? 'connected' : 'not connected'
      });
    });

    const port = process.env.MCP_SERVER_PORT || 8089;
    app.listen(port, () => {
      this.logger.info(`Document Intelligence MCP Server health endpoint running on port ${port}`);
    });
  }

  async start() {
    await this.initialize();
    await this.server.start();
    this.logger.info('Document Intelligence MCP Server started');
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    await this.server.stop();
  }
}

// Start the server
const server = new DocumentIntelligenceServer();

process.on('SIGINT', async () => {
  console.log('Shutting down...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);