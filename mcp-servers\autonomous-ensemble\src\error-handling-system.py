#!/usr/bin/env python3
"""
Error Handling and Fallback System for Political Document Processing
Comprehensive error handling with circuit breakers, retries, and fallback mechanisms
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from contextlib import asynccontextmanager
import traceback
from functools import wraps
import statistics
import random

class ErrorType(Enum):
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    AUTHENTICATION_ERROR = "authentication_error"
    VALIDATION_ERROR = "validation_error"
    PROCESSING_ERROR = "processing_error"
    RESOURCE_ERROR = "resource_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

@dataclass
class ErrorRecord:
    """Error record for tracking and analysis"""
    error_id: str
    error_type: ErrorType
    severity: ErrorSeverity
    component: str
    message: str
    timestamp: datetime
    context: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    retry_count: int = 0
    cost_impact: float = 0.0

@dataclass
class FallbackConfig:
    """Fallback configuration"""
    primary_handler: str
    fallback_handlers: List[str]
    max_retries: int = 3
    retry_delay: float = 1.0
    backoff_multiplier: float = 2.0
    max_retry_delay: float = 60.0
    timeout: float = 30.0
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 60.0

@dataclass
class CircuitBreaker:
    """Circuit breaker for handling service failures"""
    name: str
    failure_threshold: int
    recovery_timeout: float
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    success_count: int = 0
    total_requests: int = 0
    last_success_time: Optional[datetime] = None

class RetryPolicy:
    """Retry policy with exponential backoff"""
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_multiplier: float = 2.0,
                 jitter: bool = True):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_multiplier = backoff_multiplier
        self.jitter = jitter
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt"""
        delay = self.base_delay * (self.backoff_multiplier ** attempt)
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            delay *= (0.5 + random.random() * 0.5)  # Add jitter
        
        return delay

class ErrorHandlingSystem:
    """
    Comprehensive error handling system for political document processing
    Adapted from CEO quality control patterns
    """
    
    def __init__(self):
        self.error_records: Dict[str, ErrorRecord] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.fallback_configs: Dict[str, FallbackConfig] = {}
        self.error_handlers: Dict[ErrorType, List[Callable]] = {}
        self.recovery_strategies: Dict[str, Callable] = {}
        
        # Statistics
        self.error_stats = {
            "total_errors": 0,
            "resolved_errors": 0,
            "active_errors": 0,
            "error_by_type": {},
            "error_by_component": {},
            "average_resolution_time": 0.0,
            "circuit_breaker_trips": 0
        }
        
        # Configuration
        self.max_error_history = 10000
        self.error_cleanup_interval = 3600  # 1 hour
        self.monitoring_interval = 60  # 1 minute
        self.alert_thresholds = {
            "error_rate": 0.1,  # 10% error rate
            "critical_errors": 5,
            "circuit_breaker_trips": 3
        }
        
        # Background tasks
        self.cleanup_task = None
        self.monitoring_task = None
        self.running = False
        
        # Event callbacks
        self.event_callbacks = {
            "error_occurred": [],
            "error_resolved": [],
            "circuit_breaker_opened": [],
            "circuit_breaker_closed": [],
            "fallback_triggered": [],
            "alert_triggered": []
        }
        
        # Initialize logger
        self.logger = logging.getLogger(__name__)
        
        # Initialize default configurations
        self._initialize_default_configs()
    
    def _initialize_default_configs(self):
        """Initialize default configurations"""
        # Default fallback configs for different components
        self.fallback_configs = {
            "claude_sonnet": FallbackConfig(
                primary_handler="claude_sonnet",
                fallback_handlers=["gpt4", "gemini"],
                max_retries=3,
                circuit_breaker_threshold=5,
                circuit_breaker_timeout=300.0
            ),
            "gpt4": FallbackConfig(
                primary_handler="gpt4",
                fallback_handlers=["claude_sonnet", "gemini"],
                max_retries=2,
                circuit_breaker_threshold=3,
                circuit_breaker_timeout=180.0
            ),
            "perplexity": FallbackConfig(
                primary_handler="perplexity",
                fallback_handlers=["brave_search", "web_search"],
                max_retries=5,
                circuit_breaker_threshold=10,
                circuit_breaker_timeout=60.0
            ),
            "vector_search": FallbackConfig(
                primary_handler="chroma_db",
                fallback_handlers=["fallback_search"],
                max_retries=3,
                circuit_breaker_threshold=5,
                circuit_breaker_timeout=120.0
            )
        }
        
        # Initialize circuit breakers
        for component, config in self.fallback_configs.items():
            self.circuit_breakers[component] = CircuitBreaker(
                name=component,
                failure_threshold=config.circuit_breaker_threshold,
                recovery_timeout=config.circuit_breaker_timeout
            )
    
    async def start(self):
        """Start the error handling system"""
        self.running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Error Handling System started")
    
    async def stop(self):
        """Stop the error handling system"""
        self.running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
        if self.monitoring_task:
            self.monitoring_task.cancel()
        self.logger.info("Error Handling System stopped")
    
    def with_error_handling(self, component: str, error_types: List[ErrorType] = None):
        """Decorator for automatic error handling"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                return await self.execute_with_fallback(
                    func, component, error_types, *args, **kwargs
                )
            return wrapper
        return decorator
    
    async def execute_with_fallback(self, func: Callable, component: str,
                                   error_types: List[ErrorType] = None,
                                   *args, **kwargs) -> Any:
        """Execute function with automatic fallback and error handling"""
        config = self.fallback_configs.get(component)
        if not config:
            # No fallback config, execute directly
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                await self._handle_error(e, component, error_types)
                raise
        
        # Check circuit breaker
        circuit_breaker = self.circuit_breakers.get(component)
        if circuit_breaker and circuit_breaker.state == CircuitState.OPEN:
            if not await self._should_attempt_recovery(circuit_breaker):
                raise Exception(f"Circuit breaker open for {component}")
        
        retry_policy = RetryPolicy(
            max_retries=config.max_retries,
            base_delay=config.retry_delay,
            max_delay=config.max_retry_delay,
            backoff_multiplier=config.backoff_multiplier
        )
        
        # Try primary handler
        for attempt in range(config.max_retries + 1):
            try:
                # Update circuit breaker state
                if circuit_breaker and circuit_breaker.state == CircuitState.OPEN:
                    circuit_breaker.state = CircuitState.HALF_OPEN
                
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=config.timeout
                )
                
                # Success - update circuit breaker
                if circuit_breaker:
                    await self._record_success(circuit_breaker)
                
                return result
                
            except Exception as e:
                error_type = self._classify_error(e)
                
                # Record error
                error_record = await self._record_error(
                    e, component, error_type, attempt
                )
                
                # Update circuit breaker
                if circuit_breaker:
                    await self._record_failure(circuit_breaker)
                
                # Check if we should retry
                if attempt < config.max_retries and self._should_retry(error_type):
                    delay = retry_policy.calculate_delay(attempt)
                    await asyncio.sleep(delay)
                    continue
                
                # Try fallback handlers
                for fallback_handler in config.fallback_handlers:
                    try:
                        await self._emit_event("fallback_triggered", {
                            "component": component,
                            "fallback_handler": fallback_handler,
                            "error_type": error_type.value
                        })
                        
                        # Execute fallback
                        fallback_result = await self._execute_fallback(
                            fallback_handler, func, *args, **kwargs
                        )
                        
                        # Mark original error as resolved
                        error_record.resolved = True
                        error_record.resolution_time = datetime.now()
                        
                        return fallback_result
                        
                    except Exception as fallback_error:
                        await self._record_error(
                            fallback_error, fallback_handler, 
                            self._classify_error(fallback_error), 0
                        )
                        continue
                
                # All fallbacks failed
                raise e
    
    async def _execute_fallback(self, handler_name: str, original_func: Callable,
                               *args, **kwargs) -> Any:
        """Execute fallback handler"""
        # This would be implemented based on your specific fallback strategies
        # For now, we'll simulate different fallback approaches
        
        if handler_name == "gpt4":
            # Fallback to GPT-4
            return await self._simulate_gpt4_fallback(*args, **kwargs)
        elif handler_name == "gemini":
            # Fallback to Gemini
            return await self._simulate_gemini_fallback(*args, **kwargs)
        elif handler_name == "brave_search":
            # Fallback to Brave search
            return await self._simulate_brave_search_fallback(*args, **kwargs)
        elif handler_name == "fallback_search":
            # Fallback to simple search
            return await self._simulate_fallback_search(*args, **kwargs)
        else:
            # Generic fallback
            return await self._simulate_generic_fallback(*args, **kwargs)
    
    async def _simulate_gpt4_fallback(self, *args, **kwargs) -> Dict[str, Any]:
        """Simulate GPT-4 fallback"""
        await asyncio.sleep(0.5)  # Simulate processing time
        return {
            "result": "GPT-4 fallback result",
            "confidence": 0.7,
            "fallback_used": True,
            "handler": "gpt4"
        }
    
    async def _simulate_gemini_fallback(self, *args, **kwargs) -> Dict[str, Any]:
        """Simulate Gemini fallback"""
        await asyncio.sleep(0.3)  # Simulate processing time
        return {
            "result": "Gemini fallback result",
            "confidence": 0.6,
            "fallback_used": True,
            "handler": "gemini"
        }
    
    async def _simulate_brave_search_fallback(self, *args, **kwargs) -> Dict[str, Any]:
        """Simulate Brave search fallback"""
        await asyncio.sleep(0.8)  # Simulate processing time
        return {
            "result": "Brave search fallback result",
            "sources": ["fallback_source_1", "fallback_source_2"],
            "fallback_used": True,
            "handler": "brave_search"
        }
    
    async def _simulate_fallback_search(self, *args, **kwargs) -> Dict[str, Any]:
        """Simulate fallback search"""
        await asyncio.sleep(1.0)  # Simulate processing time
        return {
            "result": "Fallback search result",
            "limited_results": True,
            "fallback_used": True,
            "handler": "fallback_search"
        }
    
    async def _simulate_generic_fallback(self, *args, **kwargs) -> Dict[str, Any]:
        """Simulate generic fallback"""
        await asyncio.sleep(0.5)  # Simulate processing time
        return {
            "result": "Generic fallback result",
            "fallback_used": True,
            "handler": "generic"
        }
    
    def _classify_error(self, error: Exception) -> ErrorType:
        """Classify error type"""
        error_msg = str(error).lower()
        
        if "timeout" in error_msg:
            return ErrorType.TIMEOUT_ERROR
        elif "rate limit" in error_msg or "429" in error_msg:
            return ErrorType.RATE_LIMIT_ERROR
        elif "authentication" in error_msg or "401" in error_msg:
            return ErrorType.AUTHENTICATION_ERROR
        elif "validation" in error_msg or "400" in error_msg:
            return ErrorType.VALIDATION_ERROR
        elif "network" in error_msg or "connection" in error_msg:
            return ErrorType.NETWORK_ERROR
        elif "resource" in error_msg or "memory" in error_msg:
            return ErrorType.RESOURCE_ERROR
        elif "processing" in error_msg:
            return ErrorType.PROCESSING_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def _should_retry(self, error_type: ErrorType) -> bool:
        """Determine if error should be retried"""
        non_retryable_errors = [
            ErrorType.AUTHENTICATION_ERROR,
            ErrorType.VALIDATION_ERROR
        ]
        return error_type not in non_retryable_errors
    
    async def _record_error(self, error: Exception, component: str,
                           error_type: ErrorType, retry_count: int) -> ErrorRecord:
        """Record error for tracking and analysis"""
        error_id = f"{component}_{int(time.time())}_{id(error)}"
        
        # Determine severity
        severity = self._determine_severity(error_type, component)
        
        error_record = ErrorRecord(
            error_id=error_id,
            error_type=error_type,
            severity=severity,
            component=component,
            message=str(error),
            timestamp=datetime.now(),
            context={"retry_count": retry_count},
            stack_trace=traceback.format_exc(),
            retry_count=retry_count
        )
        
        self.error_records[error_id] = error_record
        
        # Update statistics
        self.error_stats["total_errors"] += 1
        self.error_stats["active_errors"] += 1
        
        error_type_key = error_type.value
        self.error_stats["error_by_type"][error_type_key] = \
            self.error_stats["error_by_type"].get(error_type_key, 0) + 1
        
        self.error_stats["error_by_component"][component] = \
            self.error_stats["error_by_component"].get(component, 0) + 1
        
        # Emit event
        await self._emit_event("error_occurred", {
            "error_id": error_id,
            "error_type": error_type.value,
            "component": component,
            "severity": severity.value,
            "message": str(error)
        })
        
        # Check for alert conditions
        await self._check_alert_conditions(error_type, component)
        
        self.logger.error(f"Error recorded: {error_id} - {error}")
        return error_record
    
    def _determine_severity(self, error_type: ErrorType, component: str) -> ErrorSeverity:
        """Determine error severity"""
        # Critical errors
        if error_type in [ErrorType.RESOURCE_ERROR, ErrorType.AUTHENTICATION_ERROR]:
            return ErrorSeverity.CRITICAL
        
        # High severity for core components
        if component in ["claude_sonnet", "gpt4"] and error_type == ErrorType.PROCESSING_ERROR:
            return ErrorSeverity.HIGH
        
        # Medium severity for network/timeout errors
        if error_type in [ErrorType.NETWORK_ERROR, ErrorType.TIMEOUT_ERROR]:
            return ErrorSeverity.MEDIUM
        
        # Low severity for others
        return ErrorSeverity.LOW
    
    async def _record_success(self, circuit_breaker: CircuitBreaker):
        """Record successful operation"""
        circuit_breaker.success_count += 1
        circuit_breaker.total_requests += 1
        circuit_breaker.last_success_time = datetime.now()
        
        # Close circuit breaker if it was half-open
        if circuit_breaker.state == CircuitState.HALF_OPEN:
            circuit_breaker.state = CircuitState.CLOSED
            circuit_breaker.failure_count = 0
            
            await self._emit_event("circuit_breaker_closed", {
                "component": circuit_breaker.name,
                "success_count": circuit_breaker.success_count
            })
    
    async def _record_failure(self, circuit_breaker: CircuitBreaker):
        """Record failed operation"""
        circuit_breaker.failure_count += 1
        circuit_breaker.total_requests += 1
        circuit_breaker.last_failure_time = datetime.now()
        
        # Open circuit breaker if threshold reached
        if (circuit_breaker.failure_count >= circuit_breaker.failure_threshold and
            circuit_breaker.state != CircuitState.OPEN):
            
            circuit_breaker.state = CircuitState.OPEN
            self.error_stats["circuit_breaker_trips"] += 1
            
            await self._emit_event("circuit_breaker_opened", {
                "component": circuit_breaker.name,
                "failure_count": circuit_breaker.failure_count,
                "threshold": circuit_breaker.failure_threshold
            })
    
    async def _should_attempt_recovery(self, circuit_breaker: CircuitBreaker) -> bool:
        """Check if circuit breaker should attempt recovery"""
        if circuit_breaker.state != CircuitState.OPEN:
            return True
        
        if not circuit_breaker.last_failure_time:
            return True
        
        time_since_failure = datetime.now() - circuit_breaker.last_failure_time
        return time_since_failure.total_seconds() >= circuit_breaker.recovery_timeout
    
    async def _check_alert_conditions(self, error_type: ErrorType, component: str):
        """Check if alert conditions are met"""
        # Check error rate
        if self.error_stats["total_errors"] > 0:
            error_rate = self.error_stats["active_errors"] / self.error_stats["total_errors"]
            if error_rate > self.alert_thresholds["error_rate"]:
                await self._emit_event("alert_triggered", {
                    "type": "high_error_rate",
                    "error_rate": error_rate,
                    "threshold": self.alert_thresholds["error_rate"]
                })
        
        # Check critical errors
        critical_errors = len([
            e for e in self.error_records.values()
            if e.severity == ErrorSeverity.CRITICAL and not e.resolved
        ])
        
        if critical_errors >= self.alert_thresholds["critical_errors"]:
            await self._emit_event("alert_triggered", {
                "type": "critical_errors",
                "count": critical_errors,
                "threshold": self.alert_thresholds["critical_errors"]
            })
        
        # Check circuit breaker trips
        if self.error_stats["circuit_breaker_trips"] >= self.alert_thresholds["circuit_breaker_trips"]:
            await self._emit_event("alert_triggered", {
                "type": "circuit_breaker_trips",
                "count": self.error_stats["circuit_breaker_trips"],
                "threshold": self.alert_thresholds["circuit_breaker_trips"]
            })
    
    async def _cleanup_loop(self):
        """Background cleanup of old error records"""
        while self.running:
            try:
                await self._cleanup_old_errors()
                await asyncio.sleep(self.error_cleanup_interval)
            except Exception as e:
                self.logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _cleanup_old_errors(self):
        """Clean up old error records"""
        if len(self.error_records) <= self.max_error_history:
            return
        
        # Sort by timestamp
        sorted_errors = sorted(
            self.error_records.items(),
            key=lambda x: x[1].timestamp
        )
        
        # Remove oldest errors
        removal_count = len(self.error_records) - self.max_error_history
        for i in range(removal_count):
            error_id, error_record = sorted_errors[i]
            
            # Only remove resolved errors
            if error_record.resolved:
                del self.error_records[error_id]
                if not error_record.resolved:
                    self.error_stats["active_errors"] -= 1
    
    async def _monitoring_loop(self):
        """Background monitoring and analysis"""
        while self.running:
            try:
                await self._analyze_error_patterns()
                await self._update_statistics()
                await asyncio.sleep(self.monitoring_interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _analyze_error_patterns(self):
        """Analyze error patterns and trends"""
        # Calculate resolution times
        resolved_errors = [
            e for e in self.error_records.values()
            if e.resolved and e.resolution_time
        ]
        
        if resolved_errors:
            resolution_times = [
                (e.resolution_time - e.timestamp).total_seconds()
                for e in resolved_errors
            ]
            self.error_stats["average_resolution_time"] = statistics.mean(resolution_times)
        
        # Update resolved count
        self.error_stats["resolved_errors"] = len(resolved_errors)
        
        # Recalculate active errors
        active_errors = len([
            e for e in self.error_records.values()
            if not e.resolved
        ])
        self.error_stats["active_errors"] = active_errors
    
    async def _update_statistics(self):
        """Update error statistics"""
        # Reset circuit breaker stats if needed
        current_time = datetime.now()
        
        for circuit_breaker in self.circuit_breakers.values():
            if (circuit_breaker.last_failure_time and 
                circuit_breaker.state == CircuitState.OPEN):
                
                time_since_failure = current_time - circuit_breaker.last_failure_time
                if time_since_failure.total_seconds() >= circuit_breaker.recovery_timeout:
                    circuit_breaker.state = CircuitState.HALF_OPEN
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]):
        """Emit event to registered callbacks"""
        callbacks = self.event_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                await callback(data)
            except Exception as e:
                self.logger.error(f"Error in event callback: {e}")
    
    def add_event_callback(self, event_type: str, callback: Callable):
        """Add event callback"""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        self.event_callbacks[event_type].append(callback)
    
    async def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        return {
            **self.error_stats,
            "circuit_breakers": {
                name: {
                    "state": cb.state.value,
                    "failure_count": cb.failure_count,
                    "success_count": cb.success_count,
                    "total_requests": cb.total_requests,
                    "failure_rate": cb.failure_count / max(1, cb.total_requests)
                }
                for name, cb in self.circuit_breakers.items()
            }
        }
    
    async def get_error_details(self, error_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed error information"""
        if error_id not in self.error_records:
            return None
        
        error_record = self.error_records[error_id]
        return {
            "error_id": error_record.error_id,
            "error_type": error_record.error_type.value,
            "severity": error_record.severity.value,
            "component": error_record.component,
            "message": error_record.message,
            "timestamp": error_record.timestamp.isoformat(),
            "resolved": error_record.resolved,
            "resolution_time": error_record.resolution_time.isoformat() if error_record.resolution_time else None,
            "retry_count": error_record.retry_count,
            "context": error_record.context,
            "stack_trace": error_record.stack_trace
        }
    
    async def resolve_error(self, error_id: str) -> bool:
        """Manually resolve an error"""
        if error_id not in self.error_records:
            return False
        
        error_record = self.error_records[error_id]
        error_record.resolved = True
        error_record.resolution_time = datetime.now()
        
        self.error_stats["active_errors"] -= 1
        self.error_stats["resolved_errors"] += 1
        
        await self._emit_event("error_resolved", {
            "error_id": error_id,
            "component": error_record.component,
            "resolution_time": error_record.resolution_time.isoformat()
        })
        
        return True
    
    async def reset_circuit_breaker(self, component: str) -> bool:
        """Manually reset circuit breaker"""
        if component not in self.circuit_breakers:
            return False
        
        circuit_breaker = self.circuit_breakers[component]
        circuit_breaker.state = CircuitState.CLOSED
        circuit_breaker.failure_count = 0
        circuit_breaker.success_count = 0
        
        await self._emit_event("circuit_breaker_closed", {
            "component": component,
            "manual_reset": True
        })
        
        return True
    
    async def get_recovery_recommendations(self) -> List[Dict[str, Any]]:
        """Get recovery recommendations based on error patterns"""
        recommendations = []
        
        # Check for frequent errors
        component_errors = {}
        for error_record in self.error_records.values():
            if not error_record.resolved:
                component = error_record.component
                component_errors[component] = component_errors.get(component, 0) + 1
        
        for component, count in component_errors.items():
            if count >= 5:
                recommendations.append({
                    "type": "frequent_errors",
                    "component": component,
                    "error_count": count,
                    "recommendation": f"Investigate {component} for recurring issues"
                })
        
        # Check circuit breaker status
        for name, cb in self.circuit_breakers.items():
            if cb.state == CircuitState.OPEN:
                recommendations.append({
                    "type": "circuit_breaker_open",
                    "component": name,
                    "recommendation": f"Circuit breaker open for {name}, consider manual reset or investigation"
                })
        
        # Check error types
        error_type_counts = {}
        for error_record in self.error_records.values():
            if not error_record.resolved:
                error_type = error_record.error_type.value
                error_type_counts[error_type] = error_type_counts.get(error_type, 0) + 1
        
        for error_type, count in error_type_counts.items():
            if count >= 3:
                recommendations.append({
                    "type": "error_pattern",
                    "error_type": error_type,
                    "count": count,
                    "recommendation": f"Pattern of {error_type} errors detected, investigate root cause"
                })
        
        return recommendations


# Example usage
async def main():
    error_system = ErrorHandlingSystem()
    await error_system.start()
    
    # Example function that might fail
    @error_system.with_error_handling("claude_sonnet")
    async def process_document(content: str) -> Dict[str, Any]:
        if "error" in content.lower():
            raise Exception("Processing error occurred")
        
        await asyncio.sleep(0.5)  # Simulate processing
        return {"result": "Document processed successfully"}
    
    # Test error handling
    try:
        result = await process_document("This is a test document")
        print(f"Success: {result}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with error
    try:
        result = await process_document("This document has an error")
        print(f"Success: {result}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Get error statistics
    stats = await error_system.get_error_stats()
    print(f"Error stats: {stats}")
    
    # Get recovery recommendations
    recommendations = await error_system.get_recovery_recommendations()
    print(f"Recommendations: {recommendations}")
    
    await error_system.stop()

if __name__ == "__main__":
    asyncio.run(main())