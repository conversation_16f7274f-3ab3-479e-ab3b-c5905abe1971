FROM node:18-alpine

# Create app directory
WORKDIR /app

# Create logs directory
RUN mkdir -p /app/logs

# Install security updates and required packages
RUN apk update && apk upgrade && apk add --no-cache \
    dumb-init \
    curl \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcpuser -u 1001 -G nodejs

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY . .

# Create scripts directory and data refresh script
RUN mkdir -p scripts && \
    echo '#!/usr/bin/env node\nconsole.log("Data refresh script placeholder");' > scripts/refresh-indicators.js && \
    chmod +x scripts/refresh-indicators.js

# Set proper permissions
RUN chown -R mcpuser:nodejs /app && \
    chmod +x server.js

# Switch to non-root user
<PERSON><PERSON> mcpuser

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8091/health || exit 1

# Expose port
EXPOSE 8091

# Environment variables documentation
ENV MCP_SERVER_PORT=8091 \
    LOG_LEVEL=info \
    NODE_ENV=production \
    CACHE_TTL=3600 \
    MAX_DATA_POINTS=1000

# Labels for metadata
LABEL maintainer="Beau Lewis" \
      description="Economic Analysis MCP Server" \
      version="1.0.0" \
      org.opencontainers.image.title="Economic Analysis MCP Server" \
      org.opencontainers.image.description="Comprehensive economic analysis tools for political and policy analysis" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.authors="Beau Lewis" \
      org.opencontainers.image.source="https://github.com/beau-lewis/political-document-system" \
      org.opencontainers.image.licenses="MIT"

# Start the application
CMD ["node", "server.js"]