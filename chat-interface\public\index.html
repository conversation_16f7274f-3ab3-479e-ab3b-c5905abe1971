<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Political Document Processing - <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Political Document Processing System</h1>
            <h2><PERSON> for Economic Justice</h2>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">Connecting...</span>
            </div>
        </header>

        <main class="main-content">
            <div class="sidebar">
                <div class="sidebar-section">
                    <h3>Document Processing</h3>
                    <form id="documentForm" class="document-form">
                        <div class="form-group">
                            <label for="taskType">Task Type:</label>
                            <select id="taskType" required>
                                <option value="">Select task type</option>
                                <option value="generate_whitepaper">Generate White Paper</option>
                                <option value="edit">Edit Document</option>
                                <option value="combine">Combine Documents</option>
                                <option value="generate_policy_brief">Generate Policy Brief</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="topic">Topic:</label>
                            <input type="text" id="topic" placeholder="e.g., Medicare for All Implementation" required>
                        </div>

                        <div class="form-group">
                            <label for="category">Category:</label>
                            <select id="category" required>
                                <option value="">Select category</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="education">Education</option>
                                <option value="economic_justice">Economic Justice</option>
                                <option value="climate">Climate & Environment</option>
                                <option value="democratic_reform">Democratic Reform</option>
                                <option value="social_trust_fund">Social Trust Fund</option>
                                <option value="constitutional_amendments">Constitutional Amendments</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tokenTier">Token Allocation:</label>
                            <select id="tokenTier">
                                <option value="1">Tier 1 - 5K tokens (Quick)</option>
                                <option value="2" selected>Tier 2 - 10K tokens (Standard)</option>
                                <option value="3">Tier 3 - 25K tokens (Detailed)</option>
                                <option value="4">Tier 4 - 50K tokens (Comprehensive)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="researchLevel">Research Level:</label>
                            <select id="researchLevel">
                                <option value="basic">Basic</option>
                                <option value="standard" selected>Standard</option>
                                <option value="comprehensive">Comprehensive</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="outputFormat">Output Format:</label>
                            <select id="outputFormat">
                                <option value="markdown" selected>Markdown</option>
                                <option value="pdf">PDF</option>
                                <option value="docx">Word Document</option>
                                <option value="html">HTML</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="fileUpload">Upload Document (optional):</label>
                            <input type="file" id="fileUpload" accept=".txt,.md,.pdf,.docx">
                        </div>

                        <button type="submit" class="btn-primary">Process Document</button>
                    </form>
                </div>

                <div class="sidebar-section">
                    <h3>Active Jobs</h3>
                    <div id="activeJobs" class="active-jobs">
                        <p class="no-jobs">No active processing jobs</p>
                    </div>
                </div>
            </div>

            <div class="chat-area">
                <div class="chat-header">
                    <h3>Political Assistant Chat</h3>
                    <div class="typing-indicator" id="typingIndicator" style="display: none;">
                        <span>AI Assistant is typing...</span>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="message system-message">
                        <div class="message-content">
                            <p>Welcome to the Political Document Processing System. I'm here to help you create, edit, and analyze political documents aligned with Beau Lewis's vision for economic justice and democratic renewal.</p>
                            <p>You can ask me questions about policy positions, request document generation, or start a conversation about political strategy.</p>
                        </div>
                        <div class="message-time">System</div>
                    </div>
                </div>

                <div class="chat-input-area">
                    <div class="chat-input-container">
                        <input type="text" id="chatInput" placeholder="Ask about policy, request documents, or start a political discussion..." />
                        <button id="sendButton" class="btn-send">Send</button>
                    </div>
                </div>
            </div>
        </main>

        <div class="status-bar">
            <div class="status-item">
                <span>Session: </span>
                <span id="sessionId">Not connected</span>
            </div>
            <div class="status-item">
                <span>Documents processed today: </span>
                <span id="documentsToday">0</span>
            </div>
            <div class="status-item">
                <span>System load: </span>
                <span id="systemLoad">Normal</span>
            </div>
        </div>
    </div>

    <!-- Document Processing Modal -->
    <div id="processingModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Processing Document</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="processing-status">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="processingStatus">Initializing document processing...</p>
                    <p id="estimatedTime">Estimated completion: calculating...</p>
                </div>
                <div id="processingSteps" class="processing-steps">
                    <!-- Dynamic steps will be added here -->
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>