# Document Version Mapping - n8n_workflow_windows

This document maps the latest version of each document type with all previous versions listed underneath. Review carefully before deleting any documents.

---

## 1. Manifesto Documents

### Latest: `manifesto/manifesto_for_agents.md`
Previous versions:
- `manifesto_claude.md` (Claude-specific version)
- `manifesto_v3.md` (Version 3)
- `manifesto_v2.md` (Version 2)
- `manifesto.md` (Original version)
- `suggestions/rovodev_manifesto.md` (RovoDev version)
- `n8n docs/rovodev_manifesto.md` (Duplicate)

### Related manifesto documents:
- `manifesto/core_essence.md`
- `manifesto/style_guide.md`
- `manifesto/voice_guidelines_beau_lewis.md`

---

## 2. Handoff Documents

### Latest: `HANDOFF_DOCUMENT_PHASE_4.md`
Previous versions:
- `HANDOFF_DOCUMENT_PHASE_3.md`
- `HANDOFF_DOCUMENT_PHASE_2.md`
- `HANDOFF_DOCUMENT.md` (Original Phase 1)

### Latest Summary: `handoff-summary-2025-01-28.md`

---

## 3. Project Status Documents

### Latest: `PROJECT_STATUS.md`
Previous versions:
- `project_status.d` (Old stub file - 43B)

### Phase Completion Summaries:
- `PHASE_3_COMPLETION_SUMMARY.md` (Latest phase summary)
- `PHASE_2_COMPLETION_SUMMARY.md`
- `PHASE_2_ENHANCEMENTS.md`
- `project_completion_summary.md` (Early summary)

---

## 4. n8n Workflow Setup Documents

### Latest: `n8n docs/n8n_workflow_setup.md` (29KB, 620 lines)
Previous versions:
- `suggestions/n8n_workflow_setup.md` (Duplicate - 29KB)
- `n8n_workflow_setup.md` (Root stub - 75B, 2 lines)

---

## 5. n8n Workflow Design Documents

### Latest: `n8n_workflow_final_design.md` (27KB, 664 lines)
Previous versions:
- `mvp_workflow_n8n_blueprint.md`
- `mvp_workflow_documentation.md`
- `n8n_workflow_implementation.md`
- `cursor_n8n_workflow.md`
- `kilocode_n8n_workflow.md`

### Related workflow files:
- `mvp_n8n_implementation_fixed.json` (Fixed version)
- `mvp_n8n_implementation.json` (Original)
- `workflows/enhanced-political-document-processor.json` (Enhanced)
- `workflows/political-document-processor.json` (Original)

---

## 6. Claude-Related Documents

### Latest: `CLAUDE-CODE-LEFTOFF.md`
Previous versions:
- `CLAUDE-CODE-HANDOFF.md`
- `CLAUDE-CODE-N8N-PLAN.MD`
- `claudes-plan.md`
- `CLAUDE.md` (Root)
- `mcp-servers/n8n-mcp-server/CLAUDE.md` (MCP-specific)

---

## 7. RovoDev Temporary Documents

### ⚠️ IMPORTANT: These are identical copies between folders
All these tmp_rovodev files appear in both `n8n docs/` and `suggestions/` folders. **These are important working documents** containing:
- AI model research and analysis
- Professional document templates  
- Workflow architecture designs
- Quality control systems
- Manifesto integration strategies

**Recommendation: Keep one complete set (suggest keeping in `suggestions/` folder) and remove duplicates from `n8n docs/`**

Files (identical in both locations):
- `tmp_rovodev_quality_control_conversation_system.md` (Quality control workflow design)
- `tmp_rovodev_professional_docx_templates.md` (Professional document formatting templates)
- `tmp_rovodev_prompt_file_format_standard.md` (Standardized instruction templates)
- `tmp_rovodev_manifesto_integration_strategy.md` (Manifesto integration approach)
- `tmp_rovodev_folder_structure_design.md` (Google Drive organization strategy)
- `tmp_rovodev_ai_model_research_analysis.md` (AI model selection and performance analysis)
- `tmp_rovodev_manifesto_structure_template.md` (Manifesto document structure)
- `tmp_rovodev_workflow_architecture_design.md` (Workflow system architecture)
- `tmp_rovodev_workflow_prompt_strategy.md` (Prompt engineering strategy)
- `tmp_rovodev_docker_compose_n8n.yml` (Docker configuration)
- `tmp_rovodev_n8n_setup_guide.md` (n8n setup instructions)

### Related RovoDev documents:
- `suggestions/rovodev_n8n_master_design.md`
- `suggestions/rovodev-master-n8n-prompt-final.md`

---

## 8. Complete Workflow Build Prompts

### Latest: `complete-rovodev-n8n-workflow-build-prompt.md` (Root - 28KB)
Duplicate:
- `suggestions/complete-rovodev-n8n-workflow-build-prompt.md` (Same file)

---

## 9. Agent Specifications

### Latest: `n8n_agent_specifications.md`
Related agent documents:
- `suggestions/n8n_implementation_guide_complete.md` (Comprehensive implementation guide)
- `suggestions/.agent.md`
- All files in `sub-agents/` folder (8 specialized agents)

---

## 10. White Papers and Political Documents

### Organized Structure:
- `white_papers_markdown/` (Main folder with subfolders by topic)
  - Duplicates/Old versions:
    - `white_papers_markdown/white_papers_markdown/` (Nested duplicate)
    - `white_papers_markdown/white_papers_markdown1/` (Version 1)
    - `white_papers_markdown/markdown files/`

### Master Summary Documents:
Multiple copies exist:
- `white_papers_markdown/Master_Summary_Political_Organization_Beau_Lewis.md`
- `suggestions/Master_Summary_Political_Organization_Beau_Lewis.md`
- `n8n docs/Master_Summary_Political_Organization_Beau_Lewis.md`

### Lists of White Paper Areas:
- `white_papers_markdown/list of areas for white papers.md`
- `n8n docs/list of areas for white papers.md`
- `white_papers_markdown/Policy Areas for White Papers.md`

---

## 11. Category Supplements

### Latest location: `manifesto/category-supplements/`
Duplicates in `suggestions/`:
- `category_supplement_funding_revenue.md`
- `category_supplement_rights_repair_grow.md`
- `category_supplement_ethics_accountability.md`
- `category_supplement_constitutional_amendments.md`
- `category_supplement_jobs_automation.md`
- `category_supplement_housing.md`
- `category_supplement_economic_policy.md`
- `category_supplement_education.md`
- `category_supplement_healthcare.md`

---

## 12. Core Documents (Duplicated)

These appear in multiple locations:
- `core_essence.md` (in `manifesto/` and `suggestions/`)
- `style_guide.md` (in `manifesto/` and `suggestions/`)
- `voice_guidelines_beau_lewis.md` (in `manifesto/` and `suggestions/`)
- `manifesto_for_agents.md` (in `manifesto/` and `suggestions/`)

---

## 13. Miscellaneous Document Groups

### Testing and Error Handling:
- `testing_error_handling_strategy.md` (Latest comprehensive strategy)

### Data Flow:
- `data_flow_specification.md`

### MCP Tool Workflows:
- `mcp_tool_workflows_design.md`

### n8n Best Practices:
- `n8n_best_practices.md`

### Infrastructure:
- `INFRASTRUCTURE_SETUP.md`
- `MCP_SERVERS_SETUP.md`

### Validation Reports:
- `n8n-mcp-validation-report.md`
- `kiro-n8n-report.md`

### Docker Compose Files:
- `docker-compose.yml` (Main)
- `tmp_rovodev_docker_compose_n8n.yml` (In both n8n docs and suggestions)

---

## 14. American Social Trust Fund (ASTF) Documents

### Latest: `n8n docs/ASTF FIXED WITH AI.md`
Previous/Related versions:
- `n8n docs/Implementing the American Social Trust Fund (ASTF) A Strategic Blueprint.md`
- `n8n docs/Anticipating and Countering Attacks on the American Social Trust Fund (ASTF).md`
- `n8n docs/American Social Trust Fund mission statement.md`
- `n8n docs/Amendment 10_ Establishment of the Social Trust Fund.md`

---

## 15. Configuration Files

### n8n Configuration:
- `.claude.json` (11KB, 366 lines)
- `.mcp.json` (9.4KB, 342 lines)
- `.cursor/mcp.json` (3.5KB, 80 lines)
- `.claude/settings.local.json` (894B, 48 lines)

### Clinerules Files:
- `.clinerules-test`
- `.clinerules-debug`
- `.clinerules-code`
- `.clinerules-ask`
- `.clinerules-architect`

---

## 16. Converter/Tools Documentation

### Document Converter:
- `convertor/README.md` (Main documentation)
- `convertor/README_Document_Converter.md` (Specific converter docs)
- `convertor/PROJECT_SUMMARY.md`

---

## 17. Additional Findings

### Random Markdowns:
- `randon-markdowns/07-29-25-endofchat-n8n.md` (Chat transcript)

### Amendment Documents (in n8n docs):
- `Amendment 1_ Ending Presidential Immunity.md`
- `Amendment 8_ AI Political Integrity & Bad Faith Accountability.md`
- `Amendment 10_ Establishment of the Social Trust Fund.md`

### White Papers in n8n docs:
- `White Paper_ Universal Higher Education for America.md`
- `Strengthening American Democracy Through Public Education Reform.md`
- `The Right to Repair_ Securing Consumer Rights and Environmental Protection.md`
- `The Right to Grow and Right to Seed_ Securing Agricultural Freedom and Food Sovereignty.md`

### 2028 Democratic Platform:
- `n8n docs/2028 democratic 2.md`

---

## Recommendations:

1. **Clear duplicates to delete:**
   - All tmp_rovodev files in `n8n docs/` (keep suggestions/ versions) - **These are identical copies of important working documents**
   - `project_status.d` (old stub)
   - Root `n8n_workflow_setup.md` (75B stub)
   - Nested duplicate folders in white_papers_markdown

2. **Consider consolidating:**
   - Category supplements (keep only manifesto/category-supplements/)
   - Core documents (keep only manifesto/ versions)
   - Master summary documents (keep one authoritative version)

3. **Archive old versions:**
   - Create an `archive/` folder for historical versions
   - Move older manifesto versions (v2, v3, original)
   - Move older handoff documents (Phases 1-3)
   - Move older workflow designs

4. **Important to keep:**
   - All latest versions marked above
   - All MCP server documentation
   - All sub-agent specifications
   - Phase completion summaries (historical record)
   - **All tmp_rovodev files** (important working documents - consolidate to one location)

5. **Summary of duplicate patterns found:**
   - **tmp_rovodev files**: 11 **important working documents** duplicated between `n8n docs/` and `suggestions/` (identical copies)
   - **Core manifesto files**: 4 files duplicated between `manifesto/` and `suggestions/`
   - **Category supplements**: 9 files duplicated between folders
   - **Master summary**: 3 identical copies in different locations
   - **White paper folders**: 3 nested/duplicate folder structures

---

*Note: File sizes and line counts are included where significantly different between versions. Always verify content before deletion.*

*Document created on: January 29, 2025* 