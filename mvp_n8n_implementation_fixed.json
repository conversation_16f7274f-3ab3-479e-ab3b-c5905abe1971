{"name": "Manifesto-Aligned Document Processor MVP", "version": 1, "connections": {"Document Input": {"main": [[{"node": "Load Manifesto Context", "type": "main", "index": 0}]]}, "Load Manifesto Context": {"main": [[{"node": "Extract Document Content", "type": "main", "index": 0}]]}, "Extract Document Content": {"main": [[{"node": "Lead Coordinator Agent", "type": "main", "index": 0}]]}, "Lead Coordinator Agent": {"main": [[{"node": "Routing Decision", "type": "main", "index": 0}]]}, "Routing Decision": {"main": [[{"node": "Policy Analysis Agent", "type": "main", "index": 0}], [{"node": "Editorial Enhancement Agent", "type": "main", "index": 0}], [{"node": "Research Agent", "type": "main", "index": 0}], [{"node": "Human Review Queue", "type": "main", "index": 0}]]}, "Policy Analysis Agent": {"main": [[{"node": "Quality Control Validator", "type": "main", "index": 0}]]}, "Editorial Enhancement Agent": {"main": [[{"node": "Quality Control Validator", "type": "main", "index": 0}]]}, "Research Agent": {"main": [[{"node": "Quality Control Validator", "type": "main", "index": 0}]]}, "Quality Control Validator": {"main": [[{"node": "Output Formatter", "type": "main", "index": 0}]]}, "Output Formatter": {"main": [[{"node": "Distribution", "type": "main", "index": 0}]]}}, "nodes": [{"name": "Document Input", "type": "@n8n/n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "webhookId": "manifesto-doc-processor", "parameters": {"path": "process-document", "httpMethod": "POST", "responseMode": "responseNode", "responseData": "allEntries"}}, {"name": "Load Manifesto Context", "type": "@n8n/n8n-nodes-base.code", "typeVersion": 1, "position": [400, 300], "parameters": {"language": "javascript", "jsCode": "const manifestoPrinciples = { \"new_american_patriotism\": \"Love of fellow citizens, not blind loyalty to power.\", \"transformative_over_incremental\": \"Address root causes, constitutional solutions.\", \"people_over_profit\": \"Working families first. Universal access.\", \"unity_over_division\": \"Build bridges while confronting unjust systems.\", \"radical_transparency\": \"Complete accountability. No immunity.\", \"universal_rights\": \"Healthcare, education, housing as human rights.\"}; const formattedContext = 'MANIFESTO PRINCIPLES FOR AI PROCESSING: New American Patriotism - Love of fellow citizens, government as good as the people'; return [{ json: { ...item.json, manifesto_context: formattedContext, manifesto_principles: manifestoPrinciples, processing_timestamp: new Date().toISOString() } }];"}}, {"name": "Extract Document Content", "type": "@n8n/n8n-nodes-base.code", "typeVersion": 1, "position": [600, 300], "parameters": {"language": "javascript", "jsCode": "let extractedContent = ''; let documentType = 'unknown'; if (item.json.body && item.json.body.content) { extractedContent = item.json.body.content; documentType = item.json.body.type || 'text'; } else if (item.json.body && item.json.body.text) { extractedContent = item.json.body.text; documentType = 'text'; } else if (item.json.body) { extractedContent = JSON.stringify(item.json.body); documentType = 'json'; } else { extractedContent = JSON.stringify(item.json); documentType = 'fallback'; } const wordCount = extractedContent.split(/\\s+/).length; return [{ json: { ...item.json, document_content: extractedContent, document_type: documentType, content_length: extractedContent.length, word_count: wordCount, extraction_timestamp: new Date().toISOString() } }];"}}, {"name": "Lead Coordinator Agent", "type": "@n8n/n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [800, 300], "parameters": {"url": "https://api.anthropic.com/v1/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "anthropicApi", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "{\"model\": \"claude-3-sonnet-********\",\"max_tokens\": 1000,\"messages\": [{\"role\": \"user\",\"content\": \"SYSTEM: Lead Coordinator for New American Patriotism document processing. MANIFESTO: {{ $json.manifesto_context }} TASK: Analyze document and determine processing strategy. Document: {{ $json.document_content }} OUTPUT JSON: {\\\"alignment_score\\\": 0.0-1.0, \\\"recommended_action\\\": \\\"analyze|enhance|critique\\\", \\\"reasoning\\\": \\\"explanation\\\"}\"}]}"}}, {"name": "Routing Decision", "type": "@n8n/n8n-nodes-base.switch", "typeVersion": 1, "position": [1000, 300], "parameters": {"rules": {"rules": [{"operation": "equal", "value1": "={{ JSON.parse($json.content[0].text).recommended_action }}", "value2": "analyze"}, {"operation": "equal", "value1": "={{ JSON.parse($json.content[0].text).recommended_action }}", "value2": "enhance"}, {"operation": "equal", "value1": "={{ JSON.parse($json.content[0].text).recommended_action }}", "value2": "critique"}]}, "fallbackOutput": 3}}, {"name": "Policy Analysis Agent", "type": "@n8n/n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1200, 100], "parameters": {"url": "https://api.anthropic.com/v1/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "anthropicApi", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "{\"model\": \"claude-3-sonnet-********\",\"max_tokens\": 2000,\"messages\": [{\"role\": \"user\",\"content\": \"SPECIALIZATION: Constitutional Policy Analysis MANIFESTO: {{ $('Load Manifesto Context').item.json.manifesto_context }} MISSION: Analyze for constitutional reform opportunities that advance New American Patriotism. FOCUS: Amendment opportunities, universal programs, anti-corruption, economic justice, individual sovereignty. Document: {{ $('Extract Document Content').item.json.document_content }} Provide detailed policy analysis with constitutional amendment language.\"}]}"}}, {"name": "Editorial Enhancement Agent", "type": "@n8n/n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1200, 300], "parameters": {"url": "https://api.anthropic.com/v1/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "anthropicApi", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "{\"model\": \"claude-3-sonnet-********\",\"max_tokens\": 2000,\"messages\": [{\"role\": \"user\",\"content\": \"SPECIALIZATION: Editorial Enhancement for New American Patriotism MANIFESTO: {{ $('Load Manifesto Context').item.json.manifesto_context }} MISSION: Enhance document language to advance manifesto principles. CRITERIA: Passionate tone, accessible language, hopeful vision, systemic solutions, unity-building. Document: {{ $('Extract Document Content').item.json.document_content }} Provide enhanced version advancing New American Patriotism.\"}]}"}}, {"name": "Research Agent", "type": "@n8n/n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1200, 500], "parameters": {"url": "https://api.anthropic.com/v1/messages", "authentication": "predefinedCredentialType", "nodeCredentialType": "anthropicApi", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "anthropic-version", "value": "2023-06-01"}]}, "sendBody": true, "bodyContentType": "json", "jsonBody": "{\"model\": \"claude-3-sonnet-********\",\"max_tokens\": 2000,\"messages\": [{\"role\": \"user\",\"content\": \"SPECIALIZATION: Research & Critical Analysis MANIFESTO: {{ $('Load Manifesto Context').item.json.manifesto_context }} MISSION: Provide research-backed recommendations aligned with manifesto principles. PRIORITIES: Constitutional precedents, universal program success, corporate power evidence, working family impact, feasibility analysis. Document: {{ $('Extract Document Content').item.json.document_content }} Provide research findings supporting manifesto-aligned conclusions.\"}]}"}}, {"name": "Human Review Queue", "type": "@n8n/n8n-nodes-base.code", "typeVersion": 1, "position": [1200, 700], "parameters": {"language": "javascript", "jsCode": "const reviewInfo = { document_id: item.json.processing_timestamp, review_reason: 'AI coordinator unable to determine optimal processing path', status: 'pending_review' }; console.log('Document queued for human review:', reviewInfo); return [{ json: { ...item.json, review_info: reviewInfo, processing_status: 'human_review_required', ai_output: 'Document requires human review before processing can continue.' } }];"}}, {"name": "Quality Control Validator", "type": "@n8n/n8n-nodes-base.code", "typeVersion": 1, "position": [1400, 300], "parameters": {"language": "javascript", "jsCode": "function validateAgainstManifesto(content) { const text = content.toLowerCase(); const transformativeScore = (text.includes('constitutional') ? 0.3 : 0) + (text.includes('amendment') ? 0.2 : 0) + (text.includes('systemic') ? 0.2 : 0) + (text.includes('universal') ? 0.3 : 0); const peopleFirstScore = (text.includes('working families') ? 0.3 : 0) + (text.includes('healthcare') || text.includes('education') || text.includes('housing') ? 0.3 : 0); const averageScore = (transformativeScore + peopleFirstScore) / 2; return { transformative_score: Math.min(transformativeScore, 1.0), people_first_score: Math.min(peopleFirstScore, 1.0), average_score: averageScore }; } let aiContent = ''; if (item.json.content && item.json.content[0] && item.json.content[0].text) { aiContent = item.json.content[0].text; } else if (item.json.ai_output) { aiContent = item.json.ai_output; } else { aiContent = JSON.stringify(item.json); } const validationResults = validateAgainstManifesto(aiContent); const overallGrade = validationResults.average_score >= 0.8 ? 'A' : validationResults.average_score >= 0.6 ? 'B' : validationResults.average_score >= 0.4 ? 'C' : 'D'; const passesQC = validationResults.average_score >= 0.5; return [{ json: { ...item.json, validation_results: validationResults, overall_manifesto_grade: overallGrade, passes_quality_control: passesQC, validated_content: aiContent, validation_timestamp: new Date().toISOString() } }];"}}, {"name": "Output Formatter", "type": "@n8n/n8n-nodes-base.code", "typeVersion": 1, "position": [1600, 300], "parameters": {"language": "javascript", "jsCode": "const manifestoSignature = '\\n\\n---\\n**Processed through Manifesto-Aligned AI System**\\n*Advancing New American Patriotism: Government as good as the American people*'; function generateTitle(content) { const text = content.toLowerCase(); if (text.includes('healthcare')) return 'Healthcare as a Human Right: Manifesto Analysis'; if (text.includes('education')) return 'Universal Education: Building Prosperity'; if (text.includes('housing')) return 'Right to Housing: Constitutional Framework'; return 'Policy Analysis: Advancing New American Patriotism'; } const formattedOutput = { title: generateTitle(item.json.validated_content), content: item.json.validated_content + manifestoSignature, metadata: { processed_date: new Date().toISOString(), manifesto_alignment_grade: item.json.overall_manifesto_grade, passes_quality_control: item.json.passes_quality_control } }; return [{ json: { ...item.json, formatted_output: formattedOutput, final_content: formattedOutput.content, processing_complete: true } }];"}}, {"name": "Distribution", "type": "@n8n/n8n-nodes-base.code", "typeVersion": 1, "position": [1800, 300], "parameters": {"language": "javascript", "jsCode": "const output = { success: true, document_id: item.json.processing_timestamp, title: item.json.formatted_output.title, content: item.json.formatted_output.content, manifesto_grade: item.json.overall_manifesto_grade, quality_control_passed: item.json.passes_quality_control, metadata: item.json.formatted_output.metadata }; console.log('Document processing complete:', { title: output.title, grade: output.manifesto_grade, passed_qc: output.quality_control_passed }); return [{ json: output }];"}}]}