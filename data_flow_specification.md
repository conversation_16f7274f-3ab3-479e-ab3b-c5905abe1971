# Data Flow Specification: Main Workflow ↔ MCP Tools

## Overview

This document defines the complete data flow architecture between the main n8n workflow and specialized MCP (Model Context Protocol) tools, ensuring seamless integration while maintaining manifesto principle alignment throughout the entire processing pipeline.

## Data Flow Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                      MAIN WORKFLOW                             │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Document Input → Context Loading → Lead Coordinator           │
│       ↓                ↓               ↓                       │
│  [Manifesto Context] [Document Data] [Routing Decision]        │
│                                                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    MCP TOOL LAYER                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │Constitutional│  │  Research   │  │ Editorial   │             │
│  │  Analyzer   │  │ Validator   │  │ Enhancer    │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
│                                                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                 QUALITY CONTROL & OUTPUT                       │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Validation → Formatting → Distribution                        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Core Data Structures

### 1. Manifesto Context Object
```json
{
  "manifesto_context": {
    "core_principles": {
      "new_american_patriotism": {
        "definition": "Love of fellow citizens, not blind loyalty to power",
        "weight": 0.25,
        "keywords": ["fellow citizens", "community", "service"],
        "anti_patterns": ["blind loyalty", "nationalism", "corporate power"]
      },
      "transformative_over_incremental": {
        "definition": "Address root causes through constitutional solutions",
        "weight": 0.25,
        "keywords": ["constitutional", "amendment", "systemic", "universal"],
        "anti_patterns": ["incremental", "reform", "compromise", "moderate"]
      },
      "people_over_profit": {
        "definition": "Working families first, universal access over markets",
        "weight": 0.25,
        "keywords": ["working families", "universal", "public good"],
        "anti_patterns": ["market-based", "private", "profit", "means-tested"]
      },
      "unity_over_division": {
        "definition": "Build bridges while confronting unjust systems",
        "weight": 0.25,
        "keywords": ["unity", "shared struggles", "together"],
        "anti_patterns": ["partisan", "divisive", "identity politics"]
      }
    },
    "constitutional_framework": [
      "End corporate money in politics",
      "Universal public financing of elections",
      "End presidential and congressional immunity",
      "Ban stock trading by officials",
      "Prohibit nepotism",
      "Term limits for all federal positions",
      "Truth and accountability amendment"
    ],
    "universal_programs": [
      "Medicare for All",
      "Tuition-free higher education",
      "American Social Trust Fund",
      "National housing initiative",
      "Right to repair, grow, build"
    ],
    "validation_thresholds": {
      "minimum_alignment_score": 0.6,
      "constitutional_preference_score": 0.7,
      "working_family_impact_score": 0.5
    }
  }
}
```

### 2. Document Processing State
```json
{
  "document_state": {
    "input": {
      "content": "Original document text",
      "type": "policy|speech|research|legislation",
      "metadata": {
        "source": "document source",
        "author": "document author",
        "date": "2024-01-15T10:30:00Z",
        "tags": ["healthcare", "constitutional", "policy"]
      }
    },
    "processing": {
      "timestamp": "2024-01-15T10:30:00Z",
      "session_id": "unique-session-identifier",
      "stage": "input|analysis|routing|processing|validation|output",
      "current_agent": "lead_coordinator|constitutional_analyzer|research_validator|editorial_enhancer",
      "manifesto_context_loaded": true,
      "routing_decision": {
        "recommended_action": "analyze|enhance|critique|review",
        "confidence": 0.85,
        "reasoning": "Document shows incremental approach requiring constitutional transformation"
      }
    },
    "analysis": {
      "content_metrics": {
        "word_count": 1250,
        "reading_level": 12,
        "sentiment": "neutral",
        "topics": ["healthcare", "reform", "policy"]
      },
      "manifesto_alignment": {
        "initial_score": 0.45,
        "principle_scores": {
          "new_american_patriotism": 0.3,
          "transformative_solutions": 0.2,
          "people_over_profit": 0.6,
          "unity_building": 0.7
        },
        "improvement_areas": ["constitutional_framework", "universal_access"]
      }
    }
  }
}
```

### 3. MCP Tool Request Format
```json
{
  "mcp_request": {
    "tool_name": "constitutional_policy_analyzer",
    "request_id": "req_20240115_103000_001",
    "session_id": "sess_20240115_103000",
    "input": {
      "document_content": "Full document text for analysis",
      "manifesto_context": "Loaded manifesto principles and frameworks",
      "processing_context": {
        "coordinator_analysis": {
          "alignment_score": 0.45,
          "recommended_action": "analyze",
          "reasoning": "Requires constitutional transformation"
        },
        "routing_metadata": {
          "primary_themes": ["healthcare", "reform"],
          "manifesto_connections": ["universal_rights", "transformative_solutions"],
          "processing_priority": "high"
        }
      },
      "output_requirements": {
        "format": "detailed_analysis|brief_summary|enhanced_content",
        "target_audience": "policy_makers|working_families|activists",
        "manifesto_emphasis": ["constitutional_framework", "universal_programs"],
        "length_target": "800-1200 words"
      }
    },
    "expected_response_schema": {
      "type": "object",
      "properties": {
        "tool_output": {"type": "string"},
        "manifesto_alignment_scores": {"type": "object"},
        "constitutional_recommendations": {"type": "array"},
        "evidence_citations": {"type": "array"},
        "tool_metadata": {"type": "object"}
      }
    }
  }
}
```

### 4. MCP Tool Response Format
```json
{
  "mcp_response": {
    "request_id": "req_20240115_103000_001",
    "tool_name": "constitutional_policy_analyzer",
    "status": "success|error|partial",
    "processing_time_ms": 3500,
    "output": {
      "tool_output": "Enhanced/analyzed content with constitutional recommendations",
      "manifesto_alignment_scores": {
        "new_american_patriotism": 0.88,
        "transformative_solutions": 0.92,
        "people_over_profit": 0.85,
        "unity_building": 0.79,
        "overall_score": 0.86
      },
      "constitutional_recommendations": [
        {
          "amendment_number": "Amendment XXVIII",
          "title": "Healthcare as a Human Right",
          "text": "The right of all citizens to healthcare...",
          "implementation_strategy": "Phase-in over 4 years with automatic enrollment",
          "political_feasibility": 0.7
        }
      ],
      "evidence_citations": [
        {
          "source": "Congressional Budget Office Healthcare Analysis 2023",
          "relevance": "Universal system cost savings",
          "manifesto_support": "people_over_profit"
        }
      ],
      "tool_metadata": {
        "confidence_level": 0.91,
        "processing_agent": "constitutional_policy_analyzer",
        "manifesto_principles_applied": ["transformative_solutions", "universal_rights"],
        "improvement_suggestions": ["Add enforcement mechanisms", "Strengthen worker protections"],
        "follow_up_tools": ["research_validator", "legislative_strategy_advisor"]
      }
    },
    "errors": [],
    "warnings": [
      "Original document alignment below threshold - significant enhancement applied"
    ]
  }
}
```

## Messaging Protocols

### 1. Request-Response Protocol
```
Main Workflow → MCP Tool:
1. Validate request format against schema
2. Include session context and manifesto principles
3. Set timeout (30 seconds for analysis, 60 seconds for enhancement)
4. Include retry logic with exponential backoff

MCP Tool → Main Workflow:
1. Validate response completeness
2. Ensure manifesto alignment scores are present
3. Include processing metadata for logging
4. Flag any errors or warnings for human review
```

### 2. Error Handling Protocol
```json
{
  "error_response": {
    "request_id": "req_20240115_103000_001",
    "tool_name": "constitutional_policy_analyzer",
    "status": "error",
    "error": {
      "code": "MANIFESTO_ALIGNMENT_FAILED",
      "message": "Unable to align document with manifesto principles",
      "details": "Document content contradicts core principle: people_over_profit",
      "recovery_action": "route_to_human_review",
      "fallback_tool": "editorial_enhancer"
    },
    "partial_output": "Any analysis completed before error",
    "session_context": "Preserved for human review"
  }
}
```

### 3. State Synchronization Protocol
```json
{
  "state_sync": {
    "session_id": "sess_20240115_103000",
    "current_stage": "mcp_tool_processing",
    "active_tools": ["constitutional_policy_analyzer"],
    "completed_tools": [],
    "manifesto_context": "Shared manifesto principles state",
    "document_state": "Current document processing state",
    "next_stage": "quality_control_validation",
    "rollback_point": "lead_coordinator_analysis"
  }
}
```

## Data Formats and Standards

### 1. Content Encoding
- **Text Content**: UTF-8 encoding
- **Document Metadata**: ISO 8601 timestamps, standardized tags
- **Manifesto Scores**: Float values 0.0-1.0 with 2 decimal precision
- **Constitutional Text**: Formal amendment language with section numbering

### 2. API Standards
- **Request Format**: JSON with required fields validation
- **Response Format**: Consistent schema across all tools
- **Error Codes**: Standardized error taxonomy
- **Versioning**: Semantic versioning for protocol compatibility

### 3. Validation Rules
```javascript
// Manifesto alignment validation
function validateManifestoAlignment(scores) {
  const required_principles = [
    'new_american_patriotism',
    'transformative_solutions', 
    'people_over_profit',
    'unity_building'
  ];
  
  // All principles must be present
  const missing_principles = required_principles.filter(
    principle => !(principle in scores)
  );
  
  if (missing_principles.length > 0) {
    throw new Error(`Missing manifesto principles: ${missing_principles.join(', ')}`);
  }
  
  // Overall score must meet threshold
  const overall_score = Object.values(scores).reduce((a, b) => a + b, 0) / Object.keys(scores).length;
  
  if (overall_score < 0.6) {
    throw new Error(`Manifesto alignment below threshold: ${overall_score}`);
  }
  
  return true;
}

// Constitutional recommendation validation
function validateConstitutionalRecommendation(amendment) {
  const required_fields = ['amendment_number', 'title', 'text', 'implementation_strategy'];
  
  const missing_fields = required_fields.filter(
    field => !(field in amendment) || !amendment[field]
  );
  
  if (missing_fields.length > 0) {
    throw new Error(`Missing amendment fields: ${missing_fields.join(', ')}`);
  }
  
  // Amendment text must follow constitutional format
  if (!amendment.text.startsWith('Section 1.')) {
    throw new Error('Amendment text must follow constitutional section format');
  }
  
  return true;
}
```

## State Management

### 1. Session State Management
```json
{
  "session_state": {
    "session_id": "sess_20240115_103000",
    "created_at": "2024-01-15T10:30:00Z",
    "last_updated": "2024-01-15T10:33:45Z",
    "status": "active|completed|error|timeout",
    "progress": {
      "total_stages": 7,
      "completed_stages": 4,
      "current_stage": "mcp_tool_processing",
      "estimated_completion": "2024-01-15T10:35:00Z"
    },
    "context": {
      "manifesto_principles": "Loaded and validated",
      "document_metadata": "Extracted and normalized",
      "routing_decision": "Constitutional analysis required",
      "active_tools": ["constitutional_policy_analyzer"],
      "tool_queue": ["research_validator", "editorial_enhancer"]
    },
    "rollback_points": [
      {
        "stage": "lead_coordinator_analysis",
        "timestamp": "2024-01-15T10:31:30Z",
        "state_snapshot": "Complete state at routing decision"
      }
    ]
  }
}
```

### 2. Tool State Coordination
```javascript
class ToolStateManager {
  constructor(sessionId) {
    this.sessionId = sessionId;
    this.activeTools = new Map();
    this.completedTools = new Map();
    this.sharedContext = new Map();
  }
  
  async executeToolChain(tools, document, manifestoContext) {
    for (const tool of tools) {
      try {
        // Set tool state to active
        this.activeTools.set(tool.name, {
          startTime: Date.now(),
          status: 'processing',
          input: { document, manifestoContext, ...this.sharedContext }
        });
        
        // Execute tool with timeout
        const result = await this.executeWithTimeout(tool, 30000);
        
        // Validate result against manifesto principles
        this.validateResult(result);
        
        // Update shared context with results
        this.sharedContext.set(`${tool.name}_output`, result.output);
        this.sharedContext.set(`${tool.name}_scores`, result.manifesto_alignment_scores);
        
        // Mark tool as completed
        this.completedTools.set(tool.name, {
          endTime: Date.now(),
          status: 'completed',
          output: result
        });
        
        this.activeTools.delete(tool.name);
        
      } catch (error) {
        this.handleToolError(tool, error);
      }
    }
    
    return this.generateFinalOutput();
  }
  
  async executeWithTimeout(tool, timeoutMs) {
    return Promise.race([
      tool.execute(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Tool timeout')), timeoutMs)
      )
    ]);
  }
  
  validateResult(result) {
    // Ensure manifesto alignment scores are present and valid
    if (!result.manifesto_alignment_scores) {
      throw new Error('Missing manifesto alignment scores');
    }
    
    const overallScore = Object.values(result.manifesto_alignment_scores)
      .reduce((a, b) => a + b, 0) / Object.keys(result.manifesto_alignment_scores).length;
    
    if (overallScore < 0.6) {
      throw new Error(`Manifesto alignment below threshold: ${overallScore}`);
    }
  }
  
  handleToolError(tool, error) {
    console.error(`Tool ${tool.name} failed:`, error);
    
    // Route to human review with error context
    this.sharedContext.set('error_context', {
      failed_tool: tool.name,
      error_message: error.message,
      requires_human_review: true
    });
    
    // Try fallback tool if available
    if (tool.fallback) {
      console.log(`Attempting fallback tool: ${tool.fallback}`);
      return this.executeToolChain([{ name: tool.fallback }]);
    }
  }
}
```

### 3. Data Persistence Strategy
```javascript
// Session data persistence
const sessionStore = {
  // Store session state for recovery
  async saveSession(sessionId, state) {
    await redis.setex(`session:${sessionId}`, 3600, JSON.stringify(state));
  },
  
  // Retrieve session for continuation
  async loadSession(sessionId) {
    const state = await redis.get(`session:${sessionId}`);
    return state ? JSON.parse(state) : null;
  },
  
  // Store tool outputs for reuse
  async cacheToolOutput(sessionId, toolName, output) {
    const key = `tool_cache:${sessionId}:${toolName}`;
    await redis.setex(key, 1800, JSON.stringify(output)); // 30 min cache
  }
};

// Document processing history
const documentStore = {
  async saveProcessedDocument(documentId, finalOutput) {
    const record = {
      document_id: documentId,
      processed_at: new Date().toISOString(),
      manifesto_grade: finalOutput.manifesto_grade,
      alignment_scores: finalOutput.alignment_scores,
      constitutional_recommendations: finalOutput.constitutional_recommendations,
      final_content: finalOutput.content,
      processing_metadata: finalOutput.metadata
    };
    
    await database.documents.insert(record);
  },
  
  async getProcessingHistory(filters) {
    return await database.documents.find(filters);
  }
};
```

## Integration Points

### 1. Main Workflow Integration
```javascript
// In n8n Lead Coordinator node
const mcpToolRequest = {
  tool_name: 'constitutional_policy_analyzer',
  input: {
    document_content: $('Extract Document Content').item.json.document_content,
    manifesto_context: $('Load Manifesto Context').item.json.manifesto_context,
    processing_context: {
      coordinator_analysis: JSON.parse($json.content[0].text),
      routing_metadata: $json.routing_metadata
    }
  }
};

// Send to MCP tool via HTTP request
const mcpResponse = await fetch('https://mcp-server.com/tools/constitutional_analyzer', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(mcpToolRequest)
});

const result = await mcpResponse.json();

// Validate response and continue workflow
if (result.status === 'success') {
  return [{
    json: {
      ...item.json,
      mcp_tool_output: result.output.tool_output,
      manifesto_alignment_scores: result.output.manifesto_alignment_scores,
      constitutional_recommendations: result.output.constitutional_recommendations,
      tool_metadata: result.output.tool_metadata
    }
  }];
} else {
  // Route to human review
  return [{
    json: {
      ...item.json,
      requires_human_review: true,
      error_context: result.error
    }
  }];
}
```

### 2. Quality Control Integration
```javascript
// In Quality Control Validator node
function validateMCPToolOutput(toolOutput, manifestoScores) {
  // Validate all required manifesto scores are present
  const requiredPrinciples = [
    'new_american_patriotism',
    'transformative_solutions',
    'people_over_profit', 
    'unity_building'
  ];
  
  const missingPrinciples = requiredPrinciples.filter(
    principle => !(principle in manifestoScores)
  );
  
  if (missingPrinciples.length > 0) {
    throw new Error(`MCP tool missing manifesto scores: ${missingPrinciples.join(', ')}`);
  }
  
  // Calculate overall alignment
  const overallScore = Object.values(manifestoScores)
    .reduce((sum, score) => sum + score, 0) / Object.keys(manifestoScores).length;
  
  // Determine if output passes quality control
  const passesQC = overallScore >= 0.6;
  
  return {
    passes_quality_control: passesQC,
    overall_manifesto_alignment: overallScore,
    manifesto_scores: manifestoScores,
    validated_content: toolOutput,
    validation_timestamp: new Date().toISOString()
  };
}
```

## Performance and Monitoring

### 1. Performance Metrics
```javascript
const metrics = {
  // Tool response times
  tool_performance: {
    constitutional_analyzer: { avg_response_ms: 3500, success_rate: 0.98 },
    research_validator: { avg_response_ms: 5200, success_rate: 0.95 },
    editorial_enhancer: { avg_response_ms: 4100, success_rate: 0.97 }
  },
  
  // Manifesto alignment trends
  alignment_metrics: {
    average_input_score: 0.52,
    average_output_score: 0.84,
    improvement_factor: 1.62,
    constitutional_recommendations_per_doc: 2.3
  },
  
  // Error rates and recovery
  error_metrics: {
    tool_failure_rate: 0.03,
    successful_recovery_rate: 0.89,
    human_review_required_rate: 0.08
  }
};
```

### 2. Monitoring and Alerting
```javascript
// Monitor manifesto alignment degradation
function monitorManifestoAlignment(scores) {
  const overallScore = Object.values(scores).reduce((a, b) => a + b, 0) / Object.keys(scores).length;
  
  if (overallScore < 0.5) {
    alerting.send({
      severity: 'high',
      message: `Manifesto alignment critically low: ${overallScore}`,
      action_required: 'Review tool prompts and validation logic'
    });
  }
}

// Monitor tool performance degradation
function monitorToolPerformance(toolName, responseTime, success) {
  const threshold = toolPerformanceThresholds[toolName];
  
  if (responseTime > threshold.max_response_ms) {
    alerting.send({
      severity: 'medium',
      message: `Tool ${toolName} response time degraded: ${responseTime}ms`,
      action_required: 'Check tool server health'
    });
  }
  
  if (!success) {
    const recentFailures = getRecentFailures(toolName, '5m');
    if (recentFailures.length > 3) {
      alerting.send({
        severity: 'high',
        message: `Tool ${toolName} experiencing repeated failures`,
        action_required: 'Investigate and potentially disable tool'
      });
    }
  }
}
```

This data flow specification ensures robust, manifesto-aligned communication between all system components while maintaining high performance and reliability. 