{
  "name": "Manifesto-Aligned Document Processor MVP",
  "version": 1,
  "connections": {
    "Document Input": {
      "main": [
        [
          {
            "node": "Load Manifesto Context",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Load Manifesto Context": {
      "main": [
        [
          {
            "node": "Extract Document Content",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Document Content": {
      "main": [
        [
          {
            "node": "Lead Coordinator Agent",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Lead Coordinator Agent": {
      "main": [
        [
          {
            "node": "Routing Decision",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Routing Decision": {
      "main": [
        [
          {
            "node": "Policy Analysis Agent",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Editorial Enhancement Agent",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Research Agent",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Human Review Queue",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Policy Analysis Agent": {
      "main": [
        [
          {
            "node": "Quality Control Validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Editorial Enhancement Agent": {
      "main": [
        [
          {
            "node": "Quality Control Validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Research Agent": {
      "main": [
        [
          {
            "node": "Quality Control Validator",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Quality Control Validator": {
      "main": [
        [
          {
            "node": "Output Formatter",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Output Formatter": {
      "main": [
        [
          {
            "node": "Distribution",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "nodes": [
    {
      "name": "Document Input",
      "type": "@n8n/n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [200, 300],
      "webhookId": "manifesto-doc-processor",
      "parameters": {
        "path": "process-document",
        "httpMethod": "POST",
        "responseMode": "responseNode",
        "responseData": "allEntries"
      }
    },
    {
      "name": "Load Manifesto Context",
      "type": "@n8n/n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [400, 300],
      "parameters": {
        "language": "javascript",
        "jsCode": "// Load and format manifesto principles for AI context\nconst manifestoPrinciples = {\n  \"new_american_patriotism\": \"Love of fellow citizens, not blind loyalty to power. Government as good and decent as its people.\",\n  \"transformative_over_incremental\": \"Address root causes, not symptoms. Constitutional-level solutions for permanent change.\",\n  \"people_over_profit\": \"Working families first. Universal access over market-based solutions. Public goods over privatization.\",\n  \"unity_over_division\": \"Build bridges while confronting unjust systems. Shared struggles: housing, healthcare, education.\",\n  \"radical_transparency\": \"Complete accountability. No immunity for the powerful. Real consequences for corruption.\",\n  \"universal_rights\": \"Healthcare, education, housing as human rights, not commodities. Automatic coverage from birth.\",\n  \"constitutional_framework\": [\n    \"End corporate money in politics\",\n    \"Universal public financing of elections\", \n    \"End presidential and congressional immunity\",\n    \"Ban stock trading by officials\",\n    \"Prohibit nepotism\",\n    \"Term limits for all federal positions\",\n    \"Truth and accountability amendment\"\n  ],\n  \"economic_justice\": [\n    \"American Social Trust Fund\",\n    \"Automation dividend\", \n    \"Universal healthcare\",\n    \"Tuition-free higher education\",\n    \"National housing initiative\"\n  ],\n  \"individual_sovereignty\": [\n    \"Right to grow food\",\n    \"Right to repair\", \n    \"Right to build\",\n    \"Responsible gun ownership\"\n  ]\n};\n\nconst formattedContext = `\nMANIFESTO PRINCIPLES FOR AI PROCESSING:\n\n**CORE PHILOSOPHY**: New American Patriotism - Love of fellow citizens, not blind loyalty to power\n\n**DECISION PRIORITIES**:\n1. Transformative over incremental solutions\n2. People over profit in all considerations  \n3. Unity through shared struggles, confronting unjust systems\n4. Radical transparency and accountability\n5. Universal rights as foundation\n\n**CONSTITUTIONAL FRAMEWORK**: ${manifestoPrinciples.constitutional_framework.join(', ')}\n\n**ECONOMIC JUSTICE PRIORITIES**: ${manifestoPrinciples.economic_justice.join(', ')}\n\n**INDIVIDUAL SOVEREIGNTY**: ${manifestoPrinciples.individual_sovereignty.join(', ')}\n\n**AI PROCESSING INSTRUCTION**: Every analysis and recommendation must advance these principles and contribute to building a government as good and decent as the American people.\n`;\n\nreturn [{\n  json: {\n    ...item.json,\n    manifesto_context: formattedContext,\n    manifesto_principles: manifestoPrinciples,\n    processing_timestamp: new Date().toISOString()\n  }\n}];"
      }
    },
    {
      "name": "Extract Document Content",
      "type": "@n8n/n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [600, 300],
      "parameters": {
        "language": "javascript",
        "jsCode": "// Extract and normalize document content\nlet extractedContent = '';\nlet documentType = 'unknown';\n\n// Handle different input types\nif (item.json.body && item.json.body.content) {\n  // Direct content submission\n  extractedContent = item.json.body.content;\n  documentType = item.json.body.type || 'text';\n} else if (item.json.body && item.json.body.text) {\n  // Text content\n  extractedContent = item.json.body.text;\n  documentType = 'text';\n} else if (item.json.body) {\n  // Assume the body is the content\n  extractedContent = JSON.stringify(item.json.body);\n  documentType = 'json';\n} else {\n  // Default handling\n  extractedContent = JSON.stringify(item.json);\n  documentType = 'fallback';\n}\n\n// Basic content analysis\nconst wordCount = extractedContent.split(/\\s+/).length;\nconst hasPolicy = /policy|legislation|law|amendment|bill|act/i.test(extractedContent);\nconst hasEconomic = /economic|financial|budget|tax|healthcare|education/i.test(extractedContent);\nconst hasPolitical = /political|democracy|election|campaign|voting/i.test(extractedContent);\n\nreturn [{\n  json: {\n    ...item.json,\n    document_content: extractedContent,\n    document_type: documentType,\n    content_length: extractedContent.length,\n    word_count: wordCount,\n    content_analysis: {\n      has_policy_content: hasPolicy,\n      has_economic_content: hasEconomic,\n      has_political_content: hasPolitical\n    },\n    extraction_method: 'automated',\n    extraction_timestamp: new Date().toISOString()\n  }\n}];"
      }
    },
    {
      "name": "Lead Coordinator Agent",
      "type": "@n8n/n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [800, 300],
      "parameters": {
        "url": "https://api.anthropic.com/v1/messages",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "anthropicApi",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "anthropic-version",
              "value": "2023-06-01"
            }
          ]
        },
        "sendBody": true,
        "bodyContentType": "json",
        "jsonBody": "{\n  \"model\": \"claude-3-sonnet-20240229\",\n  \"max_tokens\": 1000,\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"SYSTEM CONTEXT:\\nYou are a Lead Coordinator for document processing that advances New American Patriotism.\\n\\nMANIFESTO PRINCIPLES:\\n{{ $json.manifesto_context }}\\n\\nANALYSIS TASK:\\nAnalyze this document and determine the optimal processing strategy based on manifesto alignment.\\n\\nDocument Type: {{ $json.document_type }}\\nDocument Content: {{ $json.document_content }}\\nContent Analysis: {{ JSON.stringify($json.content_analysis) }}\\n\\nREQUIRED OUTPUT (JSON ONLY):\\n{\\n  \\\"alignment_score\\\": 0.0-1.0,\\n  \\\"primary_themes\\\": [\\\"theme1\\\", \\\"theme2\\\"],\\n  \\\"recommended_action\\\": \\\"analyze|enhance|critique|synthesize\\\",\\n  \\\"specialist_agents\\\": [\\\"policy\\\", \\\"research\\\", \\\"editorial\\\"],\\n  \\\"manifesto_connections\\\": [\\\"principle1\\\", \\\"principle2\\\"],\\n  \\\"processing_priority\\\": \\\"high|medium|low\\\",\\n  \\\"expected_outputs\\\": [\\\"brief\\\", \\\"whitepaper\\\", \\\"analysis\\\"],\\n  \\\"reasoning\\\": \\\"explanation of analysis\\\"\\n}\"\n    }\n  ]\n}",
        "options": {}
      }
    },
    {
      "name": "Routing Decision",
      "type": "@n8n/n8n-nodes-base.switch",
      "typeVersion": 1,
      "position": [1000, 300],
      "parameters": {
        "rules": {
          "rules": [
            {
              "operation": "equal",
              "value1": "={{ JSON.parse($json.content[0].text).recommended_action }}",
              "value2": "analyze"
            },
            {
              "operation": "equal", 
              "value1": "={{ JSON.parse($json.content[0].text).recommended_action }}",
              "value2": "enhance"
            },
            {
              "operation": "equal",
              "value1": "={{ JSON.parse($json.content[0].text).recommended_action }}",
              "value2": "critique"
            }
          ]
        },
        "fallbackOutput": 3
      }
    },
    {
      "name": "Policy Analysis Agent",
      "type": "@n8n/n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1200, 100],
      "parameters": {
        "url": "https://api.anthropic.com/v1/messages",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "anthropicApi",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "anthropic-version",
              "value": "2023-06-01"
            }
          ]
        },
        "sendBody": true,
        "bodyContentType": "json",
        "jsonBody": "{\n  \"model\": \"claude-3-sonnet-20240229\",\n  \"max_tokens\": 2000,\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"SPECIALIZATION: Constitutional Policy Analysis\\nMANIFESTO CONTEXT: {{ $('Load Manifesto Context').item.json.manifesto_context }}\\n\\nANALYSIS MISSION:\\nAnalyze this document for policy implications and constitutional reform opportunities that advance New American Patriotism.\\n\\nFOCUS AREAS:\\n1. Constitutional amendment opportunities\\n2. Universal program potential\\n3. Anti-corruption measures\\n4. Economic justice implications\\n5. Individual sovereignty protections\\n\\nCoordinator Analysis: {{ JSON.parse($('Lead Coordinator Agent').item.json.content[0].text) }}\\nDocument: {{ $('Extract Document Content').item.json.document_content }}\\n\\nProvide detailed policy analysis with specific constitutional amendment language and implementation strategies that align with manifesto principles.\"\n    }\n  ]\n}"
      }
    },
    {
      "name": "Editorial Enhancement Agent",
      "type": "@n8n/n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1200, 300],
      "parameters": {
        "url": "https://api.anthropic.com/v1/messages",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "anthropicApi",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "anthropic-version",
              "value": "2023-06-01"
            }
          ]
        },
        "sendBody": true,
        "bodyContentType": "json",
        "jsonBody": "{\n  \"model\": \"claude-3-sonnet-20240229\",\n  \"max_tokens\": 2000,\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"SPECIALIZATION: Editorial Enhancement for New American Patriotism\\nMANIFESTO CONTEXT: {{ $('Load Manifesto Context').item.json.manifesto_context }}\\n\\nEDITORIAL MISSION:\\nEnhance this document's language and messaging to better advance manifesto principles.\\n\\nENHANCEMENT CRITERIA:\\n1. Passionate but principled tone\\n2. Accessible language for working families\\n3. Hopeful vision with concrete steps\\n4. Systemic solutions emphasis\\n5. Unity-building while confronting injustice\\n\\nCoordinator Analysis: {{ JSON.parse($('Lead Coordinator Agent').item.json.content[0].text) }}\\nOriginal Document: {{ $('Extract Document Content').item.json.document_content }}\\n\\nProvide an enhanced version that powerfully advances New American Patriotism principles while maintaining factual accuracy.\"\n    }\n  ]\n}"
      }
    },
    {
      "name": "Research Agent",
      "type": "@n8n/n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1200, 500],
      "parameters": {
        "url": "https://api.anthropic.com/v1/messages",
        "authentication": "predefinedCredentialType",
        "nodeCredentialType": "anthropicApi",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Content-Type",
              "value": "application/json"
            },
            {
              "name": "anthropic-version",
              "value": "2023-06-01"
            }
          ]
        },
        "sendBody": true,
        "bodyContentType": "json",
        "jsonBody": "{\n  \"model\": \"claude-3-sonnet-20240229\",\n  \"max_tokens\": 2000,\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"SPECIALIZATION: Research & Critical Analysis\\nMANIFESTO CONTEXT: {{ $('Load Manifesto Context').item.json.manifesto_context }}\\n\\nRESEARCH MISSION:\\nProvide critical analysis and research-backed recommendations aligned with manifesto principles.\\n\\nRESEARCH PRIORITIES:\\n1. Constitutional amendment precedents\\n2. Universal program success stories globally\\n3. Corporate power concentration evidence\\n4. Working family impact data\\n5. Implementation feasibility analysis\\n\\nCoordinator Analysis: {{ JSON.parse($('Lead Coordinator Agent').item.json.content[0].text) }}\\nDocument to Research: {{ $('Extract Document Content').item.json.document_content }}\\n\\nProvide research findings, fact-checking, and evidence-based recommendations that support manifesto-aligned conclusions.\"\n    }\n  ]\n}"
      }
    },
    {
      "name": "Human Review Queue",
      "type": "@n8n/n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [1200, 700],
      "parameters": {
        "language": "javascript",
        "jsCode": "// Queue for human review when AI confidence is low or content is ambiguous\nconst reviewInfo = {\n  document_id: item.json.processing_timestamp,\n  review_reason: 'AI coordinator unable to determine optimal processing path',\n  coordinator_analysis: JSON.parse(item.json.content[0].text),\n  document_content: item.json.document_content.substring(0, 500) + '...',\n  manifesto_principles: item.json.manifesto_principles,\n  review_priority: 'medium',\n  assigned_to: 'human_reviewer',\n  created_at: new Date().toISOString(),\n  status: 'pending_review'\n};\n\n// This would typically send to a review system or notification\nconsole.log('Document queued for human review:', reviewInfo);\n\nreturn [{\n  json: {\n    ...item.json,\n    review_info: reviewInfo,\n    processing_status: 'human_review_required',\n    ai_output: 'Document requires human review before processing can continue.'\n  }\n}];"
      }
    },
    {
      "name": "Quality Control Validator",
      "type": "@n8n/n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [1400, 300],
      "parameters": {
        "language": "javascript",
        "jsCode": "// Validate AI output against manifesto principles\nfunction validateAgainstManifesto(content, manifestoPrinciples) {\n  const text = content.toLowerCase();\n  \n  // Scoring functions for each principle\n  const transformativeScore = (\n    (text.includes('constitutional') ? 0.3 : 0) +\n    (text.includes('amendment') ? 0.2 : 0) +\n    (text.includes('systemic') ? 0.2 : 0) +\n    (text.includes('universal') ? 0.3 : 0)\n  );\n  \n  const peopleFirstScore = (\n    (text.includes('working families') ? 0.3 : 0) +\n    (text.includes('public good') ? 0.2 : 0) +\n    (text.includes('corporate') && text.includes('power') ? 0.2 : 0) +\n    (text.includes('healthcare') || text.includes('education') || text.includes('housing') ? 0.3 : 0)\n  );\n  \n  const unityScore = (\n    (text.includes('unity') || text.includes('together') ? 0.3 : 0) +\n    (text.includes('shared') || text.includes('common') ? 0.2 : 0) +\n    (!text.includes('partisan') || text.includes('bipartisan') ? 0.2 : 0) +\n    (text.includes('american') && text.includes('patriot') ? 0.3 : 0)\n  );\n  \n  const accountabilityScore = (\n    (text.includes('transparency') ? 0.25 : 0) +\n    (text.includes('accountability') ? 0.25 : 0) +\n    (text.includes('enforcement') ? 0.25 : 0) +\n    (text.includes('consequences') ? 0.25 : 0)\n  );\n  \n  const universalRightsScore = (\n    (text.includes('human right') ? 0.4 : 0) +\n    (text.includes('universal') ? 0.3 : 0) +\n    (text.includes('automatic coverage') || text.includes('tuition-free') ? 0.3 : 0)\n  );\n  \n  return {\n    transformative_score: Math.min(transformativeScore, 1.0),\n    people_first_score: Math.min(peopleFirstScore, 1.0),\n    unity_score: Math.min(unityScore, 1.0),\n    accountability_score: Math.min(accountabilityScore, 1.0),\n    universal_rights_score: Math.min(universalRightsScore, 1.0)\n  };\n}\n\n// Get AI output content\nlet aiContent = '';\nif (item.json.content && item.json.content[0] && item.json.content[0].text) {\n  aiContent = item.json.content[0].text;\n} else if (item.json.ai_output) {\n  aiContent = item.json.ai_output;\n} else {\n  aiContent = JSON.stringify(item.json);\n}\n\n// Run validation\nconst validationResults = validateAgainstManifesto(aiContent, item.json.manifesto_principles);\n\n// Calculate overall grade\nconst scores = Object.values(validationResults);\nconst averageScore = scores.reduce((a, b) => a + b, 0) / scores.length;\n\nconst overallGrade = averageScore >= 0.8 ? 'A' : \n                   averageScore >= 0.6 ? 'B' : \n                   averageScore >= 0.4 ? 'C' : \n                   averageScore >= 0.2 ? 'D' : 'F';\n\n// Determine if content passes quality control\nconst passesQC = averageScore >= 0.5;\n\nreturn [{\n  json: {\n    ...item.json,\n    validation_results: validationResults,\n    overall_manifesto_grade: overallGrade,\n    average_alignment_score: averageScore,\n    passes_quality_control: passesQC,\n    validated_content: aiContent,\n    validation_timestamp: new Date().toISOString()\n  }\n}];"
      }
    },
    {
      "name": "Output Formatter",
      "type": "@n8n/n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [1600, 300],
      "parameters": {
        "language": "javascript",
        "jsCode": "// Format final output with professional presentation\nconst manifestoSignature = `\\n\\n---\\n**Processed through Manifesto-Aligned AI System**\\n*Advancing New American Patriotism: Government as good and decent as the American people*\\n\\nProcessed: ${new Date().toISOString()}\\nManifesto Alignment Grade: ${item.json.overall_manifesto_grade}\\nQuality Control: ${item.json.passes_quality_control ? 'PASSED' : 'REQUIRES REVIEW'}`;\n\n// Generate title based on content and manifesto principles\nfunction generateTitle(content, manifestoPrinciples) {\n  const text = content.toLowerCase();\n  if (text.includes('healthcare')) return 'Healthcare as a Human Right: A Manifesto-Aligned Analysis';\n  if (text.includes('education')) return 'Universal Education: Building American Prosperity';\n  if (text.includes('housing')) return 'The Right to Housing: Constitutional Framework for Justice';\n  if (text.includes('campaign') || text.includes('election')) return 'Reclaiming Democracy: Campaign Finance Reform';\n  if (text.includes('economic')) return 'Economic Justice: New American Patriotism in Action';\n  return 'Policy Analysis: Advancing New American Patriotism';\n}\n\nconst formattedOutput = {\n  title: generateTitle(item.json.validated_content, item.json.manifesto_principles),\n  subtitle: `Processed ${new Date().toLocaleDateString()} • Grade: ${item.json.overall_manifesto_grade}`,\n  content: item.json.validated_content + manifestoSignature,\n  metadata: {\n    processed_date: new Date().toISOString(),\n    manifesto_alignment_grade: item.json.overall_manifesto_grade,\n    average_alignment_score: item.json.average_alignment_score,\n    validation_details: item.json.validation_results,\n    processing_agent: item.json.specialist_agents || 'unknown',\n    document_type: item.json.document_type,\n    word_count: item.json.word_count,\n    passes_quality_control: item.json.passes_quality_control\n  },\n  manifesto_principles_advanced: Object.keys(item.json.validation_results).filter(key => item.json.validation_results[key] > 0.5).map(key => key.replace('_score', '').replace('_', ' '))\n};\n\nreturn [{\n  json: {\n    ...item.json,\n    formatted_output: formattedOutput,\n    final_content: formattedOutput.content,\n    processing_complete: true\n  }\n}];"
      }
    },
    {
      "name": "Distribution",
      "type": "@n8n/n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [1800, 300],
      "parameters": {
        "language": "javascript",
        "jsCode": "// Final distribution and notification\nconst output = {\n  success: true,\n  document_id: item.json.processing_timestamp,\n  title: item.json.formatted_output.title,\n  content: item.json.formatted_output.content,\n  manifesto_grade: item.json.overall_manifesto_grade,\n  alignment_score: item.json.average_alignment_score,\n  quality_control_passed: item.json.passes_quality_control,\n  principles_advanced: item.json.formatted_output.manifesto_principles_advanced,
  metadata: item.json.formatted_output.metadata,\n  processing_summary: {\n    input_length: item.json.content_length,\n    output_length: item.json.formatted_output.content.length,\n    processing_time: new Date().toISOString(),\n    agent_type: Array.isArray(item.json.specialist_agents) ? item.json.specialist_agents[0] : 'unknown'\n  }\n};\n\n// Log completion\nconsole.log('Document processing complete:', {\n  title: output.title,\n  grade: output.manifesto_grade,\n  passed_qc: output.quality_control_passed\n});\n\nreturn [{ json: output }];"
      }
    }
  ]
} 