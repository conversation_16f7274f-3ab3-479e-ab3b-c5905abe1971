{"name": "fact-checking-mcp-server", "version": "1.0.0", "description": "MCP server for comprehensive fact-checking, source validation, and claim verification", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "crypto": "^1.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "tiktoken": "^1.0.15", "openai": "^4.20.1", "axios": "^1.6.2", "winston": "^3.11.0", "cheerio": "^1.0.0-rc.12", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "natural": "^6.12.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["fact-checking", "political", "verification", "mcp-server", "source-validation", "claim-analysis", "bias-detection"], "author": "n8n-workflow-system", "license": "MIT"}