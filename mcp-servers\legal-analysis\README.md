# Legal Analysis MCP Server

Advanced legal document analysis, constitutional compliance checking, and legal research server implementing the Model Context Protocol (MCP).

## Features

### Core Legal Analysis Tools

- **Legal Document Analyzer** - Comprehensive analysis of legal documents with AI-powered insights
- **Constitutional Compliance Check** - Automated constitutional compliance verification
- **Precedent Research** - Legal precedent and case law research capabilities
- **Regulatory Impact Analysis** - Assessment of regulatory impact for proposed legislation
- **Legal Risk Assessment** - Identification and mitigation of legal risks
- **Legal Citation Extractor** - Extraction and validation of legal citations
- **Legal Terminology Analyzer** - Analysis of legal terminology with definitions
- **Comparative Legal Analysis** - Document comparison for consistency and conflicts

### Database Integration

- **PostgreSQL** - Persistent storage for analysis results, precedents, and compliance data
- **Redis** - High-performance caching for frequent legal queries
- **Structured Data Models** - Comprehensive schemas for legal analysis tracking

### AI-Powered Analysis

- **OpenAI Integration** - Advanced legal document analysis using GPT-4
- **Natural Language Processing** - Legal terminology extraction and analysis
- **Citation Recognition** - Automated identification and validation of legal citations
- **Constitutional Reference Matching** - Smart matching against constitutional provisions

## Installation

### Prerequisites

- Node.js 18+
- PostgreSQL database
- Redis cache
- OpenAI API key

### Environment Variables

```bash
OPENAI_API_KEY=your_openai_api_key
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=legal_analysis
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=n8n_secure_password
REDIS_HOST=localhost
REDIS_PORT=6379
HEALTH_PORT=8091
LOG_LEVEL=info
```

### Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test
```

### Docker Deployment

```bash
# Build image
docker build -t legal-analysis-mcp-server .

# Run container
docker run -d \
  --name legal-analysis-mcp \
  -p 8091:8091 \
  -e OPENAI_API_KEY=your_key \
  -e POSTGRES_HOST=postgres \
  -e REDIS_HOST=redis \
  legal-analysis-mcp-server
```

## MCP Tools

### legal_document_analyzer

Analyze legal documents and identify key provisions, legal principles, and potential issues.

**Parameters:**
- `document_path` (string) - Path to legal document file
- `document_content` (string) - Raw text content of legal document
- `analysis_type` (enum) - Type of analysis: basic, comprehensive, constitutional, regulatory, contractual
- `extract_citations` (boolean) - Extract legal citations and case references
- `identify_key_provisions` (boolean) - Identify key legal provisions and clauses

### constitutional_compliance_check

Check compliance with constitutional requirements and identify potential constitutional issues.

**Parameters:**
- `document_content` (string, required) - Legal document content to check
- `focus_amendments` (array) - Specific constitutional amendments to focus on (1-27)
- `document_type` (enum) - Type of document: legislation, regulation, policy, executive_order, judicial_opinion
- `detailed_analysis` (boolean) - Provide detailed constitutional analysis with citations

### precedent_research

Research legal precedents and case law relevant to specific legal issues or principles.

**Parameters:**
- `legal_issue` (string, required) - Legal issue or principle to research
- `jurisdiction` (enum) - Jurisdiction scope: federal, state, all
- `time_period` (enum) - Time period focus: recent, landmark, all
- `court_level` (enum) - Court level: supreme_court, appellate, district, all
- `max_results` (integer) - Maximum number of precedents to return (1-50)

### regulatory_impact_analysis

Analyze regulatory impact of proposed legislation or policy changes.

**Parameters:**
- `document_content` (string, required) - Proposed legislation or regulation content
- `impact_categories` (array) - Categories of impact: economic, social, environmental, administrative, constitutional
- `affected_sectors` (array) - Specific sectors or industries that may be affected
- `implementation_timeline` (string) - Proposed implementation timeline
- `stakeholder_analysis` (boolean) - Include stakeholder impact analysis

### legal_risk_assessment

Assess legal risks and compliance issues in documents or proposed actions.

**Parameters:**
- `document_content` (string, required) - Document content to assess
- `context_description` (string) - Additional context about the situation
- `risk_categories` (array) - Categories of legal risk: constitutional, statutory, regulatory, contractual, tort, criminal
- `risk_tolerance` (enum) - Risk tolerance level: low, medium, high
- `include_mitigation` (boolean) - Include risk mitigation strategies

### legal_citation_extractor

Extract and validate legal citations from documents.

**Parameters:**
- `document_content` (string, required) - Document content to extract citations from
- `citation_types` (array) - Types of citations: case_law, statutes, regulations, constitutional, secondary_sources
- `validate_citations` (boolean) - Validate citation format and accuracy
- `include_context` (boolean) - Include surrounding context for each citation

### legal_terminology_analyzer

Analyze legal terminology usage and provide definitions and explanations.

**Parameters:**
- `document_content` (string, required) - Document content to analyze
- `complexity_level` (enum) - Complexity level: basic, intermediate, advanced
- `include_definitions` (boolean) - Include definitions for identified legal terms
- `plain_language_summary` (boolean) - Provide plain language summary of complex terms

### comparative_legal_analysis

Compare legal documents for consistency, conflicts, and alignment.

**Parameters:**
- `document_a` (string, required) - First legal document content
- `document_b` (string, required) - Second legal document content
- `comparison_type` (enum) - Type of comparison: consistency, conflicts, alignment, comprehensive
- `focus_areas` (array) - Specific legal areas to focus comparison on

## Database Schema

### legal_analysis
- Analysis results and document metadata
- Constitutional issues and compliance scores
- Legal risks and recommendations

### legal_precedents
- Legal precedent citations and references
- Case names, courts, and legal principles
- Relevance scoring and categorization

### constitutional_compliance
- Constitutional compliance tracking
- Amendment-specific compliance issues
- Recommendations and remediation steps

### regulatory_impact
- Regulatory impact assessments
- Economic and social impact analysis
- Implementation timelines and affected parties

### legal_risk_assessment
- Legal risk categorization and scoring
- Mitigation strategies and recommendations
- Probability and impact severity ratings

## Health Monitoring

The server provides a health endpoint at `/health` for monitoring:

```bash
curl http://localhost:8091/health
```

Returns server status, uptime, and version information.

## Security Features

- **Helmet.js** - Security headers and protection
- **CORS** - Cross-origin resource sharing configuration
- **Input Validation** - Comprehensive parameter validation
- **Error Handling** - Secure error responses without sensitive data exposure
- **Non-root User** - Docker container runs with limited privileges

## Logging

Winston-based logging with configurable levels:
- Console output for development
- File logging for production
- Structured JSON format for log aggregation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- GitHub Issues: https://github.com/Beaulewis1977/n8n-workflow/issues
- Documentation: See inline code documentation