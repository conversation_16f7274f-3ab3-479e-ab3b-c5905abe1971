# Workflow Prompt Strategy for Complex Document Processing

## Handling Your Use Cases

### Simple Case: 5 Documents → 1 White Paper
**Input Method**: Place 5 documents + 1 instruction document in political_in folder
**Instruction Document Format**:
```markdown
# WORKFLOW_INSTRUCTION
TYPE: simple_synthesis
OUTPUT: single_whitepaper
TITLE: "Universal Healthcare Policy Framework"
FOCUS: "Combine key points from all documents into comprehensive policy paper"
TONE: professional, policy-focused
LENGTH: 3000-5000 words
```

### Complex Case: 100 Documents → Multiple Related Documents
**Input Method**: Place 100 documents + 1 master instruction document
**Master Instruction Format**:
```markdown
# WORKFLOW_INSTRUCTION
TYPE: complex_analysis
OUTPUT: multiple_documents
PROJECT: "Universal Healthcare Implementation"

## Required Outputs:
1. "Universal Healthcare White Paper" - comprehensive overview
2. "5-Year Implementation Plan" - timeline and phases  
3. "Healthcare Workforce Development" - education/training needs
4. "Funding and Payment Models" - financial framework
5. "Private to Public Transition Plan" - migration strategy

## Cross-References:
- Link healthcare workforce needs to education policy docs
- Connect funding models to economic impact studies
- Reference implementation timeline in all documents

## AI Instructions:
- Use RAG to find relevant content across all 100 documents
- Maintain consistency across all output documents
- Create internal cross-references between outputs
- Follow manifesto principles throughout
```

## Workflow Logic:
1. **Document Ingestion**: AI reads instruction document first
2. **Task Classification**: Determines simple vs complex processing
3. **RAG Query Planning**: AI plans what information to retrieve
4. **Content Generation**: Creates documents according to instructions
5. **Cross-Reference Validation**: Ensures consistency across outputs

## Benefits:
- Flexible instruction system
- Scalable from simple to complex tasks
- Maintains document relationships
- Leverages your manifesto for consistent voice