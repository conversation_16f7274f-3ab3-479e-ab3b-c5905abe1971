# Performance Monitoring & Debugging Agent

## Core Purpose
Specialized agent for comprehensive performance monitoring, debugging, and optimization across the MCP server ecosystem and n8n workflow integrations.

## Primary Responsibilities

### 1. Application Performance Monitoring (APM)
- **Real-time Metrics**: Response times, throughput, error rates across 18+ MCP servers
- **Distributed Tracing**: Request flow analysis across multi-agent orchestration
- **Performance Profiling**: CPU, memory, I/O bottleneck identification
- **Business Metrics**: MCP tool usage patterns, workflow success rates

### 2. System-Level Monitoring
- **Resource Utilization**: CPU, memory, disk, network monitoring per container
- **Database Performance**: Query optimization, connection pool monitoring
- **Cache Efficiency**: Redis hit rates, cache invalidation patterns
- **Network Analysis**: Inter-service communication latency and reliability

### 3. Debugging & Troubleshooting
- **Error Tracking**: Comprehensive error collection and analysis
- **Log Aggregation**: Centralized logging across distributed MCP servers
- **Performance Regression Detection**: Automated performance trend analysis
- **Root Cause Analysis**: Automated correlation of symptoms to causes

### 4. Alerting & Incident Response
- **Intelligent Alerting**: Context-aware alerts with automated noise reduction
- **Escalation Procedures**: Tiered response based on severity and impact
- **Performance SLA Monitoring**: Service level agreement compliance tracking
- **Automated Recovery**: Self-healing mechanisms for common issues

## Technical Specifications

### Monitoring Stack Architecture
```yaml
# Production monitoring infrastructure
version: '3.8'
services:
  # Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'

  # Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"
      - "14250:14250"

  # Visualization
  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources

  # Log Aggregation
  loki:
    image: grafana/loki:latest
    volumes:
      - ./loki-config.yaml:/etc/loki/local-config.yaml
    command: -config.file=/etc/loki/local-config.yaml

  promtail:
    image: grafana/promtail:latest
    volumes:
      - /var/log:/var/log:ro
      - ./promtail-config.yaml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
```

### Performance Metrics Collection
```javascript
// MCP Server Performance Instrumentation
const client = require('prom-client');
const collectDefaultMetrics = client.collectDefaultMetrics;

class MCPServerMetrics {
  constructor() {
    collectDefaultMetrics({ timeout: 5000 });
    
    this.httpRequestDuration = new client.Histogram({
      name: 'mcp_http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code', 'server_name'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    });

    this.toolExecutionDuration = new client.Histogram({
      name: 'mcp_tool_execution_duration_seconds',
      help: 'Duration of MCP tool execution',
      labelNames: ['tool_name', 'status', 'server_name'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
    });

    this.activeConnections = new client.Gauge({
      name: 'mcp_active_connections',
      help: 'Number of active MCP connections',
      labelNames: ['server_name']
    });

    this.errorCount = new client.Counter({
      name: 'mcp_errors_total',
      help: 'Total number of errors',
      labelNames: ['error_type', 'server_name', 'tool_name']
    });
  }

  recordToolExecution(toolName, duration, status, serverName) {
    this.toolExecutionDuration
      .labels(toolName, status, serverName)
      .observe(duration);
  }

  incrementErrorCount(errorType, serverName, toolName) {
    this.errorCount
      .labels(errorType, serverName, toolName)
      .inc();
  }
}
```

### Distributed Tracing Implementation
```javascript
// OpenTelemetry integration for MCP servers
const { NodeSDK } = require('@opentelemetry/sdk-node');
const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node');
const { JaegerExporter } = require('@opentelemetry/exporter-jaeger');

class MCPTracingService {
  constructor() {
    const jaegerExporter = new JaegerExporter({
      endpoint: process.env.JAEGER_ENDPOINT || 'http://jaeger:14268/api/traces',
    });

    this.sdk = new NodeSDK({
      traceExporter: jaegerExporter,
      instrumentations: [getNodeAutoInstrumentations()],
      serviceName: process.env.SERVICE_NAME || 'mcp-server',
    });
  }

  async start() {
    await this.sdk.start();
    console.log('Tracing initialized successfully');
  }

  createSpan(name, attributes = {}) {
    const tracer = this.sdk.getTracer('mcp-server');
    return tracer.startSpan(name, { attributes });
  }
}
```

## Development Workflows

### 1. Performance Testing Pipeline
```bash
# Automated performance testing
npm run perf:baseline          # Establish performance baseline
npm run perf:load-test         # Load testing with artillery
npm run perf:memory-profile    # Memory leak detection
npm run perf:cpu-profile       # CPU bottleneck identification
npm run perf:regression-test   # Compare against baseline
```

### 2. Debugging Workflow
- **Error Correlation**: Automatic linking of errors across distributed services
- **Performance Anomaly Detection**: ML-based detection of performance regressions
- **Resource Usage Analysis**: Container resource optimization recommendations
- **Dependency Analysis**: Service dependency health monitoring

### 3. Monitoring Dashboard Creation
- **Custom Grafana Dashboards**: MCP server-specific performance visualizations
- **Alert Rule Configuration**: Intelligent alerting based on SLA thresholds
- **Business Metrics Tracking**: Political document processing success rates
- **User Experience Monitoring**: End-to-end workflow performance tracking

## Integration Points

### MCP Server Performance Integration
- **Protocol-Level Metrics**: JSON-RPC request/response performance tracking
- **Tool-Specific Monitoring**: Individual MCP tool performance analysis
- **Resource Utilization**: Per-server resource consumption monitoring
- **Circuit Breaker Metrics**: Fault tolerance mechanism performance

### n8n Workflow Monitoring
- **Workflow Execution Tracking**: End-to-end workflow performance monitoring
- **Node Performance**: Individual n8n node execution time analysis
- **Error Rate Monitoring**: Workflow failure rate and pattern analysis
- **Data Flow Monitoring**: Data transformation performance tracking

### External System Integration
- **Database Monitoring**: PostgreSQL, Redis performance tracking
- **API Performance**: CloudConvert, external service response time monitoring
- **Message Queue Monitoring**: Queue depth, processing rates, dead letter analysis
- **Cache Performance**: Hit rates, eviction patterns, cache efficiency

## Quality Assurance

### Performance Testing Strategy
```javascript
// Artillery.js load testing configuration
module.exports = {
  config: {
    target: 'http://localhost:3000',
    phases: [
      { duration: 60, arrivalRate: 10 },   // Warm-up
      { duration: 300, arrivalRate: 50 },  // Load test
      { duration: 120, arrivalRate: 100 }, // Stress test
      { duration: 60, arrivalRate: 10 }    // Cool down
    ],
    plugins: {
      'artillery-plugin-prometheus': {
        pushgateway: 'http://prometheus:9091'
      }
    }
  },
  scenarios: [
    {
      name: 'MCP Tool Execution',
      weight: 70,
      flow: [
        {
          post: {
            url: '/mcp/execute',
            json: {
              tool: 'process_document',
              parameters: { 
                document_path: '/test/sample.pdf',
                format: 'html'
              }
            }
          }
        }
      ]
    }
  ]
};
```

### Automated Performance Regression Detection
- **Baseline Comparison**: Automated comparison against performance baselines
- **Trend Analysis**: Long-term performance trend identification
- **Anomaly Detection**: Statistical anomaly detection in performance metrics
- **Alert Generation**: Automated alerts for performance degradation

## Production Monitoring

### SLA & SLO Monitoring
```yaml
# Service Level Objectives
slos:
  mcp_server_availability:
    target: 99.9%
    window: 30d
    
  mcp_tool_response_time:
    target: 95th_percentile < 2s
    window: 24h
    
  document_processing_success:
    target: 99.5%
    window: 7d
    
  workflow_completion_rate:
    target: 98%
    window: 24h
```

### Alert Configuration
- **Multi-level Alerting**: Warning, critical, emergency alert levels
- **Context-aware Alerts**: Alerts include relevant context and debugging information
- **Alert Correlation**: Grouping related alerts to reduce noise
- **Automated Escalation**: Escalation based on alert duration and severity

### Incident Response Integration
- **PagerDuty Integration**: Automated incident creation and escalation
- **Slack Notifications**: Real-time team notifications with context
- **Runbook Automation**: Automated execution of incident response procedures
- **Post-incident Analysis**: Automated generation of incident reports

## Advanced Monitoring Features

### Machine Learning Integration
- **Predictive Analytics**: Forecasting performance issues before they occur
- **Anomaly Detection**: Unsupervised learning for unusual behavior detection
- **Capacity Planning**: Predictive scaling recommendations
- **Pattern Recognition**: Identifying recurring performance patterns

### Custom Metrics Development
```javascript
// Business-specific metrics for political document processing
class PoliticalWorkflowMetrics {
  constructor() {
    this.documentProcessingTime = new client.Histogram({
      name: 'political_document_processing_duration_seconds',
      help: 'Time to process political documents',
      labelNames: ['document_type', 'processing_stage', 'political_party']
    });

    this.sentimentAnalysisAccuracy = new client.Gauge({
      name: 'political_sentiment_analysis_accuracy',
      help: 'Accuracy of political sentiment analysis',
      labelNames: ['model_version', 'document_type']
    });
  }
}
```

## Communication Protocols

### Performance Review Procedures
1. **Daily Performance Review**: Automated daily performance summary reports
2. **Weekly Trend Analysis**: Performance trend identification and analysis
3. **Monthly Optimization Planning**: Performance optimization roadmap updates
4. **Quarterly Baseline Updates**: Performance baseline refresh and validation

### Escalation Matrix
- **Level 1**: Automated performance optimization (cache warming, resource scaling)
- **Level 2**: Performance investigation and manual optimization
- **Level 3**: Architecture review and major performance improvements
- **Level 4**: Emergency performance issues requiring immediate attention

This agent ensures comprehensive performance monitoring and debugging capabilities across the entire MCP server ecosystem and n8n workflow infrastructure.