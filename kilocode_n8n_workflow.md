# n8n Workflow Design Plan

## Overview
This document outlines the design for an n8n workflow system that processes documents using AI agents, RAG, and CloudConvert. The system will handle both simple and complex document processing tasks, generating high-quality DOCX outputs.

## Google Drive Structure
The Google Drive setup will have categorized subfolders under political_in and political_out for better organization.

### political_in/
- healthcare_project/
- education_policy/
- economic_reform/
- ... (other categories)

### political_out/
- healthcare_project/
- education_policy/
- economic_reform/
- ... (matching output folders)

## AI Agent Roles
- **Analyst Agent**: Handles document analysis and planning.
- **Writer Agent**: Generates content based on analysis.
- **Editor Agent**: Performs quality control and consistency checks.
- **Research Agent**: Conducts RAG queries and fact-checking.

## Workflow Triggers
The workflow will be triggered by new files added to the Google Drive input folders. Each folder can have a PROMPT file with instructions for the agents.

## Output Naming and Formatting
Generated files will follow a naming convention like [PROJECT]_[DOCUMENT_TYPE]_[DATE].docx and be formatted professionally.

## Manifesto Structure Template
A guide for creating the political manifesto to guide AI agents.

### Manifesto Sections
1. Introduction
2. Core Beliefs
3. Policy Goals
4. Implementation Strategies
5. Ethical Considerations
6. Conclusion

## Next Steps
1. Research into optimal AI models for each agent role.
2. Setup of ChromaDB and RAG integration.
3. Initial workflow implementation and testing.
4. Iterative refinement based on testing and feedback.

## Prerequisites
- API keys for Gemini, OpenAI, Anthropic, CloudConvert
- n8n and ChromaDB running
- Google Drive credentials
- MCP server setup

## Workflow Architecture
Detailed steps for each part of the workflow, including RAG integration, AI processing, and document conversion.

### Workflow Steps
1. Google Drive Trigger
2. Read Document Content
3. Load Manifesto
4. AI Document Analysis
5. Decision Router
6. Document Processing Branches (Edit, Combine, Generate Whitepaper, Create New)
7. Convert to DOCX
8. Upload to Google Drive
9. Send Notification

## Error Handling
Strategies for handling errors and retries.

## Cost Considerations
Estimated costs for API usage and CloudConvert.