#!/usr/bin/env node

import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { Client } from 'pg';
import { createClient } from 'redis';
import axios from 'axios';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import { marked } from 'marked';
import createDOMPurify from 'dompurify';
import { JSDOM } from 'jsdom';
import path from 'path';
import fs from 'fs/promises';

/**
 * Political Document Processing Chat Interface
 * Real-time conversational interface for document processing and AI interaction
 */

class ChatInterface {
  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new Server(this.server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    // Database connections
    this.pgClient = null;
    this.redisClient = null;

    // DOMPurify for sanitizing HTML
    const window = new JSDOM('').window;
    this.DOMPurify = createDOMPurify(window);

    // Configuration
    this.port = process.env.PORT || 3001;
    this.n8nWebhookUrl = process.env.N8N_WEBHOOK_URL || 'http://n8n:5678/webhook/process-document';
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'political_conversations',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD
    });

    try {
      await this.pgClient.connect();
      await this.redisClient.connect();
      console.log('Chat Interface: Connected to databases');
    } catch (error) {
      console.error('Failed to initialize Chat Interface:', error);
      throw error;
    }
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "ws:", "wss:"]
        }
      }
    }));

    // CORS
    this.app.use(cors({
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP'
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // File upload configuration
    const storage = multer.memoryStorage();
    this.upload = multer({ 
      storage,
      limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit
      fileFilter: (req, file, cb) => {
        const allowedTypes = ['text/plain', 'text/markdown', 'application/pdf', 
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error('Unsupported file type'));
        }
      }
    });

    // Static files
    this.app.use('/public', express.static('public'));
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        services: {
          postgres: this.pgClient ? 'connected' : 'disconnected',
          redis: this.redisClient ? 'connected' : 'disconnected'
        }
      });
    });

    // Get conversation history
    this.app.get('/api/conversations/:sessionId/history', async (req, res) => {
      try {
        const { sessionId } = req.params;
        const limit = parseInt(req.query.limit) || 50;
        
        const history = await this.getConversationHistory(sessionId, limit);
        res.json({ history });
      } catch (error) {
        console.error('Error fetching conversation history:', error);
        res.status(500).json({ error: 'Failed to fetch conversation history' });
      }
    });

    // Start new conversation session
    this.app.post('/api/conversations/start', async (req, res) => {
      try {
        const { userId = 'beau_lewis', theme } = req.body;
        const sessionId = uuidv4();
        
        await this.createConversationSession(sessionId, userId, theme);
        
        res.json({ 
          sessionId,
          message: 'Conversation session started',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error starting conversation:', error);
        res.status(500).json({ error: 'Failed to start conversation' });
      }
    });

    // Process document request
    this.app.post('/api/documents/process', this.upload.single('file'), async (req, res) => {
      try {
        const {
          sessionId,
          taskType,
          topic,
          category,
          tokenTier,
          researchLevel,
          outputFormat
        } = req.body;

        let fileData = null;
        if (req.file) {
          fileData = {
            filename: req.file.originalname,
            content: req.file.buffer.toString('utf8'),
            mimetype: req.file.mimetype
          };
        }

        const result = await this.processDocumentRequest({
          sessionId,
          taskType,
          topic,
          category,
          tokenTier: parseInt(tokenTier),
          researchLevel,
          outputFormat,
          fileData
        });

        res.json(result);
      } catch (error) {
        console.error('Error processing document request:', error);
        res.status(500).json({ error: 'Failed to process document request' });
      }
    });

    // Get document status
    this.app.get('/api/documents/:jobId/status', async (req, res) => {
      try {
        const { jobId } = req.params;
        const status = await this.getDocumentStatus(jobId);
        res.json(status);
      } catch (error) {
        console.error('Error fetching document status:', error);
        res.status(500).json({ error: 'Failed to fetch document status' });
      }
    });

    // Download generated document
    this.app.get('/api/documents/:jobId/download', async (req, res) => {
      try {
        const { jobId } = req.params;
        const documentInfo = await this.getDocumentInfo(jobId);
        
        if (!documentInfo || !documentInfo.output_path) {
          return res.status(404).json({ error: 'Document not found' });
        }

        // Set appropriate headers
        res.setHeader('Content-Disposition', `attachment; filename="${documentInfo.output_filename}"`);
        res.setHeader('Content-Type', this.getContentType(documentInfo.output_filename));
        
        // Stream file
        const fileBuffer = await fs.readFile(documentInfo.output_path);
        res.send(fileBuffer);
      } catch (error) {
        console.error('Error downloading document:', error);
        res.status(500).json({ error: 'Failed to download document' });
      }
    });

    // Get system analytics
    this.app.get('/api/analytics/dashboard', async (req, res) => {
      try {
        const analytics = await this.getSystemAnalytics();
        res.json(analytics);
      } catch (error) {
        console.error('Error fetching analytics:', error);
        res.status(500).json({ error: 'Failed to fetch analytics' });
      }
    });

    // User feedback endpoint
    this.app.post('/api/feedback', async (req, res) => {
      try {
        const { sessionId, jobId, feedbackType, rating, comments } = req.body;
        
        await this.storeFeedback({
          sessionId,
          jobId,
          feedbackType,
          rating,
          comments,
          timestamp: new Date().toISOString()
        });

        res.json({ message: 'Feedback stored successfully' });
      } catch (error) {
        console.error('Error storing feedback:', error);
        res.status(500).json({ error: 'Failed to store feedback' });
      }
    });
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);

      // Join conversation room
      socket.on('join_conversation', async (data) => {
        const { sessionId, userId } = data;
        socket.join(sessionId);
        socket.sessionId = sessionId;
        socket.userId = userId;

        // Update session activity
        await this.updateSessionActivity(sessionId);

        // Send recent conversation history
        const history = await this.getConversationHistory(sessionId, 10);
        socket.emit('conversation_history', { history });

        console.log(`Client ${socket.id} joined conversation ${sessionId}`);
      });

      // Handle chat messages
      socket.on('chat_message', async (data) => {
        try {
          const { sessionId, message, messageType = 'text' } = data;
          
          // Store user message
          const messageId = uuidv4();
          await this.storeConversationMessage({
            sessionId,
            messageId,
            userId: socket.userId,
            message,
            messageType,
            timestamp: new Date().toISOString()
          });

          // Broadcast to room
          this.io.to(sessionId).emit('new_message', {
            messageId,
            userId: socket.userId,
            message,
            messageType,
            timestamp: new Date().toISOString()
          });

          // Process message for AI response if needed
          if (messageType === 'question' || message.includes('?')) {
            await this.processAIResponse(sessionId, message, socket);
          }

        } catch (error) {
          console.error('Error handling chat message:', error);
          socket.emit('error', { message: 'Failed to process message' });
        }
      });

      // Handle document processing requests via socket
      socket.on('process_document', async (data) => {
        try {
          const result = await this.processDocumentRequest(data);
          socket.emit('document_processing_started', result);
          
          // Set up status updates
          this.setupDocumentStatusUpdates(result.jobId, socket);
        } catch (error) {
          console.error('Error processing document via socket:', error);
          socket.emit('document_processing_error', { error: error.message });
        }
      });

      // Handle typing indicators
      socket.on('typing_start', (data) => {
        socket.to(data.sessionId).emit('user_typing', {
          userId: socket.userId,
          typing: true
        });
      });

      socket.on('typing_stop', (data) => {
        socket.to(data.sessionId).emit('user_typing', {
          userId: socket.userId,
          typing: false
        });
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
      });
    });
  }

  async processDocumentRequest(requestData) {
    try {
      const {
        sessionId,
        taskType,
        topic,
        category,
        tokenTier,
        researchLevel,
        outputFormat,
        fileData
      } = requestData;

      // Validate required fields
      if (!taskType || !topic || !category) {
        throw new Error('Missing required fields: taskType, topic, category');
      }

      // Prepare request payload for n8n webhook
      const webhookPayload = {
        sessionId,
        taskType,
        topic,
        category,
        tokenTier: tokenTier || 2,
        researchLevel: researchLevel || 'standard',
        outputFormat: outputFormat || 'markdown',
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: 'chat-interface',
          fileData
        }
      };

      // Send to n8n workflow
      const response = await axios.post(this.n8nWebhookUrl, webhookPayload, {
        timeout: 60000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Store interaction in conversation history
      await this.storeConversationMessage({
        sessionId,
        messageId: uuidv4(),
        userId: 'system',
        message: `Started processing: ${taskType} for "${topic}"`,
        messageType: 'system',
        timestamp: new Date().toISOString(),
        metadata: { jobId: response.data.jobId }
      });

      return {
        success: true,
        jobId: response.data.jobId,
        message: 'Document processing started',
        estimatedCompletion: this.estimateCompletionTime(taskType, tokenTier),
        webhookResponse: response.data
      };

    } catch (error) {
      console.error('Error processing document request:', error);
      throw error;
    }
  }

  async processAIResponse(sessionId, userMessage, socket) {
    try {
      // This would integrate with the manifesto context MCP server
      // to provide intelligent responses about political content
      
      const responseMessage = `I understand you're asking about: "${userMessage}". Let me help you with that political question.`;
      
      // Store AI response
      const messageId = uuidv4();
      await this.storeConversationMessage({
        sessionId,
        messageId,
        userId: 'ai_assistant',
        message: responseMessage,
        messageType: 'ai_response',
        timestamp: new Date().toISOString()
      });

      // Send to client
      socket.emit('ai_response', {
        messageId,
        message: responseMessage,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error processing AI response:', error);
      socket.emit('error', { message: 'Failed to generate AI response' });
    }
  }

  async setupDocumentStatusUpdates(jobId, socket) {
    // Poll for document processing status updates
    const statusInterval = setInterval(async () => {
      try {
        const status = await this.getDocumentStatus(jobId);
        socket.emit('document_status_update', { jobId, status });

        // Stop polling when complete or failed
        if (status.processing_status === 'completed' || status.processing_status === 'failed') {
          clearInterval(statusInterval);
          
          if (status.processing_status === 'completed') {
            socket.emit('document_completed', {
              jobId,
              filename: status.output_filename,
              downloadUrl: `/api/documents/${jobId}/download`
            });
          }
        }
      } catch (error) {
        console.error('Error checking document status:', error);
        clearInterval(statusInterval);
      }
    }, 5000); // Check every 5 seconds

    // Set timeout to stop polling after 10 minutes
    setTimeout(() => {
      clearInterval(statusInterval);
    }, 600000);
  }

  // Database operations

  async createConversationSession(sessionId, userId, theme) {
    await this.pgClient.query(`
      INSERT INTO conversation_sessions (session_id, user_id, conversation_theme, start_time, last_activity)
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT (session_id) DO UPDATE SET last_activity = CURRENT_TIMESTAMP
    `, [sessionId, userId, theme]);
  }

  async updateSessionActivity(sessionId) {
    await this.pgClient.query(`
      UPDATE conversation_sessions 
      SET last_activity = CURRENT_TIMESTAMP 
      WHERE session_id = $1
    `, [sessionId]);
  }

  async getConversationHistory(sessionId, limit = 50) {
    const result = await this.pgClient.query(`
      SELECT * FROM conversation_history 
      WHERE session_id = $1 
      ORDER BY timestamp DESC 
      LIMIT $2
    `, [sessionId, limit]);
    
    return result.rows.reverse(); // Return in chronological order
  }

  async storeConversationMessage(messageData) {
    await this.pgClient.query(`
      INSERT INTO conversation_history (
        session_id, user_message, system_response, timestamp, 
        documents_referenced, confidence_score, token_usage
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      messageData.sessionId,
      messageData.userId === 'system' ? '' : messageData.message,
      messageData.userId !== 'system' ? messageData.message : '',
      messageData.timestamp,
      JSON.stringify(messageData.metadata || {}),
      messageData.confidence || 0.8,
      JSON.stringify({ tokens: messageData.message.length })
    ]);
  }

  async getDocumentStatus(jobId) {
    const result = await this.pgClient.query(`
      SELECT * FROM document_processing_jobs 
      WHERE job_id = $1
    `, [jobId]);
    
    return result.rows[0] || null;
  }

  async getDocumentInfo(jobId) {
    const result = await this.pgClient.query(`
      SELECT output_filename, output_path, processing_status 
      FROM document_processing_jobs 
      WHERE job_id = $1 AND processing_status = 'completed'
    `, [jobId]);
    
    return result.rows[0] || null;
  }

  async storeFeedback(feedbackData) {
    await this.pgClient.query(`
      INSERT INTO user_feedback (
        session_id, job_id, feedback_type, feedback_value, rating, feedback_timestamp
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      feedbackData.sessionId,
      feedbackData.jobId,
      feedbackData.feedbackType,
      feedbackData.comments,
      feedbackData.rating,
      feedbackData.timestamp
    ]);
  }

  async getSystemAnalytics() {
    // Get various system metrics
    const [sessionStats, documentStats, qualityStats] = await Promise.all([
      this.pgClient.query(`
        SELECT COUNT(*) as total_sessions,
               COUNT(CASE WHEN last_activity > NOW() - INTERVAL '24 hours' THEN 1 END) as active_24h
        FROM conversation_sessions
      `),
      this.pgClient.query(`
        SELECT processing_status, COUNT(*) as count
        FROM document_processing_jobs
        WHERE processing_start > NOW() - INTERVAL '7 days'
        GROUP BY processing_status
      `),
      this.pgClient.query(`
        SELECT AVG(overall_quality_score) as avg_quality,
               COUNT(*) as total_reviews
        FROM quality_control_reviews
        WHERE review_timestamp > NOW() - INTERVAL '7 days'
      `)
    ]);

    return {
      sessions: sessionStats.rows[0],
      documents: documentStats.rows,
      quality: qualityStats.rows[0],
      timestamp: new Date().toISOString()
    };
  }

  // Utility methods

  estimateCompletionTime(taskType, tokenTier) {
    const baseTime = {
      'generate_whitepaper': 180, // 3 minutes
      'edit': 60,                 // 1 minute
      'combine': 90,              // 1.5 minutes
      'generate_policy_brief': 120 // 2 minutes
    };

    const tierMultiplier = {
      1: 0.8,  // 5K tokens - faster
      2: 1.0,  // 10K tokens - baseline
      3: 1.5,  // 25K tokens - slower
      4: 2.2   // 50K tokens - much slower
    };

    const base = baseTime[taskType] || 120;
    const multiplier = tierMultiplier[tokenTier] || 1.0;
    
    return Math.round(base * multiplier);
  }

  getContentType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const contentTypes = {
      '.pdf': 'application/pdf',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.md': 'text/markdown',
      '.html': 'text/html',
      '.txt': 'text/plain'
    };
    return contentTypes[ext] || 'application/octet-stream';
  }

  async start() {
    await this.initialize();
    
    this.server.listen(this.port, () => {
      console.log(`Political Chat Interface running on port ${this.port}`);
      console.log(`Health check: http://localhost:${this.port}/health`);
    });
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    this.server.close();
  }
}

// Start the server
const chatInterface = new ChatInterface();

process.on('SIGINT', async () => {
  console.log('Shutting down Chat Interface...');
  await chatInterface.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Chat Interface...');
  await chatInterface.stop();
  process.exit(0);
});

chatInterface.start().catch(console.error);