const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
const axios = require('axios');
const cheerio = require('cheerio');
const puppeteer = require('puppeteer');
const natural = require('natural');
const compromise = require('compromise');
const sentiment = require('sentiment');
const OpenAI = require('openai');
const Anthropic = require('anthropic');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const redis = require('redis');
const { Pool } = require('pg');
const Database = require('sqlite3').Database;
const NodeCache = require('node-cache');
const http = require('http');
const socketIo = require('socket.io');
const RSSParser = require('rss-parser');
const { isAfter, subDays, parseISO } = require('date-fns');
const uuid = require('uuid').v4;
const _ = require('lodash');
const validator = require('validator');
const urlParse = require('url-parse');
const client = require('prom-client');
const promMiddleware = require('express-prometheus-middleware');

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Initialize AI clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
const googleAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);

// Initialize databases and cache
const redisClient = redis.createClient({
  host: process.env.REDIS_HOST || 'redis',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD
});

const pgPool = new Pool({
  host: process.env.POSTGRES_HOST || 'postgresql',
  port: process.env.POSTGRES_PORT || 5432,
  database: process.env.POSTGRES_DB || 'political_conversations',
  user: process.env.POSTGRES_USER || 'n8n_user',
  password: process.env.POSTGRES_PASSWORD
});

const db = new Database('/app/data/fact_checking.db');
const memoryCache = new NodeCache({ stdTTL: 3600, checkperiod: 600 });

// Initialize Express and Socket.IO
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5678'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

const port = process.env.MCP_SERVER_PORT || 8094;

// Prometheus metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });

const factCheckCounter = new client.Counter({
  name: 'fact_checks_total',
  help: 'Total number of fact checks performed',
  labelNames: ['type', 'status', 'verdict']
});

const factCheckDuration = new client.Histogram({
  name: 'fact_check_duration_seconds',
  help: 'Duration of fact checking operations',
  labelNames: ['type'],
  buckets: [0.5, 1, 2, 5, 10, 30, 60, 120]
});

const sourceValidationCounter = new client.Counter({
  name: 'source_validations_total',
  help: 'Total source validation attempts',
  labelNames: ['source_type', 'credibility_score']
});

const realTimeFactChecksGauge = new client.Gauge({
  name: 'realtime_fact_checks_active',
  help: 'Number of active real-time fact checking sessions'
});

register.registerMetric(factCheckCounter);
register.registerMetric(factCheckDuration);
register.registerMetric(sourceValidationCounter);
register.registerMetric(realTimeFactChecksGauge);

// Trusted source configuration
const trustedSources = {
  tier1: [
    'reuters.com', 'apnews.com', 'bbc.com', 'npr.org', 'pbs.org',
    'factcheck.org', 'snopes.com', 'politifact.com', 'washington-post.com'
  ],
  tier2: [
    'cnn.com', 'nytimes.com', 'wsj.com', 'theguardian.com', 'economist.com',
    'axios.com', 'bloomberg.com', 'cbsnews.com', 'abcnews.go.com'
  ],
  tier3: [
    'usa-today.com', 'time.com', 'newsweek.com', 'thehill.com', 'politico.com'
  ],
  government: [
    'gov', 'congress.gov', 'senate.gov', 'house.gov', 'whitehouse.gov',
    'supremecourt.gov', 'cbo.gov', 'gao.gov', 'census.gov'
  ],
  academic: [
    'edu', 'org', 'ac.uk', 'scholar.google.com', 'jstor.org', 'pubmed.ncbi.nlm.nih.gov'
  ]
};

// Unreliable source patterns
const unreliableSources = [
  'infowars.com', 'breitbart.com', 'dailymail.co.uk', 'rt.com',
  'sputniknews.com', 'zerohedge.com', 'naturalnews.com'
];

// Express middleware setup
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5678'],
  credentials: true
}));

app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 200,
  message: { error: 'Too many fact checking requests' }
}));

app.use(express.json({ limit: '5mb' }));
app.use(winston.expressLogger);

app.use(promMiddleware({
  metricsPath: '/metrics',
  collectDefaultMetrics: true
}));

// Core fact-checking engine
class AutonomousFactChecker {
  constructor() {
    this.activeSessions = new Map();
    this.rssParser = new RSSParser();
    this.browser = null;
    this.initializeBrowser();
  }

  async initializeBrowser() {
    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      logger.info('Puppeteer browser initialized for fact checking');
    } catch (error) {
      logger.error('Failed to initialize browser:', error);
    }
  }

  async checkFactClaim(claim, options = {}) {
    const timer = factCheckDuration.startTimer({ type: 'claim_verification' });
    const factCheckId = uuid();
    
    try {
      logger.info(`Starting fact check for claim: ${claim.substring(0, 100)}...`);

      // Extract entities and key facts from the claim
      const extractedFacts = await this.extractFactualClaims(claim);
      
      // Perform multi-source verification
      const verificationResults = await Promise.all([
        this.verifyWithTrustedSources(extractedFacts),
        this.crossReferenceWithAPIs(extractedFacts),
        this.verifyWithAI(claim, extractedFacts),
        this.checkRecentNews(extractedFacts)
      ]);

      // Synthesize results
      const finalVerdict = await this.synthesizeVerification(claim, verificationResults);
      
      // Store result
      await this.storeFactCheckResult(factCheckId, claim, finalVerdict);

      factCheckCounter.inc({ 
        type: 'claim_verification', 
        status: 'success', 
        verdict: finalVerdict.verdict 
      });
      
      timer();

      return {
        id: factCheckId,
        claim,
        verdict: finalVerdict.verdict,
        confidence: finalVerdict.confidence,
        evidence: finalVerdict.evidence,
        sources: finalVerdict.sources,
        reasoning: finalVerdict.reasoning,
        timestamp: new Date().toISOString(),
        processingTime: Date.now() - timer.startTime
      };

    } catch (error) {
      factCheckCounter.inc({ 
        type: 'claim_verification', 
        status: 'error', 
        verdict: 'unknown' 
      });
      timer();
      throw error;
    }
  }

  async extractFactualClaims(text) {
    const doc = compromise(text);
    
    // Extract named entities
    const entities = {
      people: doc.people().out('array'),
      places: doc.places().out('array'),
      organizations: doc.organizations().out('array'),
      dates: doc.dates().out('array'),
      numbers: doc.values().out('array')
    };

    // Extract potential factual statements using AI
    const aiAnalysis = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a fact-checking specialist. Extract specific, verifiable factual claims from the given text. Focus on claims that can be checked against reliable sources.'
        },
        {
          role: 'user',
          content: `Text: "${text}"\n\nExtract the key factual claims that can be verified. For each claim, provide:\n1. The specific claim\n2. What type of evidence would verify it\n3. Potential sources to check`
        }
      ],
      max_tokens: 1000,
      temperature: 0.2
    });

    const factualClaims = aiAnalysis.choices[0].message.content;

    return {
      entities,
      extractedClaims: factualClaims,
      originalText: text
    };
  }

  async verifyWithTrustedSources(extractedFacts) {
    const searchQueries = this.generateSearchQueries(extractedFacts);
    const sourceResults = [];

    for (const query of searchQueries.slice(0, 3)) { // Limit to 3 queries
      try {
        // Search trusted news sources
        const newsResults = await this.searchTrustedNews(query);
        sourceResults.push(...newsResults);

        // Search government sources if relevant
        if (this.isGovernmentRelated(query)) {
          const govResults = await this.searchGovernmentSources(query);
          sourceResults.push(...govResults);
        }

        // Search academic sources for research claims
        if (this.isResearchRelated(query)) {
          const academicResults = await this.searchAcademicSources(query);
          sourceResults.push(...academicResults);
        }

      } catch (error) {
        logger.error(`Error searching for query "${query}":`, error);
      }
    }

    return {
      type: 'trusted_sources',
      results: sourceResults,
      confidence: this.calculateSourceConfidence(sourceResults)
    };
  }

  generateSearchQueries(extractedFacts) {
    const queries = [];

    // Generate queries from entities
    if (extractedFacts.entities.people.length > 0) {
      queries.push(`${extractedFacts.entities.people[0]} political statement`);
    }

    if (extractedFacts.entities.organizations.length > 0) {
      queries.push(`${extractedFacts.entities.organizations[0]} official position`);
    }

    // Generate queries from key phrases
    const sentences = extractedFacts.originalText.split('.').slice(0, 2);
    sentences.forEach(sentence => {
      if (sentence.length > 10) {
        queries.push(sentence.trim());
      }
    });

    return queries.filter(q => q.length > 5);
  }

  async searchTrustedNews(query) {
    const results = [];
    
    try {
      // Use a news API or web scraping for trusted sources
      const searchUrl = `https://newsapi.org/v2/everything?q=${encodeURIComponent(query)}&sources=reuters,associated-press,bbc-news&apiKey=${process.env.NEWS_API_KEY}`;
      
      if (process.env.NEWS_API_KEY) {
        const response = await axios.get(searchUrl);
        
        response.data.articles?.forEach(article => {
          results.push({
            url: article.url,
            title: article.title,
            content: article.description,
            source: article.source.name,
            publishedAt: article.publishedAt,
            credibilityScore: this.calculateSourceCredibility(article.source.name)
          });
        });
      } else {
        // Fallback to direct search of trusted sources
        const trustedUrls = trustedSources.tier1.slice(0, 2);
        for (const domain of trustedUrls) {
          const searchResults = await this.searchWebsite(domain, query);
          results.push(...searchResults);
        }
      }

    } catch (error) {
      logger.error('Error searching trusted news:', error);
    }

    return results;
  }

  async searchWebsite(domain, query) {
    const results = [];
    
    try {
      if (this.browser) {
        const page = await this.browser.newPage();
        await page.goto(`https://${domain}/search?q=${encodeURIComponent(query)}`, {
          waitUntil: 'networkidle0',
          timeout: 10000
        });

        // Extract search results (this would need to be customized per site)
        const searchResults = await page.evaluate(() => {
          const links = Array.from(document.querySelectorAll('a[href*="/"]'));
          return links.slice(0, 3).map(link => ({
            url: link.href,
            title: link.textContent?.trim() || '',
            content: link.parentElement?.textContent?.trim() || ''
          }));
        });

        results.push(...searchResults.map(result => ({
          ...result,
          source: domain,
          credibilityScore: this.calculateSourceCredibility(domain)
        })));

        await page.close();
      }
    } catch (error) {
      logger.error(`Error searching ${domain}:`, error);
    }

    return results;
  }

  calculateSourceCredibility(source) {
    const domain = typeof source === 'string' ? source.toLowerCase() : '';
    
    if (trustedSources.tier1.some(t => domain.includes(t))) return 0.95;
    if (trustedSources.tier2.some(t => domain.includes(t))) return 0.85;
    if (trustedSources.tier3.some(t => domain.includes(t))) return 0.75;
    if (trustedSources.government.some(t => domain.includes(t))) return 0.90;
    if (trustedSources.academic.some(t => domain.includes(t))) return 0.88;
    if (unreliableSources.some(u => domain.includes(u))) return 0.20;
    
    return 0.50; // Default for unknown sources
  }

  async crossReferenceWithAPIs(extractedFacts) {
    const apiResults = [];

    try {
      // Google Fact Check API (if available)
      if (process.env.GOOGLE_FACT_CHECK_API_KEY) {
        const factCheckResults = await this.queryGoogleFactCheck(extractedFacts.originalText);
        apiResults.push(...factCheckResults);
      }

      // Fact-checking organization APIs
      const factCheckOrgResults = await this.queryFactCheckOrgs(extractedFacts);
      apiResults.push(...factCheckOrgResults);

    } catch (error) {
      logger.error('Error cross-referencing with APIs:', error);
    }

    return {
      type: 'api_verification',
      results: apiResults,
      confidence: apiResults.length > 0 ? 0.85 : 0.0
    };
  }

  async queryGoogleFactCheck(claim) {
    const results = [];
    
    try {
      const apiUrl = `https://factchecktools.googleapis.com/v1alpha1/claims:search?query=${encodeURIComponent(claim)}&key=${process.env.GOOGLE_FACT_CHECK_API_KEY}`;
      const response = await axios.get(apiUrl);
      
      response.data.claims?.forEach(claim => {
        results.push({
          claim: claim.text,
          claimant: claim.claimant,
          claimDate: claim.claimDate,
          claimReview: claim.claimReview?.[0],
          credibilityScore: 0.90
        });
      });

    } catch (error) {
      logger.error('Error querying Google Fact Check API:', error);
    }

    return results;
  }

  async verifyWithAI(claim, extractedFacts) {
    try {
      // Use multiple AI models for cross-verification
      const [gptAnalysis, claudeAnalysis, geminiAnalysis] = await Promise.all([
        this.analyzeWithGPT(claim, extractedFacts),
        this.analyzeWithClaude(claim, extractedFacts),
        this.analyzeWithGemini(claim, extractedFacts)
      ]);

      return {
        type: 'ai_verification',
        results: [gptAnalysis, claudeAnalysis, geminiAnalysis],
        confidence: this.calculateAIConsensus([gptAnalysis, claudeAnalysis, geminiAnalysis])
      };

    } catch (error) {
      logger.error('Error in AI verification:', error);
      return { type: 'ai_verification', results: [], confidence: 0.0 };
    }
  }

  async analyzeWithGPT(claim, extractedFacts) {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a fact-checking expert. Analyze claims for factual accuracy, provide likelihood assessments, and suggest verification approaches. Be precise and cite reasoning.'
        },
        {
          role: 'user',
          content: `Claim: "${claim}"\n\nExtracted facts: ${JSON.stringify(extractedFacts, null, 2)}\n\nAssess:\n1. Likelihood this claim is true (0-100%)\n2. Key evidence needed\n3. Red flags or inconsistencies\n4. Confidence in assessment`
        }
      ],
      max_tokens: 800,
      temperature: 0.1
    });

    return {
      model: 'gpt-4',
      analysis: response.choices[0].message.content,
      confidence: this.extractConfidenceFromText(response.choices[0].message.content)
    };
  }

  async analyzeWithClaude(claim, extractedFacts) {
    const response = await anthropic.messages.create({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 800,
      messages: [
        {
          role: 'user',
          content: `As a fact-checking specialist, analyze this claim for accuracy:\n\nClaim: "${claim}"\n\nExtracted facts: ${JSON.stringify(extractedFacts, null, 2)}\n\nProvide:\n1. Truth assessment (likely true/false/mixed/uncertain)\n2. Supporting evidence or contradictions\n3. Reliability confidence (1-10)\n4. Verification strategy`
        }
      ]
    });

    return {
      model: 'claude-3-sonnet',
      analysis: response.content[0].text,
      confidence: this.extractConfidenceFromText(response.content[0].text)
    };
  }

  async analyzeWithGemini(claim, extractedFacts) {
    const model = googleAI.getGenerativeModel({ model: 'gemini-pro' });
    const prompt = `Fact-check this political claim:\n\nClaim: "${claim}"\n\nContext: ${JSON.stringify(extractedFacts, null, 2)}\n\nProvide analysis with truth likelihood, evidence assessment, and confidence level.`;
    
    const result = await model.generateContent(prompt);
    const response = result.response.text();

    return {
      model: 'gemini-pro',
      analysis: response,
      confidence: this.extractConfidenceFromText(response)
    };
  }

  extractConfidenceFromText(text) {
    // Extract confidence scores from AI responses
    const confidenceRegex = /(\d+)%|(\d\.\d+)|confidence[:\s]+(\d+)/gi;
    const matches = text.match(confidenceRegex);
    
    if (matches && matches.length > 0) {
      const numbers = matches.map(m => parseFloat(m.replace(/[^\d.]/g, '')));
      return Math.max(...numbers) / 100;
    }
    
    // Fallback based on language confidence indicators
    if (text.toLowerCase().includes('very confident') || text.toLowerCase().includes('highly likely')) return 0.9;
    if (text.toLowerCase().includes('confident') || text.toLowerCase().includes('likely')) return 0.75;
    if (text.toLowerCase().includes('uncertain') || text.toLowerCase().includes('unclear')) return 0.4;
    if (text.toLowerCase().includes('unlikely') || text.toLowerCase().includes('doubtful')) return 0.25;
    
    return 0.6; // Default moderate confidence
  }

  calculateAIConsensus(analyses) {
    const confidences = analyses.map(a => a.confidence);
    const avgConfidence = confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length;
    
    // Boost confidence if models agree
    const confidenceVariance = this.calculateVariance(confidences);
    const consensusBonus = confidenceVariance < 0.1 ? 0.1 : 0;
    
    return Math.min(avgConfidence + consensusBonus, 1.0);
  }

  calculateVariance(numbers) {
    const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
    const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
  }

  async synthesizeVerification(claim, verificationResults) {
    // Weighted synthesis of all verification results
    const weights = {
      trusted_sources: 0.4,
      api_verification: 0.3,
      ai_verification: 0.2,
      recent_news: 0.1
    };

    let totalConfidence = 0;
    let totalWeight = 0;
    const evidence = [];

    verificationResults.forEach(result => {
      if (result && result.confidence > 0) {
        const weight = weights[result.type] || 0.1;
        totalConfidence += result.confidence * weight;
        totalWeight += weight;
        
        evidence.push({
          type: result.type,
          confidence: result.confidence,
          results: result.results?.slice(0, 3) // Limit evidence items
        });
      }
    });

    const finalConfidence = totalWeight > 0 ? totalConfidence / totalWeight : 0.5;

    // Determine verdict based on confidence and evidence
    let verdict;
    if (finalConfidence >= 0.8) {
      verdict = 'TRUE';
    } else if (finalConfidence >= 0.6) {
      verdict = 'MOSTLY_TRUE';
    } else if (finalConfidence >= 0.4) {
      verdict = 'MIXED';
    } else if (finalConfidence >= 0.2) {
      verdict = 'MOSTLY_FALSE';
    } else {
      verdict = 'FALSE';
    }

    // Generate reasoning with AI
    const reasoning = await this.generateReasoning(claim, evidence, verdict, finalConfidence);

    return {
      verdict,
      confidence: finalConfidence,
      evidence,
      reasoning,
      sources: this.extractUniqueSources(evidence),
      verificationDate: new Date().toISOString()
    };
  }

  async generateReasoning(claim, evidence, verdict, confidence) {
    try {
      const evidenceSummary = evidence.map(e => `${e.type}: ${e.confidence.toFixed(2)} confidence`).join(', ');
      
      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'Generate clear, concise reasoning for fact-check verdicts. Explain the evidence and methodology used.'
          },
          {
            role: 'user',
            content: `Claim: "${claim}"\nVerdict: ${verdict}\nConfidence: ${confidence.toFixed(2)}\nEvidence: ${evidenceSummary}\n\nProvide a 2-3 sentence explanation of the fact-check reasoning.`
          }
        ],
        max_tokens: 200,
        temperature: 0.2
      });

      return response.choices[0].message.content;
    } catch (error) {
      return `Fact-check verdict: ${verdict} (${Math.round(confidence * 100)}% confidence) based on analysis of multiple sources and verification methods.`;
    }
  }

  extractUniqueSources(evidence) {
    const sources = new Set();
    
    evidence.forEach(e => {
      if (e.results && Array.isArray(e.results)) {
        e.results.forEach(result => {
          if (result.source) sources.add(result.source);
          if (result.url) sources.add(result.url);
        });
      }
    });

    return Array.from(sources).slice(0, 10); // Limit to 10 sources
  }

  // Additional helper methods...
  isGovernmentRelated(query) {
    return /government|policy|law|regulation|congress|senate|administration/i.test(query);
  }

  isResearchRelated(query) {
    return /study|research|data|statistics|report|analysis/i.test(query);
  }

  calculateSourceConfidence(results) {
    if (results.length === 0) return 0;
    const avgCredibility = results.reduce((sum, result) => sum + result.credibilityScore, 0) / results.length;
    return avgCredibility;
  }

  async storeFactCheckResult(id, claim, verdict) {
    try {
      const query = `
        INSERT INTO fact_check_results 
        (id, claim, verdict, confidence, evidence, sources, reasoning, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      `;
      
      await pgPool.query(query, [
        id,
        claim,
        verdict.verdict,
        verdict.confidence,
        JSON.stringify(verdict.evidence),
        JSON.stringify(verdict.sources),
        verdict.reasoning
      ]);
    } catch (error) {
      logger.error('Failed to store fact check result:', error);
    }
  }
}

// Initialize fact checker
const factChecker = new AutonomousFactChecker();

// MCP Tools
const tools = [
  {
    name: "fact_check_claim",
    description: "Perform comprehensive fact-checking on a political claim with multi-source verification",
    inputSchema: {
      type: "object",
      properties: {
        claim: {
          type: "string",
          description: "The claim to fact-check"
        },
        priority: {
          type: "string",
          enum: ["low", "medium", "high"],
          description: "Priority level for fact-checking"
        },
        includeRealTimeNews: {
          type: "boolean",
          description: "Include real-time news verification",
          default: true
        }
      },
      required: ["claim"]
    }
  },
  {
    name: "batch_fact_check",
    description: "Fact-check multiple claims in batch for efficiency",
    inputSchema: {
      type: "object",
      properties: {
        claims: {
          type: "array",
          items: { type: "string" },
          description: "Array of claims to fact-check"
        },
        maxConcurrent: {
          type: "number",
          description: "Maximum concurrent fact checks",
          default: 3
        }
      },
      required: ["claims"]
    }
  },
  {
    name: "verify_source_credibility",
    description: "Assess the credibility and reliability of information sources",
    inputSchema: {
      type: "object",
      properties: {
        source: {
          type: "string",
          description: "Source URL or domain to verify"
        },
        includeHistoricalAnalysis: {
          type: "boolean",
          description: "Include historical accuracy analysis",
          default: false
        }
      },
      required: ["source"]
    }
  },
  {
    name: "get_fact_check_history",
    description: "Retrieve historical fact-checking results with filtering",
    inputSchema: {
      type: "object",
      properties: {
        verdict: {
          type: "string",
          enum: ["TRUE", "MOSTLY_TRUE", "MIXED", "MOSTLY_FALSE", "FALSE"],
          description: "Filter by verdict"
        },
        minConfidence: {
          type: "number",
          description: "Minimum confidence threshold"
        },
        dateFrom: {
          type: "string",
          description: "Start date for filtering"
        },
        limit: {
          type: "number",
          description: "Maximum results to return",
          default: 50
        }
      }
    }
  },
  {
    name: "real_time_monitoring",
    description: "Start real-time monitoring of claims and statements for immediate fact-checking",
    inputSchema: {
      type: "object",
      properties: {
        sources: {
          type: "array",
          items: { type: "string" },
          description: "Sources to monitor (RSS feeds, Twitter handles, etc.)"
        },
        keywords: {
          type: "array",
          items: { type: "string" },
          description: "Keywords to trigger fact-checking"
        },
        webhookUrl: {
          type: "string",
          description: "Webhook URL for real-time notifications"
        }
      }
    }
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    service: 'autonomous-fact-checking-mcp',
    features: {
      multiSourceVerification: true,
      realTimeMonitoring: true,
      aiConsensus: true,
      sourceCredibilityAnalysis: true
    }
  });
});

app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
});

// MCP tool endpoint
app.post('/mcp/call', async (req, res) => {
  try {
    const { tool, arguments: args } = req.body;
    
    if (!tools.find(t => t.name === tool)) {
      return res.status(400).json({ error: 'Unknown tool' });
    }
    
    let result;
    
    switch (tool) {
      case 'fact_check_claim':
        result = await factChecker.checkFactClaim(args.claim, args);
        break;
      case 'batch_fact_check':
        result = await handleBatchFactCheck(args);
        break;
      case 'verify_source_credibility':
        result = await handleVerifySourceCredibility(args);
        break;
      case 'get_fact_check_history':
        result = await handleGetFactCheckHistory(args);
        break;
      case 'real_time_monitoring':
        result = await handleRealTimeMonitoring(args);
        break;
      default:
        return res.status(400).json({ error: 'Tool not implemented' });
    }
    
    res.json({ result });
    
  } catch (error) {
    logger.error('MCP tool error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Tool handlers
async function handleBatchFactCheck(args) {
  const { claims, maxConcurrent = 3 } = args;
  const results = [];
  
  for (let i = 0; i < claims.length; i += maxConcurrent) {
    const batch = claims.slice(i, i + maxConcurrent);
    const batchPromises = batch.map(claim => factChecker.checkFactClaim(claim));
    const batchResults = await Promise.allSettled(batchPromises);
    
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        results.push({
          claim: batch[index],
          error: result.reason.message,
          verdict: 'ERROR'
        });
      }
    });
  }
  
  return {
    totalClaims: claims.length,
    successfulChecks: results.filter(r => r.verdict !== 'ERROR').length,
    results
  };
}

async function handleVerifySourceCredibility(args) {
  const { source } = args;
  const credibilityScore = factChecker.calculateSourceCredibility(source);
  
  sourceValidationCounter.inc({ 
    source_type: 'unknown', 
    credibility_score: Math.round(credibilityScore * 10) / 10 
  });
  
  return {
    source,
    credibilityScore,
    tier: credibilityScore >= 0.9 ? 'tier1' : credibilityScore >= 0.8 ? 'tier2' : credibilityScore >= 0.7 ? 'tier3' : 'unverified',
    recommendation: credibilityScore >= 0.7 ? 'trusted' : credibilityScore >= 0.5 ? 'caution' : 'unreliable'
  };
}

async function handleGetFactCheckHistory(args) {
  const { verdict, minConfidence, dateFrom, limit = 50 } = args;
  
  let query = 'SELECT * FROM fact_check_results WHERE 1=1';
  const queryParams = [];
  
  if (verdict) {
    queryParams.push(verdict);
    query += ` AND verdict = $${queryParams.length}`;
  }
  
  if (minConfidence !== undefined) {
    queryParams.push(minConfidence);
    query += ` AND confidence >= $${queryParams.length}`;
  }
  
  if (dateFrom) {
    queryParams.push(dateFrom);
    query += ` AND created_at >= $${queryParams.length}`;
  }
  
  queryParams.push(limit);
  query += ` ORDER BY created_at DESC LIMIT $${queryParams.length}`;
  
  const result = await pgPool.query(query, queryParams);
  
  return {
    totalResults: result.rows.length,
    results: result.rows
  };
}

async function handleRealTimeMonitoring(args) {
  const sessionId = uuid();
  // Implementation would set up real-time monitoring
  // This is a simplified version
  
  realTimeFactChecksGauge.inc();
  
  return {
    sessionId,
    status: 'monitoring_started',
    message: 'Real-time fact-checking monitoring activated'
  };
}

// Initialize database schema
async function initializeDatabase() {
  try {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS fact_check_results (
        id UUID PRIMARY KEY,
        claim TEXT NOT NULL,
        verdict VARCHAR(20) NOT NULL,
        confidence FLOAT NOT NULL,
        evidence JSONB,
        sources JSONB,
        reasoning TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      );
      
      CREATE INDEX IF NOT EXISTS idx_fact_check_verdict ON fact_check_results(verdict);
      CREATE INDEX IF NOT EXISTS idx_fact_check_confidence ON fact_check_results(confidence);
      CREATE INDEX IF NOT EXISTS idx_fact_check_created_at ON fact_check_results(created_at);
    `;
    
    await pgPool.query(createTableQuery);
    logger.info('Fact checking database schema initialized');
  } catch (error) {
    logger.error('Failed to initialize database:', error);
    throw error;
  }
}

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
async function startServer() {
  try {
    await redisClient.connect();
    await initializeDatabase();
    
    server.listen(port, '0.0.0.0', () => {
      logger.info(`Autonomous Fact-Checking MCP Server running on port ${port}`);
      logger.info('Features: Multi-source verification, AI consensus, Real-time monitoring');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();