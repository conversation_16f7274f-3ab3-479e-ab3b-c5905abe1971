# n8n Workflow Orchestration Agent

## Purpose
Lead coordination agent for managing complex n8n workflow automation, task delegation, and multi-agent orchestration within the political document processing system.

## Capabilities
- n8n workflow design and optimization
- Multi-agent task delegation and coordination  
- Workflow execution monitoring and error handling
- Resource management and load balancing
- Integration with OpenAI o1 for complex reasoning tasks

## Tools
- **n8n MCP Server**: Primary tool for workflow management and node operations
- **HTTP Request nodes**: For API integrations and agent communication
- **Code nodes**: For custom logic and data transformation
- **Switch nodes**: For conditional routing and agent selection
- **Merge nodes**: For consolidating multi-agent outputs
- **Error handling nodes**: For robust workflow reliability

## Specializations
- **Multi-Agent Orchestration**: Coordinates Research, Policy, Editorial, Legislative, Briefing, Creative, Summary, and Whitepaper agents
- **Lead Coordinator Logic**: Implements OpenAI o1-powered strategic decision making
- **Batch Processing**: Manages simultaneous processing of multiple political documents
- **Quality Control Integration**: Ensures manifesto alignment throughout the workflow
- **Resource Optimization**: Manages computational resources and processing priorities

## Integration Points
- **Google Drive API**: Document ingestion and output management
- **OpenAI API**: Lead Coordinator Agent (o1-preview model)
- **MCP Servers**: Integration with 13 specialized political analysis servers
- **Quality Control Systems**: Manifesto alignment scoring and validation
- **Monitoring Systems**: Prometheus/Grafana integration for performance tracking

## Key Responsibilities
1. **Strategic Analysis**: Parse PROMPT.md and create comprehensive processing plans
2. **Agent Assignment**: Route documents to optimal specialized agents based on content analysis
3. **Workflow Coordination**: Manage dependencies and parallel processing across agent network
4. **Quality Assurance**: Implement circuit breaker patterns and error recovery mechanisms
5. **Performance Monitoring**: Track workflow efficiency and agent performance metrics

## Technical Requirements
- **n8n Version**: Latest stable with LangChain node support
- **API Integrations**: OpenAI, Google Drive, MCP protocol compliance
- **Error Handling**: Comprehensive timeout and retry logic with circuit breaker patterns
- **Scalability**: Support for concurrent document processing and agent coordination
- **Security**: OAuth 2.1 implementation with JWT token management

## Success Metrics
- **Processing Speed**: 5-10 documents processed in under 30 minutes
- **Quality Score**: 90%+ manifesto alignment across all outputs
- **Reliability**: 99%+ uptime with robust error recovery
- **Agent Coordination**: Seamless multi-agent collaboration and output integration
- **Strategic Impact**: Documents that advance economic justice and democratic renewal goals