FROM node:18-alpine

# Install Python and dependencies
RUN apk update && apk upgrade && apk add --no-cache \
    python3 \
    py3-pip \
    curl \
    dumb-init \
    make \
    g++ \
    sqlite \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/src

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcpuser -u 1001 -G nodejs

# Copy package files
COPY package*.json requirements.txt ./

# Install Node.js dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Install Python dependencies
RUN pip3 install -r requirements.txt

# Copy application code
COPY . .

# Set proper permissions
RUN chown -R mcpuser:nodejs /app

# Switch to non-root user
USER mcpuser

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8093/health || exit 1

# Expose port
EXPOSE 8093

# Start the application
CMD ["node", "server.js"]