-- Legal Analysis MCP Server Database Initialization
-- This file sets up the initial database schema and indexes

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types
CREATE TYPE compliance_status_type AS ENUM ('compliant', 'issues_identified', 'non_compliant', 'pending_review');
CREATE TYPE risk_level_type AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE document_type_enum AS ENUM ('legislation', 'regulation', 'policy', 'executive_order', 'judicial_opinion', 'contract', 'memo', 'other');

-- Legal document analysis table
CREATE TABLE IF NOT EXISTS legal_analysis (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255) UNIQUE NOT NULL,
    filename VARCHAR(255),
    document_type document_type_enum DEFAULT 'other',
    content_hash VARCHAR(64) NOT NULL,
    analysis_result JSONB,
    constitutional_issues JSONB,
    legal_risks JSONB,
    compliance_score DECIMAL(3,2) CHECK (compliance_score >= 0 AND compliance_score <= 10),
    word_count INTEGER,
    character_count INTEGER,
    token_count INTEGER,
    complexity_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analyzed_by VARCHAR(100),
    analysis_version VARCHAR(20) DEFAULT '1.0'
);

-- Legal precedents and citations
CREATE TABLE IF NOT EXISTS legal_precedents (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    citation VARCHAR(500) NOT NULL,
    case_name VARCHAR(500),
    court VARCHAR(200),
    year INTEGER CHECK (year > 1700 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 10),
    relevance_score DECIMAL(3,2) CHECK (relevance_score >= 0 AND relevance_score <= 1),
    legal_principle TEXT,
    jurisdiction VARCHAR(100),
    court_level VARCHAR(50),
    precedent_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Constitutional compliance tracking
CREATE TABLE IF NOT EXISTS constitutional_compliance (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    amendment_number INTEGER CHECK (amendment_number >= 1 AND amendment_number <= 27),
    article_section VARCHAR(50),
    provision_text TEXT,
    compliance_status compliance_status_type DEFAULT 'pending_review',
    issues_identified TEXT[],
    recommendations TEXT[],
    severity_level risk_level_type DEFAULT 'medium',
    legal_basis TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP,
    reviewer VARCHAR(100),
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Regulatory impact assessments
CREATE TABLE IF NOT EXISTS regulatory_impact (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    regulation_type VARCHAR(100),
    impact_category VARCHAR(100),
    impact_level risk_level_type DEFAULT 'medium',
    affected_parties TEXT[],
    economic_impact JSONB,
    social_impact JSONB,
    environmental_impact JSONB,
    implementation_timeline TEXT,
    estimated_cost DECIMAL(15,2),
    estimated_benefits DECIMAL(15,2),
    stakeholder_feedback JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Legal risk assessments
CREATE TABLE IF NOT EXISTS legal_risk_assessment (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    risk_category VARCHAR(100) NOT NULL,
    risk_level risk_level_type DEFAULT 'medium',
    risk_description TEXT NOT NULL,
    mitigation_strategies TEXT[],
    probability DECIMAL(3,2) CHECK (probability >= 0 AND probability <= 1),
    impact_severity risk_level_type DEFAULT 'medium',
    risk_score DECIMAL(4,2),
    residual_risk_level risk_level_type,
    review_date DATE,
    status VARCHAR(50) DEFAULT 'identified',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Legal citations table
CREATE TABLE IF NOT EXISTS legal_citations (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    citation_text VARCHAR(1000) NOT NULL,
    citation_type VARCHAR(50),
    case_name VARCHAR(500),
    court VARCHAR(200),
    year INTEGER,
    volume VARCHAR(20),
    reporter VARCHAR(100),
    page_number VARCHAR(20),
    is_valid BOOLEAN DEFAULT TRUE,
    validation_notes TEXT,
    context_before TEXT,
    context_after TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Legal terminology tracking
CREATE TABLE IF NOT EXISTS legal_terminology (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    term VARCHAR(200) NOT NULL,
    definition TEXT,
    complexity_level VARCHAR(20),
    frequency INTEGER DEFAULT 1,
    context TEXT,
    plain_language_explanation TEXT,
    legal_area VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Document comparison results
CREATE TABLE IF NOT EXISTS document_comparisons (
    id SERIAL PRIMARY KEY,
    document_a_id VARCHAR(255),
    document_b_id VARCHAR(255),
    comparison_type VARCHAR(50),
    similarity_score DECIMAL(3,2) CHECK (similarity_score >= 0 AND similarity_score <= 1),
    differences_identified JSONB,
    conflicts_found JSONB,
    alignment_score DECIMAL(3,2),
    comparison_summary TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_a_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE,
    FOREIGN KEY (document_b_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Analysis audit trail
CREATE TABLE IF NOT EXISTS analysis_audit_trail (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(255),
    action VARCHAR(100) NOT NULL,
    user_id VARCHAR(100),
    details JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    FOREIGN KEY (document_id) REFERENCES legal_analysis(document_id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_legal_analysis_document_id ON legal_analysis(document_id);
CREATE INDEX idx_legal_analysis_document_type ON legal_analysis(document_type);
CREATE INDEX idx_legal_analysis_created_at ON legal_analysis(created_at);
CREATE INDEX idx_legal_analysis_compliance_score ON legal_analysis(compliance_score);

CREATE INDEX idx_legal_precedents_document_id ON legal_precedents(document_id);
CREATE INDEX idx_legal_precedents_year ON legal_precedents(year);
CREATE INDEX idx_legal_precedents_court ON legal_precedents(court);
CREATE INDEX idx_legal_precedents_relevance_score ON legal_precedents(relevance_score);

CREATE INDEX idx_constitutional_compliance_document_id ON constitutional_compliance(document_id);
CREATE INDEX idx_constitutional_compliance_amendment ON constitutional_compliance(amendment_number);
CREATE INDEX idx_constitutional_compliance_status ON constitutional_compliance(compliance_status);

CREATE INDEX idx_regulatory_impact_document_id ON regulatory_impact(document_id);
CREATE INDEX idx_regulatory_impact_level ON regulatory_impact(impact_level);
CREATE INDEX idx_regulatory_impact_category ON regulatory_impact(impact_category);

CREATE INDEX idx_legal_risk_document_id ON legal_risk_assessment(document_id);
CREATE INDEX idx_legal_risk_level ON legal_risk_assessment(risk_level);
CREATE INDEX idx_legal_risk_category ON legal_risk_assessment(risk_category);

CREATE INDEX idx_legal_citations_document_id ON legal_citations(document_id);
CREATE INDEX idx_legal_citations_type ON legal_citations(citation_type);
CREATE INDEX idx_legal_citations_year ON legal_citations(year);

CREATE INDEX idx_legal_terminology_document_id ON legal_terminology(document_id);
CREATE INDEX idx_legal_terminology_term ON legal_terminology(term);
CREATE INDEX idx_legal_terminology_complexity ON legal_terminology(complexity_level);

-- Full-text search indexes
CREATE INDEX idx_legal_analysis_gin ON legal_analysis USING GIN (analysis_result);
CREATE INDEX idx_constitutional_compliance_gin ON constitutional_compliance USING GIN (issues_identified);
CREATE INDEX idx_regulatory_impact_gin ON regulatory_impact USING GIN (economic_impact);

-- Trigram indexes for fuzzy text search
CREATE INDEX idx_legal_precedents_case_name_trgm ON legal_precedents USING GIN (case_name gin_trgm_ops);
CREATE INDEX idx_legal_citations_citation_trgm ON legal_citations USING GIN (citation_text gin_trgm_ops);
CREATE INDEX idx_legal_terminology_term_trgm ON legal_terminology USING GIN (term gin_trgm_ops);

-- Update trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create update triggers
CREATE TRIGGER update_legal_analysis_updated_at 
    BEFORE UPDATE ON legal_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for common queries
CREATE OR REPLACE VIEW legal_analysis_summary AS
SELECT 
    document_id,
    filename,
    document_type,
    compliance_score,
    complexity_score,
    word_count,
    created_at,
    CASE 
        WHEN compliance_score >= 8 THEN 'High Compliance'
        WHEN compliance_score >= 6 THEN 'Moderate Compliance'
        WHEN compliance_score >= 4 THEN 'Low Compliance'
        ELSE 'Non-Compliant'
    END as compliance_category
FROM legal_analysis;

CREATE OR REPLACE VIEW high_risk_documents AS
SELECT 
    la.document_id,
    la.filename,
    la.document_type,
    lra.risk_level,
    lra.risk_description,
    lra.probability,
    lra.impact_severity
FROM legal_analysis la
JOIN legal_risk_assessment lra ON la.document_id = lra.document_id
WHERE lra.risk_level IN ('high', 'critical');

-- Insert initial constitutional amendments reference data
INSERT INTO constitutional_compliance (document_id, amendment_number, provision_text, compliance_status) VALUES
('ref_amendment_1', 1, 'Congress shall make no law respecting an establishment of religion, or prohibiting the free exercise thereof; or abridging the freedom of speech, or of the press; or the right of the people peaceably to assemble, and to petition the Government for a redress of grievances.', 'compliant'),
('ref_amendment_4', 4, 'The right of the people to be secure in their persons, houses, papers, and effects, against unreasonable searches and seizures, shall not be violated, and no Warrants shall issue, but upon probable cause, supported by Oath or affirmation, and particularly describing the place to be searched, and the persons or things to be seized.', 'compliant'),
('ref_amendment_5', 5, 'No person shall be held to answer for a capital, or otherwise infamous crime, unless on a presentment or indictment of a Grand Jury, except in cases arising in the land or naval forces, or in the Militia, when in actual service in time of War or public danger; nor shall any person be subject for the same offence to be twice put in jeopardy of life or limb; nor shall be compelled in any criminal case to be a witness against himself, nor be deprived of life, liberty, or property, without due process of law; nor shall private property be taken for public use, without just compensation.', 'compliant'),
('ref_amendment_14', 14, 'All persons born or naturalized in the United States, and subject to the jurisdiction thereof, are citizens of the United States and of the State wherein they reside. No State shall make or enforce any law which shall abridge the privileges or immunities of citizens of the United States; nor shall any State deprive any person of life, liberty, or property, without due process of law; nor deny to any person within its jurisdiction the equal protection of the laws.', 'compliant')
ON CONFLICT (document_id) DO NOTHING;

-- Create function for calculating risk scores
CREATE OR REPLACE FUNCTION calculate_risk_score(probability DECIMAL, impact VARCHAR(50))
RETURNS DECIMAL AS $$
DECLARE
    impact_weight DECIMAL;
BEGIN
    CASE impact
        WHEN 'low' THEN impact_weight := 1;
        WHEN 'medium' THEN impact_weight := 2;
        WHEN 'high' THEN impact_weight := 3;
        WHEN 'critical' THEN impact_weight := 4;
        ELSE impact_weight := 2;
    END CASE;
    
    RETURN probability * impact_weight;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO n8n_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO n8n_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO n8n_user;