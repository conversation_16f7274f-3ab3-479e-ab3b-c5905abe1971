#!/usr/bin/env python3
"""
Autonomous Model Ensemble MCP Server
Integrates all components for political document processing
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import sys
import os

# Add src directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from political_document_orchestrator import (
    PoliticalDocumentOrchestrator, PoliticalDocumentTask, 
    TaskPriority, TaskStatus, AgentType
)
from model_ensemble_router import (
    ModelEnsembleRouter, RoutingRequest, TaskComplexity
)
from context_memory_manager import (
    ContextMemoryManager, MemoryType, Priority
)
from error_handling_system import (
    ErrorHandlingSystem, ErrorType, ErrorSeverity
)

# MCP imports
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource, Tool, TextContent, ImageContent, EmbeddedResource,
    LoggingLevel, CallToolResult, ListResourcesResult, ListToolsResult,
    ReadResourceResult
)
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AutonomousEnsembleMCPServer:
    """
    Main MCP server integrating all autonomous ensemble components
    """
    
    def __init__(self):
        self.server = Server("autonomous-ensemble")
        self.orchestrator = PoliticalDocumentOrchestrator()
        self.router = ModelEnsembleRouter()
        self.memory_manager = ContextMemoryManager()
        self.error_system = ErrorHandlingSystem()
        
        # Task tracking
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.task_counter = 0
        
        # Initialize MCP server handlers
        self._setup_tools()
        self._setup_resources()
        
        # Set up event handlers
        self._setup_event_handlers()
    
    def _setup_tools(self):
        """Setup MCP tools"""
        
        @self.server.list_tools()
        async def list_tools() -> ListToolsResult:
            return ListToolsResult(
                tools=[
                    Tool(
                        name="submit_analysis_task",
                        description="Submit a political document analysis task",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "task_type": {
                                    "type": "string",
                                    "enum": ["document_analysis", "research", "synthesis", "fact_check", "quality_control"],
                                    "description": "Type of analysis task"
                                },
                                "content": {
                                    "type": "string",
                                    "description": "Content to analyze or process"
                                },
                                "priority": {
                                    "type": "string",
                                    "enum": ["low", "medium", "high", "critical"],
                                    "description": "Task priority level"
                                },
                                "context": {
                                    "type": "object",
                                    "description": "Additional context for the task"
                                },
                                "routing_strategy": {
                                    "type": "string",
                                    "enum": ["load_balancing", "capability_based", "cost_optimized", "performance_based"],
                                    "description": "Model routing strategy"
                                }
                            },
                            "required": ["task_type", "content"]
                        }
                    ),
                    Tool(
                        name="get_task_status",
                        description="Get the status of a submitted task",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "task_id": {
                                    "type": "string",
                                    "description": "ID of the task to check"
                                }
                            },
                            "required": ["task_id"]
                        }
                    ),
                    Tool(
                        name="list_active_tasks",
                        description="List all active tasks",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "status_filter": {
                                    "type": "string",
                                    "enum": ["pending", "running", "completed", "failed"],
                                    "description": "Filter tasks by status"
                                }
                            }
                        }
                    ),
                    Tool(
                        name="get_system_status",
                        description="Get overall system status and health",
                        inputSchema={
                            "type": "object",
                            "properties": {}
                        }
                    ),
                    Tool(
                        name="get_model_analytics",
                        description="Get model routing analytics and performance data",
                        inputSchema={
                            "type": "object",
                            "properties": {}
                        }
                    ),
                    Tool(
                        name="get_memory_stats",
                        description="Get memory usage statistics",
                        inputSchema={
                            "type": "object",
                            "properties": {}
                        }
                    ),
                    Tool(
                        name="get_error_stats",
                        description="Get error statistics and diagnostics",
                        inputSchema={
                            "type": "object",
                            "properties": {}
                        }
                    ),
                    Tool(
                        name="create_context_session",
                        description="Create a new context session for multi-turn conversations",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "session_id": {
                                    "type": "string",
                                    "description": "Unique session identifier"
                                },
                                "task_type": {
                                    "type": "string",
                                    "description": "Type of task for this session"
                                },
                                "context_data": {
                                    "type": "object",
                                    "description": "Initial context data"
                                }
                            },
                            "required": ["session_id", "task_type"]
                        }
                    ),
                    Tool(
                        name="optimize_system",
                        description="Analyze and optimize system performance",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "optimization_type": {
                                    "type": "string",
                                    "enum": ["routing", "memory", "error_handling"],
                                    "description": "Type of optimization to perform"
                                }
                            },
                            "required": ["optimization_type"]
                        }
                    )
                ]
            )
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            try:
                if name == "submit_analysis_task":
                    return await self._submit_analysis_task(arguments)
                elif name == "get_task_status":
                    return await self._get_task_status(arguments)
                elif name == "list_active_tasks":
                    return await self._list_active_tasks(arguments)
                elif name == "get_system_status":
                    return await self._get_system_status(arguments)
                elif name == "get_model_analytics":
                    return await self._get_model_analytics(arguments)
                elif name == "get_memory_stats":
                    return await self._get_memory_stats(arguments)
                elif name == "get_error_stats":
                    return await self._get_error_stats(arguments)
                elif name == "create_context_session":
                    return await self._create_context_session(arguments)
                elif name == "optimize_system":
                    return await self._optimize_system(arguments)
                else:
                    return CallToolResult(
                        content=[TextContent(
                            type="text",
                            text=f"Unknown tool: {name}"
                        )],
                        isError=True
                    )
            except Exception as e:
                logger.error(f"Error in tool {name}: {e}")
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Error executing tool {name}: {str(e)}"
                    )],
                    isError=True
                )
    
    def _setup_resources(self):
        """Setup MCP resources"""
        
        @self.server.list_resources()
        async def list_resources() -> ListResourcesResult:
            return ListResourcesResult(
                resources=[
                    Resource(
                        uri="autonomous-ensemble://system/status",
                        name="System Status",
                        description="Current system status and health metrics",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="autonomous-ensemble://analytics/routing",
                        name="Routing Analytics",
                        description="Model routing analytics and performance data",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="autonomous-ensemble://analytics/memory",
                        name="Memory Analytics",
                        description="Memory usage statistics and analytics",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="autonomous-ensemble://analytics/errors",
                        name="Error Analytics",
                        description="Error statistics and diagnostics",
                        mimeType="application/json"
                    ),
                    Resource(
                        uri="autonomous-ensemble://config/orchestrator",
                        name="Orchestrator Configuration",
                        description="Current orchestrator configuration",
                        mimeType="application/json"
                    )
                ]
            )
        
        @self.server.read_resource()
        async def read_resource(uri: str) -> ReadResourceResult:
            try:
                if uri == "autonomous-ensemble://system/status":
                    status = await self._get_comprehensive_status()
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=json.dumps(status, indent=2, default=str)
                        )]
                    )
                elif uri == "autonomous-ensemble://analytics/routing":
                    analytics = await self.router.get_routing_analytics()
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=json.dumps(analytics, indent=2, default=str)
                        )]
                    )
                elif uri == "autonomous-ensemble://analytics/memory":
                    stats = await self.memory_manager.get_memory_stats()
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=json.dumps(stats, indent=2, default=str)
                        )]
                    )
                elif uri == "autonomous-ensemble://analytics/errors":
                    stats = await self.error_system.get_error_stats()
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=json.dumps(stats, indent=2, default=str)
                        )]
                    )
                elif uri == "autonomous-ensemble://config/orchestrator":
                    config = await self._get_orchestrator_config()
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=json.dumps(config, indent=2, default=str)
                        )]
                    )
                else:
                    return ReadResourceResult(
                        contents=[TextContent(
                            type="text",
                            text=f"Resource not found: {uri}"
                        )]
                    )
            except Exception as e:
                logger.error(f"Error reading resource {uri}: {e}")
                return ReadResourceResult(
                    contents=[TextContent(
                        type="text",
                        text=f"Error reading resource: {str(e)}"
                    )]
                )
    
    def _setup_event_handlers(self):
        """Setup event handlers for system components"""
        
        # Memory manager events
        async def memory_pressure_handler(data: Dict[str, Any]):
            logger.warning(f"Memory pressure detected: {data}")
            # Trigger memory cleanup
            await self.memory_manager._emergency_cleanup()
        
        self.memory_manager.add_event_handler("memory_pressure", memory_pressure_handler)
        
        # Error system events
        async def error_alert_handler(data: Dict[str, Any]):
            logger.error(f"Error alert: {data}")
            # Could trigger notifications or automatic recovery
        
        self.error_system.add_event_callback("alert_triggered", error_alert_handler)
        
        # Circuit breaker events
        async def circuit_breaker_handler(data: Dict[str, Any]):
            logger.warning(f"Circuit breaker opened: {data}")
            # Could trigger fallback model activation
        
        self.error_system.add_event_callback("circuit_breaker_opened", circuit_breaker_handler)
    
    async def _submit_analysis_task(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Submit a political document analysis task"""
        try:
            task_type = arguments["task_type"]
            content = arguments["content"]
            priority_str = arguments.get("priority", "medium")
            context = arguments.get("context", {})
            routing_strategy = arguments.get("routing_strategy", "capability_based")
            
            # Map priority string to enum
            priority_map = {
                "low": TaskPriority.LOW,
                "medium": TaskPriority.MEDIUM,
                "high": TaskPriority.HIGH,
                "critical": TaskPriority.CRITICAL
            }
            priority = priority_map.get(priority_str, TaskPriority.MEDIUM)
            
            # Create task
            self.task_counter += 1
            task_id = f"task_{self.task_counter}_{int(datetime.now().timestamp())}"
            
            task = PoliticalDocumentTask(
                task_id=task_id,
                task_type=task_type,
                priority=priority,
                context=context
            )
            
            # Store task content in memory
            await self.memory_manager.store(
                f"task_content_{task_id}",
                content,
                MemoryType.TEMPORARY,
                Priority.MEDIUM,
                ttl=3600,  # 1 hour
                tags=["task", task_type]
            )
            
            # Submit task to orchestrator
            await self.orchestrator.submit_task(task)
            
            # Create routing request for model selection
            routing_request = RoutingRequest(
                request_id=task_id,
                task_type=task_type,
                content=content,
                complexity=TaskComplexity.MEDIUM,  # Could be determined dynamically
                priority=priority.value,
                context=context
            )
            
            # Route to appropriate model
            routing_response = await self.router.route_request(routing_request, routing_strategy)
            
            # Track active task
            self.active_tasks[task_id] = {
                "task": task,
                "routing_response": routing_response,
                "submitted_at": datetime.now(),
                "status": "submitted"
            }
            
            result = {
                "task_id": task_id,
                "status": "submitted",
                "selected_model": routing_response.selected_model,
                "estimated_cost": routing_response.estimated_cost,
                "estimated_latency": routing_response.estimated_latency,
                "confidence": routing_response.confidence,
                "fallback_models": routing_response.fallback_models
            }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error submitting analysis task: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error submitting task: {str(e)}"
                )],
                isError=True
            )
    
    async def _get_task_status(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Get the status of a submitted task"""
        try:
            task_id = arguments["task_id"]
            
            # Get status from orchestrator
            orchestrator_status = await self.orchestrator.get_task_status(task_id)
            
            # Get additional info from active tasks
            task_info = self.active_tasks.get(task_id, {})
            
            if orchestrator_status:
                result = {
                    "task_id": task_id,
                    "orchestrator_status": orchestrator_status,
                    "task_info": task_info,
                    "checked_at": datetime.now().isoformat()
                }
            else:
                result = {
                    "task_id": task_id,
                    "status": "not_found",
                    "message": "Task not found in orchestrator"
                }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error getting task status: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error getting task status: {str(e)}"
                )],
                isError=True
            )
    
    async def _list_active_tasks(self, arguments: Dict[str, Any]) -> CallToolResult:
        """List all active tasks"""
        try:
            status_filter = arguments.get("status_filter")
            
            # Get system status
            system_status = await self.orchestrator.get_system_status()
            
            # Filter tasks if requested
            tasks = self.active_tasks
            if status_filter:
                filtered_tasks = {}
                for task_id, task_info in tasks.items():
                    task_status = task_info.get("task", {}).get("status", "unknown")
                    if task_status == status_filter:
                        filtered_tasks[task_id] = task_info
                tasks = filtered_tasks
            
            result = {
                "active_tasks": len(tasks),
                "tasks": tasks,
                "system_status": system_status,
                "checked_at": datetime.now().isoformat()
            }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error listing active tasks: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error listing tasks: {str(e)}"
                )],
                isError=True
            )
    
    async def _get_system_status(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Get overall system status and health"""
        try:
            status = await self._get_comprehensive_status()
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(status, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error getting system status: {str(e)}"
                )],
                isError=True
            )
    
    async def _get_model_analytics(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Get model routing analytics and performance data"""
        try:
            analytics = await self.router.get_routing_analytics()
            model_status = await self.router.get_model_status()
            optimization = await self.router.optimize_routing()
            
            result = {
                "analytics": analytics,
                "model_status": model_status,
                "optimization": optimization,
                "timestamp": datetime.now().isoformat()
            }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error getting model analytics: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error getting model analytics: {str(e)}"
                )],
                isError=True
            )
    
    async def _get_memory_stats(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Get memory usage statistics"""
        try:
            stats = await self.memory_manager.get_memory_stats()
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(stats, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error getting memory stats: {str(e)}"
                )],
                isError=True
            )
    
    async def _get_error_stats(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Get error statistics and diagnostics"""
        try:
            stats = await self.error_system.get_error_stats()
            recommendations = await self.error_system.get_recovery_recommendations()
            
            result = {
                "error_stats": stats,
                "recovery_recommendations": recommendations,
                "timestamp": datetime.now().isoformat()
            }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error getting error stats: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error getting error stats: {str(e)}"
                )],
                isError=True
            )
    
    async def _create_context_session(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Create a new context session for multi-turn conversations"""
        try:
            session_id = arguments["session_id"]
            task_type = arguments["task_type"]
            context_data = arguments.get("context_data", {})
            
            # Create context window
            window_id = await self.memory_manager.create_context_window(
                session_id=session_id,
                task_type=task_type,
                context_data=context_data
            )
            
            result = {
                "session_id": session_id,
                "window_id": window_id,
                "task_type": task_type,
                "status": "created",
                "created_at": datetime.now().isoformat()
            }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(result, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error creating context session: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error creating context session: {str(e)}"
                )],
                isError=True
            )
    
    async def _optimize_system(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Analyze and optimize system performance"""
        try:
            optimization_type = arguments["optimization_type"]
            
            if optimization_type == "routing":
                optimization = await self.router.optimize_routing()
            elif optimization_type == "memory":
                # Trigger memory optimization
                await self.memory_manager._optimize_access_patterns()
                stats = await self.memory_manager.get_memory_stats()
                optimization = {
                    "type": "memory",
                    "stats": stats,
                    "optimized_at": datetime.now().isoformat()
                }
            elif optimization_type == "error_handling":
                recommendations = await self.error_system.get_recovery_recommendations()
                optimization = {
                    "type": "error_handling",
                    "recommendations": recommendations,
                    "optimized_at": datetime.now().isoformat()
                }
            else:
                optimization = {
                    "error": f"Unknown optimization type: {optimization_type}"
                }
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(optimization, indent=2, default=str)
                )]
            )
            
        except Exception as e:
            logger.error(f"Error optimizing system: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Error optimizing system: {str(e)}"
                )],
                isError=True
            )
    
    async def _get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        orchestrator_status = await self.orchestrator.get_system_status()
        model_status = await self.router.get_model_status()
        memory_stats = await self.memory_manager.get_memory_stats()
        error_stats = await self.error_system.get_error_stats()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "orchestrator": orchestrator_status,
            "model_router": {
                "model_status": model_status,
                "total_models": len(model_status)
            },
            "memory_manager": memory_stats,
            "error_system": error_stats,
            "active_tasks": len(self.active_tasks),
            "system_health": self._calculate_health_score(
                orchestrator_status, model_status, memory_stats, error_stats
            )
        }
    
    def _calculate_health_score(self, orchestrator_status: Dict[str, Any],
                               model_status: Dict[str, Any],
                               memory_stats: Dict[str, Any],
                               error_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall system health score"""
        health_score = 100.0
        issues = []
        
        # Check orchestrator health
        if orchestrator_status.get("orchestrator_status") != "running":
            health_score -= 30
            issues.append("Orchestrator not running")
        
        # Check model availability
        available_models = sum(1 for model in model_status.values() 
                             if model.get("status") == "available")
        total_models = len(model_status)
        
        if available_models < total_models * 0.5:
            health_score -= 20
            issues.append("Less than 50% of models available")
        
        # Check memory usage
        memory_usage = memory_stats.get("usage_percent", 0)
        if memory_usage > 80:
            health_score -= 15
            issues.append("High memory usage")
        
        # Check error rate
        cache_hit_rate = memory_stats.get("cache_hit_rate", 1.0)
        if cache_hit_rate < 0.7:
            health_score -= 10
            issues.append("Low cache hit rate")
        
        # Check circuit breakers
        circuit_breaker_trips = error_stats.get("circuit_breaker_trips", 0)
        if circuit_breaker_trips > 0:
            health_score -= min(circuit_breaker_trips * 5, 25)
            issues.append(f"{circuit_breaker_trips} circuit breaker trips")
        
        return {
            "score": max(0, health_score),
            "status": "healthy" if health_score >= 80 else "warning" if health_score >= 60 else "critical",
            "issues": issues
        }
    
    async def _get_orchestrator_config(self) -> Dict[str, Any]:
        """Get orchestrator configuration"""
        return {
            "max_concurrent_tasks": self.orchestrator.max_concurrent_tasks,
            "task_timeout": self.orchestrator.task_timeout,
            "agent_health_check_interval": self.orchestrator.agent_health_check_interval,
            "agents": {
                agent_id: {
                    "agent_type": agent.agent_type.value,
                    "status": agent.status,
                    "capabilities": agent.capabilities,
                    "performance_metrics": agent.performance_metrics
                }
                for agent_id, agent in self.orchestrator.agents.items()
            }
        }
    
    async def start(self):
        """Start all system components"""
        logger.info("Starting Autonomous Ensemble MCP Server...")
        
        # Start components
        await self.orchestrator.start()
        await self.memory_manager.start()
        await self.error_system.start()
        
        logger.info("All components started successfully")
    
    async def stop(self):
        """Stop all system components"""
        logger.info("Stopping Autonomous Ensemble MCP Server...")
        
        # Stop components
        await self.orchestrator.stop()
        await self.memory_manager.stop()
        await self.error_system.stop()
        
        logger.info("All components stopped")

# Main server instance
mcp_server = AutonomousEnsembleMCPServer()

async def main():
    """Main entry point"""
    try:
        # Initialize server
        await mcp_server.start()
        
        # Run MCP server
        async with stdio_server() as (read_stream, write_stream):
            await mcp_server.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="autonomous-ensemble",
                    server_version="1.0.0",
                    capabilities=mcp_server.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None
                    )
                )
            )
    
    except KeyboardInterrupt:
        logger.info("Shutting down server...")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        await mcp_server.stop()

if __name__ == "__main__":
    asyncio.run(main())