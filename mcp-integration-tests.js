#!/usr/bin/env node

/**
 * MCP Server Integration Testing System
 * Comprehensive testing suite for validating MCP server interactions,
 * n8n workflow integration, and end-to-end document processing pipelines
 */

import axios from 'axios';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import winston from 'winston';
import { performance } from 'perf_hooks';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'mcp-integration-tests' },
  transports: [
    new winston.transports.File({ filename: 'mcp-tests-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'mcp-tests-combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

class MCPIntegrationTester {
  constructor() {
    this.testResults = [];
    this.serverEndpoints = {
      'manifesto-context': 'http://localhost:8080',
      'political-content': 'http://localhost:8081', 
      'quality-control': 'http://localhost:8082',
      'research-integration': 'http://localhost:8083',
      'document-processing': 'http://localhost:8084',
      'vector-search': 'http://mcp-vector-search:8088',
      'n8n': 'http://localhost:5678'
    };
    this.testTimeout = 30000; // 30 seconds
  }

  async runAllTests() {
    logger.info('Starting comprehensive MCP integration tests...');
    const startTime = performance.now();

    const testSuites = [
      'Individual Server Tests',
      'Cross-Server Integration Tests', 
      'N8N Workflow Integration Tests',
      'End-to-End Document Processing Tests',
      'Performance and Load Tests',
      'Failover and Recovery Tests'
    ];

    for (const suiteName of testSuites) {
      logger.info(`Running ${suiteName}...`);
      
      try {
        switch (suiteName) {
          case 'Individual Server Tests':
            await this.runIndividualServerTests();
            break;
          case 'Cross-Server Integration Tests':
            await this.runCrossServerTests();
            break;
          case 'N8N Workflow Integration Tests':
            await this.runN8NIntegrationTests();
            break;
          case 'End-to-End Document Processing Tests':
            await this.runEndToEndTests();
            break;
          case 'Performance and Load Tests':
            await this.runPerformanceTests();
            break;
          case 'Failover and Recovery Tests':
            await this.runFailoverTests();
            break;
        }
      } catch (error) {
        logger.error(`Test suite ${suiteName} failed:`, error);
        this.recordTestResult(suiteName, false, error.message, 0);
      }
    }

    const endTime = performance.now();
    const totalDuration = endTime - startTime;

    const summary = this.generateTestSummary(totalDuration);
    logger.info('Integration tests completed', summary);

    // Generate detailed report
    await this.generateTestReport(summary);

    return summary;
  }

  async runIndividualServerTests() {
    const servers = [
      'manifesto-context',
      'political-content', 
      'quality-control',
      'research-integration',
      'document-processing',
      'vector-search'
    ];

    for (const serverName of servers) {
      await this.testServerHealth(serverName);
      await this.testServerCapabilities(serverName);
      await this.testServerBasicFunctions(serverName);
    }
  }

  async testServerHealth(serverName) {
    const testName = `${serverName}_health_check`;
    const startTime = performance.now();

    try {
      const response = await axios.get(
        `${this.serverEndpoints[serverName]}/health`,
        { timeout: 5000 }
      );

      const duration = performance.now() - startTime;
      const success = response.status === 200;

      this.recordTestResult(testName, success, 
        success ? 'Health check passed' : `HTTP ${response.status}`, 
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testServerCapabilities(serverName) {
    const testName = `${serverName}_capabilities_check`;
    const startTime = performance.now();

    try {
      const response = await axios.post(
        `${this.serverEndpoints[serverName]}/mcp/call`,
        { method: 'capabilities' },
        { 
          timeout: 10000,
          headers: { 'Content-Type': 'application/json' }
        }
      );

      const duration = performance.now() - startTime;
      const success = response.status === 200 && response.data.tools;
      const toolCount = response.data.tools ? response.data.tools.length : 0;

      this.recordTestResult(testName, success, 
        success ? `Found ${toolCount} tools` : 'No tools returned',
        duration
      );

      logger.info(`✓ ${testName}: ${toolCount} tools available`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testServerBasicFunctions(serverName) {
    const functionTests = {
      'manifesto-context': [
        { tool: 'get_context_by_tier', params: { tier: 2, category: 'healthcare' } }
      ],
      'political-content': [
        { tool: 'analyze_document_quality', params: { document_id: 'test-doc', analysis_type: 'voice_only' } }
      ],
      'quality-control': [
        { tool: 'calculate_context_tokens', params: { components: ['core_essence'] } }
      ],
      'research-integration': [
        { tool: 'source_credibility_check', params: { source_name: 'test-source', evaluation_criteria: ['bias'] } }
      ],
      'document-processing': [
        { tool: 'optimize_document_for_platform', params: { input_file_path: '/tmp/test.md', target_platform: 'web' } }
      ],
      'vector-search': [
        { tool: 'search_similar_content', params: { query: 'healthcare policy', limit: 3 } }
      ]
    };

    const tests = functionTests[serverName] || [];

    for (const test of tests) {
      const testName = `${serverName}_${test.tool}`;
      const startTime = performance.now();

      try {
        const response = await axios.post(
          `${this.serverEndpoints[serverName]}/mcp/call`,
          {
            tool: test.tool,
            parameters: test.params
          },
          { 
            timeout: this.testTimeout,
            headers: { 'Content-Type': 'application/json' }
          }
        );

        const duration = performance.now() - startTime;
        const success = response.status === 200;

        this.recordTestResult(testName, success, 
          success ? 'Function executed successfully' : `HTTP ${response.status}`,
          duration
        );

        if (success) {
          logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
        } else {
          logger.warn(`⚠ ${testName}: Unexpected response`);
        }
      } catch (error) {
        const duration = performance.now() - startTime;
        this.recordTestResult(testName, false, error.message, duration);
        logger.error(`✗ ${testName}: ${error.message}`);
      }
    }
  }

  async runCrossServerTests() {
    await this.testManifestoContextToPoliticalContent();
    await this.testPoliticalContentToQualityControl();
    await this.testResearchIntegrationToVectorSearch();
    await this.testDocumentProcessingChain();
  }

  async testManifestoContextToPoliticalContent() {
    const testName = 'manifesto_to_political_content_integration';
    const startTime = performance.now();

    try {
      // Step 1: Get context from manifesto-context
      const contextResponse = await axios.post(
        `${this.serverEndpoints['manifesto-context']}/mcp/call`,
        {
          tool: 'get_context_by_tier',
          parameters: { tier: 2, category: 'healthcare', include_voice_guide: true }
        },
        { timeout: 10000, headers: { 'Content-Type': 'application/json' } }
      );

      if (contextResponse.status !== 200) {
        throw new Error('Failed to get manifesto context');
      }

      // Step 2: Use context for content generation
      const contentResponse = await axios.post(
        `${this.serverEndpoints['political-content']}/mcp/call`,
        {
          tool: 'generate_white_paper',
          parameters: {
            topic: 'Universal Healthcare Access',
            category: 'healthcare',
            token_tier: 2,
            research_level: 'standard'
          }
        },
        { timeout: 30000, headers: { 'Content-Type': 'application/json' } }
      );

      const duration = performance.now() - startTime;
      const success = contentResponse.status === 200;

      this.recordTestResult(testName, success, 
        success ? 'Cross-server integration successful' : 'Content generation failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testPoliticalContentToQualityControl() {
    const testName = 'political_content_to_quality_control';
    const startTime = performance.now();

    try {
      // Test document content for quality review
      const testContent = `# Healthcare Reform Proposal
      
This white paper outlines a comprehensive approach to healthcare reform that prioritizes economic justice and ensures healthcare as a fundamental right for all Americans.

## Executive Summary
Our healthcare system must serve people, not profits. This proposal establishes a pathway to universal healthcare coverage while maintaining quality and controlling costs.`;

      const qualityResponse = await axios.post(
        `${this.serverEndpoints['quality-control']}/mcp/call`,
        {
          tool: 'review_document',
          parameters: {
            document_content: testContent,
            review_type: 'manifesto_alignment',
            strict_mode: false
          }
        },
        { timeout: 20000, headers: { 'Content-Type': 'application/json' } }
      );

      const duration = performance.now() - startTime;
      const success = qualityResponse.status === 200;

      this.recordTestResult(testName, success,
        success ? 'Quality review integration successful' : 'Quality review failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testResearchIntegrationToVectorSearch() {
    const testName = 'research_to_vector_search_integration';
    const startTime = performance.now();

    try {
      // Step 1: Perform research
      const researchResponse = await axios.post(
        `${this.serverEndpoints['research-integration']}/mcp/call`,
        {
          tool: 'research_topic',
          parameters: {
            topic: 'Nordic healthcare models',
            research_depth: 'quick',
            focus_areas: ['policy_examples', 'international_comparisons']
          }
        },
        { timeout: 15000, headers: { 'Content-Type': 'application/json' } }
      );

      if (researchResponse.status !== 200) {
        throw new Error('Research failed');
      }

      // Step 2: Search for similar content in vector database
      const vectorResponse = await axios.post(
        `${this.serverEndpoints['vector-search']}/mcp/call`,
        {
          tool: 'search_similar_content',
          parameters: {
            query: 'Nordic healthcare universal coverage',
            limit: 5,
            minimum_similarity: 0.6
          }
        },
        { timeout: 10000, headers: { 'Content-Type': 'application/json' } }
      );

      const duration = performance.now() - startTime;
      const success = vectorResponse.status === 200;

      this.recordTestResult(testName, success,
        success ? 'Research-vector integration successful' : 'Vector search failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testDocumentProcessingChain() {
    const testName = 'full_document_processing_chain';
    const startTime = performance.now();

    try {
      // Create test markdown content
      const testMarkdown = `# Test Policy Document

This is a test document for validating the document processing chain.

## Key Points
- Economic justice principles
- Democratic renewal
- Universal basic services

## Implementation
The American Social Trust Fund will finance these initiatives.`;

      // Test document conversion
      const processingResponse = await axios.post(
        `${this.serverEndpoints['document-processing']}/mcp/call`,
        {
          tool: 'generate_professional_pdf',
          parameters: {
            markdown_content: testMarkdown,
            document_title: 'Test Policy Document',
            document_type: 'policy_brief',
            include_coverpage: true
          }
        },
        { timeout: 30000, headers: { 'Content-Type': 'application/json' } }
      );

      const duration = performance.now() - startTime;
      const success = processingResponse.status === 200;

      this.recordTestResult(testName, success,
        success ? 'Document processing chain successful' : 'Document processing failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async runN8NIntegrationTests() {
    await this.testN8NWorkflowExecution();
    await this.testN8NMCPCommunication();
    await this.testN8NWebhookTrigger();
  }

  async testN8NWorkflowExecution() {
    const testName = 'n8n_workflow_execution';
    const startTime = performance.now();

    try {
      // Test the Enhanced Political Document Processor workflow
      const webhookResponse = await axios.post(
        `${this.serverEndpoints['n8n']}/webhook/process-document-enhanced`,
        {
          filename: 'test-healthcare-policy.md',
          filePath: '/tmp/test-document.md',
          documentType: 'white_paper',
          taskType: 'generate_whitepaper',
          category: 'healthcare',
          tokenTier: 2,
          topic: 'Universal Healthcare Coverage',
          researchLevel: 'standard',
          outputFormat: 'pdf',
          metadata: {
            test: true,
            author: 'Integration Test'
          }
        },
        { 
          timeout: 60000, // Allow 60 seconds for full workflow
          headers: { 'Content-Type': 'application/json' }
        }
      );

      const duration = performance.now() - startTime;
      const success = webhookResponse.status === 200;

      this.recordTestResult(testName, success,
        success ? 'N8N workflow executed successfully' : 'Workflow execution failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testN8NMCPCommunication() {
    const testName = 'n8n_mcp_communication';
    const startTime = performance.now();

    try {
      // Test n8n's ability to communicate with MCP servers
      const response = await axios.get(
        `${this.serverEndpoints['n8n']}/api/v1/workflows`,
        {
          timeout: 10000,
          headers: {
            'Authorization': `Bearer ${process.env.N8N_API_KEY || 'test-key'}`
          }
        }
      );

      const duration = performance.now() - startTime;
      const success = response.status === 200;

      this.recordTestResult(testName, success,
        success ? 'N8N API communication successful' : 'N8N API communication failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.warn(`⚠ ${testName}: ${error.message} (may be expected if N8N auth required)`);
    }
  }

  async testN8NWebhookTrigger() {
    const testName = 'n8n_webhook_trigger';
    const startTime = performance.now();

    try {
      // Test webhook trigger responsiveness
      const response = await axios.post(
        `${this.serverEndpoints['n8n']}/webhook/test`,
        { test: 'integration-test', timestamp: new Date().toISOString() },
        { timeout: 10000, headers: { 'Content-Type': 'application/json' } }
      );

      const duration = performance.now() - startTime;
      const success = response.status === 200 || response.status === 404; // 404 is acceptable if webhook doesn't exist

      this.recordTestResult(testName, success,
        success ? 'Webhook trigger responsive' : 'Webhook trigger failed',
        duration
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      // Webhook might not exist, so we'll consider connection successful if we can reach N8N
      const success = error.response && error.response.status === 404;
      this.recordTestResult(testName, success, 
        success ? 'N8N reachable (webhook not configured)' : error.message, 
        duration
      );
      
      if (success) {
        logger.info(`✓ ${testName}: N8N is responsive`);
      } else {
        logger.error(`✗ ${testName}: ${error.message}`);
      }
    }
  }

  async runEndToEndTests() {
    await this.testCompleteDocumentWorkflow();
    await this.testMultiServerOrchestration();
  }

  async testCompleteDocumentWorkflow() {
    const testName = 'complete_document_workflow';
    const startTime = performance.now();

    try {
      // Simulate complete document processing workflow
      const workflow = [
        { server: 'manifesto-context', action: 'get context' },
        { server: 'research-integration', action: 'research topic' },
        { server: 'political-content', action: 'generate content' },
        { server: 'quality-control', action: 'review quality' },
        { server: 'document-processing', action: 'format document' }
      ];

      let workflowSuccess = true;
      const workflowResults = [];

      for (const step of workflow) {
        try {
          const stepStart = performance.now();
          
          // Simplified test calls for each step
          let response;
          switch (step.server) {
            case 'manifesto-context':
              response = await axios.post(
                `${this.serverEndpoints[step.server]}/mcp/call`,
                { tool: 'get_context_by_tier', parameters: { tier: 2 } },
                { timeout: 10000, headers: { 'Content-Type': 'application/json' } }
              );
              break;
            case 'research-integration':
              response = await axios.post(
                `${this.serverEndpoints[step.server]}/mcp/call`,
                { tool: 'research_topic', parameters: { topic: 'test', research_depth: 'quick' } },
                { timeout: 15000, headers: { 'Content-Type': 'application/json' } }
              );
              break;
            case 'political-content':
              response = await axios.post(
                `${this.serverEndpoints[step.server]}/mcp/call`,
                { tool: 'analyze_document_quality', parameters: { document_id: 'test' } },
                { timeout: 15000, headers: { 'Content-Type': 'application/json' } }
              );
              break;
            case 'quality-control':
              response = await axios.post(
                `${this.serverEndpoints[step.server]}/mcp/call`,
                { tool: 'review_document', parameters: { document_content: 'test content' } },
                { timeout: 15000, headers: { 'Content-Type': 'application/json' } }
              );
              break;
            case 'document-processing':
              response = { status: 200 }; // Simplified for testing
              break;
          }

          const stepDuration = performance.now() - stepStart;
          const stepSuccess = response.status === 200;
          
          workflowResults.push({
            step: step.server,
            success: stepSuccess,
            duration: stepDuration
          });

          if (!stepSuccess) {
            workflowSuccess = false;
          }
        } catch (error) {
          workflowSuccess = false;
          workflowResults.push({
            step: step.server,
            success: false,
            error: error.message
          });
        }
      }

      const duration = performance.now() - startTime;
      
      this.recordTestResult(testName, workflowSuccess,
        workflowSuccess ? 'Complete workflow successful' : 'Workflow failed at one or more steps',
        duration,
        { workflow_results: workflowResults }
      );

      logger.info(`✓ ${testName}: ${Math.round(duration)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testMultiServerOrchestration() {
    const testName = 'multi_server_orchestration';
    const startTime = performance.now();

    try {
      // Test concurrent requests to multiple servers
      const concurrentRequests = [
        axios.get(`${this.serverEndpoints['manifesto-context']}/health`, { timeout: 5000 }),
        axios.get(`${this.serverEndpoints['political-content']}/health`, { timeout: 5000 }),
        axios.get(`${this.serverEndpoints['quality-control']}/health`, { timeout: 5000 }),
        axios.get(`${this.serverEndpoints['research-integration']}/health`, { timeout: 5000 })
      ];

      const results = await Promise.allSettled(concurrentRequests);
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.status === 200).length;
      const success = successCount >= 3; // At least 3 out of 4 should succeed

      const duration = performance.now() - startTime;

      this.recordTestResult(testName, success,
        `${successCount}/4 servers responded successfully`,
        duration
      );

      logger.info(`✓ ${testName}: ${successCount}/4 servers responsive`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async runPerformanceTests() {
    await this.testResponseTimeUnderLoad();
    await this.testConcurrentRequestHandling();
  }

  async testResponseTimeUnderLoad() {
    const testName = 'response_time_under_load';
    const startTime = performance.now();

    try {
      const iterations = 10;
      const responseTimes = [];

      for (let i = 0; i < iterations; i++) {
        const iterationStart = performance.now();
        await axios.get(`${this.serverEndpoints['manifesto-context']}/health`, { timeout: 5000 });
        const iterationDuration = performance.now() - iterationStart;
        responseTimes.push(iterationDuration);
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const success = avgResponseTime < 1000 && maxResponseTime < 2000; // Thresholds in ms

      const duration = performance.now() - startTime;

      this.recordTestResult(testName, success,
        `Avg: ${Math.round(avgResponseTime)}ms, Max: ${Math.round(maxResponseTime)}ms`,
        duration,
        { average_response_time: avgResponseTime, max_response_time: maxResponseTime }
      );

      logger.info(`✓ ${testName}: Avg ${Math.round(avgResponseTime)}ms`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async testConcurrentRequestHandling() {
    const testName = 'concurrent_request_handling';
    const startTime = performance.now();

    try {
      const concurrentCount = 5;
      const requests = Array(concurrentCount).fill().map(() =>
        axios.get(`${this.serverEndpoints['manifesto-context']}/health`, { timeout: 10000 })
      );

      const results = await Promise.allSettled(requests);
      const successCount = results.filter(r => 
        r.status === 'fulfilled' && r.value.status === 200
      ).length;

      const success = successCount === concurrentCount;
      const duration = performance.now() - startTime;

      this.recordTestResult(testName, success,
        `${successCount}/${concurrentCount} concurrent requests succeeded`,
        duration
      );

      logger.info(`✓ ${testName}: ${successCount}/${concurrentCount} succeeded`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  async runFailoverTests() {
    // Simplified failover test - in a real scenario, we'd simulate server failures
    const testName = 'failover_readiness';
    const startTime = performance.now();

    try {
      // Test health check endpoints for failover preparation
      const servers = ['manifesto-context', 'political-content', 'quality-control'];
      let healthyServers = 0;

      for (const server of servers) {
        try {
          const response = await axios.get(`${this.serverEndpoints[server]}/health`, { timeout: 3000 });
          if (response.status === 200) {
            healthyServers++;
          }
        } catch (error) {
          // Server not available
        }
      }

      const success = healthyServers >= 2; // At least 2 critical servers should be healthy
      const duration = performance.now() - startTime;

      this.recordTestResult(testName, success,
        `${healthyServers}/${servers.length} critical servers healthy`,
        duration
      );

      logger.info(`✓ ${testName}: ${healthyServers}/${servers.length} servers ready`);
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordTestResult(testName, false, error.message, duration);
      logger.error(`✗ ${testName}: ${error.message}`);
    }
  }

  recordTestResult(testName, success, message, duration, metadata = {}) {
    this.testResults.push({
      test_name: testName,
      success,
      message,
      duration: Math.round(duration),
      timestamp: new Date().toISOString(),
      metadata
    });
  }

  generateTestSummary(totalDuration) {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.success).length;
    const failed = total - passed;
    const avgDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0) / total;

    return {
      timestamp: new Date().toISOString(),
      total_tests: total,
      passed_tests: passed,
      failed_tests: failed,
      success_rate: Math.round((passed / total) * 100),
      total_duration: Math.round(totalDuration),
      average_test_duration: Math.round(avgDuration),
      status: failed === 0 ? 'ALL_PASSED' : failed < total * 0.1 ? 'MOSTLY_PASSED' : 'FAILED'
    };
  }

  async generateTestReport(summary) {
    const report = {
      test_summary: summary,
      test_results: this.testResults,
      environment: {
        node_version: process.version,
        platform: process.platform,
        test_timestamp: new Date().toISOString()
      },
      recommendations: this.generateRecommendations()
    };

    // Save detailed report
    const reportPath = path.join(__dirname, 'test-reports', `integration-test-${Date.now()}.json`);
    await fs.ensureDir(path.dirname(reportPath));
    await fs.writeJson(reportPath, report, { spaces: 2 });

    // Generate markdown summary
    const markdownReport = this.generateMarkdownReport(report);
    const markdownPath = path.join(__dirname, 'test-reports', `integration-test-${Date.now()}.md`);
    await fs.writeFile(markdownPath, markdownReport);

    logger.info(`Test reports generated:`);
    logger.info(`  JSON: ${reportPath}`);
    logger.info(`  Markdown: ${markdownPath}`);

    return report;
  }

  generateRecommendations() {
    const recommendations = [];
    const failedTests = this.testResults.filter(r => !r.success);

    if (failedTests.length > 0) {
      recommendations.push('Address failed tests before production deployment');
      
      const serverFailures = failedTests.filter(t => t.test_name.includes('health_check'));
      if (serverFailures.length > 0) {
        recommendations.push('Critical servers are failing health checks - investigate immediately');
      }

      const integrationFailures = failedTests.filter(t => t.test_name.includes('integration'));
      if (integrationFailures.length > 0) {
        recommendations.push('Integration failures detected - review MCP server communication');
      }
    }

    const slowTests = this.testResults.filter(r => r.duration > 5000);
    if (slowTests.length > 0) {
      recommendations.push('Some tests are running slowly - consider performance optimization');
    }

    if (recommendations.length === 0) {
      recommendations.push('All tests passed - system ready for production deployment');
    }

    return recommendations;
  }

  generateMarkdownReport(report) {
    const { test_summary: summary, test_results: results } = report;

    let markdown = `# MCP Integration Test Report\n\n`;
    markdown += `**Generated:** ${summary.timestamp}\n`;
    markdown += `**Status:** ${summary.status}\n`;
    markdown += `**Success Rate:** ${summary.success_rate}%\n\n`;

    markdown += `## Summary\n\n`;
    markdown += `- **Total Tests:** ${summary.total_tests}\n`;
    markdown += `- **Passed:** ${summary.passed_tests}\n`;
    markdown += `- **Failed:** ${summary.failed_tests}\n`;
    markdown += `- **Total Duration:** ${summary.total_duration}ms\n`;
    markdown += `- **Average Duration:** ${summary.average_test_duration}ms\n\n`;

    if (summary.failed_tests > 0) {
      markdown += `## Failed Tests\n\n`;
      const failed = results.filter(r => !r.success);
      failed.forEach(test => {
        markdown += `- **${test.test_name}**: ${test.message} (${test.duration}ms)\n`;
      });
      markdown += `\n`;
    }

    markdown += `## Recommendations\n\n`;
    report.recommendations.forEach(rec => {
      markdown += `- ${rec}\n`;
    });

    return markdown;
  }
}

// Command line interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new MCPIntegrationTester();
  
  tester.runAllTests()
    .then(summary => {
      console.log('\n=== TEST SUMMARY ===');
      console.log(`Status: ${summary.status}`);
      console.log(`Success Rate: ${summary.success_rate}%`);
      console.log(`Total Tests: ${summary.total_tests}`);
      console.log(`Passed: ${summary.passed_tests}`);
      console.log(`Failed: ${summary.failed_tests}`);
      
      process.exit(summary.failed_tests === 0 ? 0 : 1);
    })
    .catch(error => {
      logger.error('Test execution failed:', error);
      process.exit(1);
    });
}

export default MCPIntegrationTester;