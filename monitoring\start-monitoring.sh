#!/bin/bash

# Political Document Processing System - Monitoring Startup Script
# This script starts the Prometheus and Grafana monitoring infrastructure

set -e

echo "🚀 Starting Political Document Processing Monitoring Infrastructure..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop and try again."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found. Please run this script from the project root directory."
    exit 1
fi

# Start monitoring services
echo "📊 Starting Prometheus and Grafana..."
docker-compose --profile monitoring up -d prometheus grafana

# Wait for services to be healthy
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."

# Check Prometheus
if curl -f http://localhost:9090/-/healthy >/dev/null 2>&1; then
    echo "✅ Prometheus is healthy at http://localhost:9090"
else
    echo "⚠️  Prometheus may not be ready yet. Check http://localhost:9090"
fi

# Check Grafana
if curl -f http://localhost:3001/api/health >/dev/null 2>&1; then
    echo "✅ <PERSON><PERSON> is healthy at http://localhost:3001"
    echo "   Default login: admin / admin (change on first login)"
else
    echo "⚠️  Grafana may not be ready yet. Check http://localhost:3001"
fi

echo ""
echo "🎯 Monitoring Dashboard URLs:"
echo "   📈 Prometheus: http://localhost:9090"
echo "   📊 Grafana: http://localhost:3001"
echo ""
echo "📋 Available Dashboards in Grafana:"
echo "   • Political Document Processing - System Overview"
echo "   • Political Document Analytics - Detailed Metrics"
echo ""
echo "🔧 To start all services including MCP servers:"
echo "   docker-compose up -d"
echo ""
echo "✨ Monitoring infrastructure is ready!"