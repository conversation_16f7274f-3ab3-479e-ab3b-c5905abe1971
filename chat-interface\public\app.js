/**
 * Political Document Processing Chat Interface
 * Frontend JavaScript for real-time document processing and chat
 */

class PoliticalChatInterface {
    constructor() {
        this.socket = null;
        this.sessionId = null;
        this.currentJobs = new Map();
        this.isTyping = false;
        
        this.initializeSocket();
        this.setupEventListeners();
        this.startNewSession();
    }

    initializeSocket() {
        this.socket = io({
            autoConnect: true,
            transports: ['websocket', 'polling']
        });

        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            console.log('Connected to Political Chat Interface');
        });

        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            console.log('Disconnected from Political Chat Interface');
        });

        this.socket.on('connect_error', (error) => {
            console.error('Connection error:', error);
            this.updateConnectionStatus(false);
        });

        // Chat event handlers
        this.socket.on('conversation_history', (data) => {
            this.loadConversationHistory(data.history);
        });

        this.socket.on('new_message', (data) => {
            this.addMessage(data.userId, data.message, data.messageType, data.timestamp);
        });

        this.socket.on('ai_response', (data) => {
            this.addMessage('ai_assistant', data.message, 'ai_response', data.timestamp);
        });

        this.socket.on('user_typing', (data) => {
            this.handleTypingIndicator(data);
        });

        // Document processing handlers
        this.socket.on('document_processing_started', (data) => {
            this.handleDocumentProcessingStarted(data);
        });

        this.socket.on('document_status_update', (data) => {
            this.updateDocumentStatus(data.jobId, data.status);
        });

        this.socket.on('document_completed', (data) => {
            this.handleDocumentCompleted(data);
        });

        this.socket.on('document_processing_error', (data) => {
            this.handleDocumentError(data);
        });

        this.socket.on('error', (data) => {
            this.showError(data.message);
        });
    }

    setupEventListeners() {
        // Document form submission
        const documentForm = document.getElementById('documentForm');
        documentForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleDocumentSubmission();
        });

        // Chat input
        const chatInput = document.getElementById('chatInput');
        const sendButton = document.getElementById('sendButton');

        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            } else {
                this.handleTypingStart();
            }
        });

        chatInput.addEventListener('blur', () => {
            this.handleTypingStop();
        });

        sendButton.addEventListener('click', () => {
            this.sendChatMessage();
        });

        // File upload handling
        const fileUpload = document.getElementById('fileUpload');
        fileUpload.addEventListener('change', () => {
            this.handleFileSelection();
        });
    }

    async startNewSession() {
        try {
            const response = await fetch('/api/conversations/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: 'beau_lewis',
                    theme: 'political_document_processing'
                })
            });

            const data = await response.json();
            this.sessionId = data.sessionId;
            
            // Update UI
            document.getElementById('sessionId').textContent = this.sessionId.substring(0, 8);
            
            // Join conversation room
            this.socket.emit('join_conversation', {
                sessionId: this.sessionId,
                userId: 'beau_lewis'
            });

        } catch (error) {
            console.error('Failed to start session:', error);
            this.showError('Failed to start conversation session');
        }
    }

    updateConnectionStatus(connected) {
        const indicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (connected) {
            indicator.className = 'status-indicator connected';
            statusText.textContent = 'Connected';
        } else {
            indicator.className = 'status-indicator disconnected';
            statusText.textContent = 'Disconnected';
        }
    }

    handleDocumentSubmission() {
        const form = document.getElementById('documentForm');
        const formData = new FormData(form);
        
        // Get form values
        const taskType = document.getElementById('taskType').value;
        const topic = document.getElementById('topic').value;
        const category = document.getElementById('category').value;
        const tokenTier = document.getElementById('tokenTier').value;
        const researchLevel = document.getElementById('researchLevel').value;
        const outputFormat = document.getElementById('outputFormat').value;
        const fileUpload = document.getElementById('fileUpload').files[0];

        if (!taskType || !topic || !category) {
            this.showError('Please fill in all required fields');
            return;
        }

        // Prepare request data
        const requestData = {
            sessionId: this.sessionId,
            taskType,
            topic,
            category,
            tokenTier: parseInt(tokenTier),
            researchLevel,
            outputFormat
        };

        // Add file if uploaded
        if (fileUpload) {
            formData.append('sessionId', this.sessionId);
            formData.append('taskType', taskType);
            formData.append('topic', topic);
            formData.append('category', category);
            formData.append('tokenTier', tokenTier);
            formData.append('researchLevel', researchLevel);
            formData.append('outputFormat', outputFormat);

            // Submit via HTTP for file upload
            this.submitDocumentWithFile(formData);
        } else {
            // Submit via socket for faster processing
            this.socket.emit('process_document', requestData);
        }

        // Show processing modal
        this.showProcessingModal(taskType, topic);
    }

    async submitDocumentWithFile(formData) {
        try {
            const response = await fetch('/api/documents/process', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            this.handleDocumentProcessingStarted(result);

        } catch (error) {
            console.error('Error submitting document with file:', error);
            this.showError('Failed to submit document for processing');
        }
    }

    handleDocumentProcessingStarted(data) {
        if (data.success) {
            const job = {
                jobId: data.jobId,
                status: 'processing',
                message: data.message,
                estimatedCompletion: data.estimatedCompletion
            };

            this.currentJobs.set(data.jobId, job);
            this.updateActiveJobsDisplay();
            this.updateProcessingModal(data);

            // Add system message to chat
            this.addMessage('system', `Document processing started: ${data.message}`, 'system');
        } else {
            this.showError(data.message || 'Failed to start document processing');
        }
    }

    updateDocumentStatus(jobId, status) {
        const job = this.currentJobs.get(jobId);
        if (job) {
            job.status = status.processing_status;
            job.progress = status.progress_percentage || 0;
            job.currentStep = status.current_step;
            
            this.currentJobs.set(jobId, job);
            this.updateActiveJobsDisplay();
            this.updateProcessingModalProgress(jobId, status);
        }
    }

    handleDocumentCompleted(data) {
        const job = this.currentJobs.get(data.jobId);
        if (job) {
            job.status = 'completed';
            job.downloadUrl = data.downloadUrl;
            job.filename = data.filename;
            
            this.currentJobs.set(data.jobId, job);
            this.updateActiveJobsDisplay();
            this.closeProcessingModal();

            // Add completion message to chat
            this.addMessage('system', 
                `Document completed: ${data.filename}. <a href="${data.downloadUrl}" target="_blank">Download here</a>`, 
                'system'
            );

            // Update daily document count
            this.incrementDocumentCount();
        }
    }

    handleDocumentError(data) {
        this.showError(`Document processing failed: ${data.error}`);
        this.closeProcessingModal();
        
        // Remove failed job from active jobs
        this.currentJobs.delete(data.jobId);
        this.updateActiveJobsDisplay();
    }

    sendChatMessage() {
        const chatInput = document.getElementById('chatInput');
        const message = chatInput.value.trim();
        
        if (!message || !this.sessionId) return;

        // Add user message to chat immediately
        this.addMessage('beau_lewis', message, 'text');

        // Send to server
        this.socket.emit('chat_message', {
            sessionId: this.sessionId,
            message,
            messageType: message.includes('?') ? 'question' : 'text'
        });

        // Clear input
        chatInput.value = '';
        this.handleTypingStop();
    }

    addMessage(userId, message, messageType, timestamp = null) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');
        
        const isUser = userId === 'beau_lewis';
        const isAI = userId === 'ai_assistant';
        const isSystem = userId === 'system';
        
        let messageClass = 'message';
        if (isUser) messageClass += ' user-message';
        else if (isAI) messageClass += ' ai-message';
        else if (isSystem) messageClass += ' system-message';
        
        messageElement.className = messageClass;
        
        const timeStr = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();
        const userName = isUser ? 'You' : isAI ? 'AI Assistant' : 'System';
        
        messageElement.innerHTML = `
            <div class="message-content">
                <p>${message}</p>
            </div>
            <div class="message-time">${userName} - ${timeStr}</div>
        `;
        
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    loadConversationHistory(history) {
        const chatMessages = document.getElementById('chatMessages');
        
        // Clear existing messages except system welcome
        const systemMessages = chatMessages.querySelectorAll('.system-message');
        chatMessages.innerHTML = '';
        systemMessages.forEach(msg => chatMessages.appendChild(msg));
        
        // Add history messages
        history.forEach(item => {
            if (item.user_message) {
                this.addMessage('beau_lewis', item.user_message, 'text', item.timestamp);
            }
            if (item.system_response) {
                this.addMessage('ai_assistant', item.system_response, 'ai_response', item.timestamp);
            }
        });
    }

    handleTypingStart() {
        if (!this.isTyping) {
            this.isTyping = true;
            this.socket.emit('typing_start', { sessionId: this.sessionId });
        }
    }

    handleTypingStop() {
        if (this.isTyping) {
            this.isTyping = false;
            this.socket.emit('typing_stop', { sessionId: this.sessionId });
        }
    }

    handleTypingIndicator(data) {
        const typingIndicator = document.getElementById('typingIndicator');
        typingIndicator.style.display = data.typing ? 'block' : 'none';
    }

    handleFileSelection() {
        const fileUpload = document.getElementById('fileUpload');
        const file = fileUpload.files[0];
        
        if (file) {
            // Validate file size (50MB limit)
            if (file.size > 50 * 1024 * 1024) {
                this.showError('File size must be less than 50MB');
                fileUpload.value = '';
                return;
            }
            
            // Show file info
            this.addMessage('system', `File selected: ${file.name} (${this.formatFileSize(file.size)})`, 'system');
        }
    }

    updateActiveJobsDisplay() {
        const activeJobsContainer = document.getElementById('activeJobs');
        
        if (this.currentJobs.size === 0) {
            activeJobsContainer.innerHTML = '<p class="no-jobs">No active processing jobs</p>';
            return;
        }
        
        activeJobsContainer.innerHTML = '';
        
        this.currentJobs.forEach((job, jobId) => {
            const jobElement = document.createElement('div');
            jobElement.className = `job-item ${job.status}`;
            
            let statusText = job.status.charAt(0).toUpperCase() + job.status.slice(1);
            if (job.progress) {
                statusText += ` (${job.progress}%)`;
            }
            
            jobElement.innerHTML = `
                <div class="job-title">${job.message}</div>
                <div class="job-status">${statusText}</div>
                ${job.currentStep ? `<div class="job-step">${job.currentStep}</div>` : ''}
                ${job.downloadUrl ? `<a href="${job.downloadUrl}" target="_blank">Download ${job.filename}</a>` : ''}
            `;
            
            activeJobsContainer.appendChild(jobElement);
        });
    }

    showProcessingModal(taskType, topic) {
        const modal = document.getElementById('processingModal');
        const processingStatus = document.getElementById('processingStatus');
        
        processingStatus.textContent = `Processing ${taskType} for "${topic}"...`;
        modal.style.display = 'flex';
    }

    updateProcessingModal(data) {
        const estimatedTime = document.getElementById('estimatedTime');
        if (data.estimatedCompletion) {
            estimatedTime.textContent = `Estimated completion: ${data.estimatedCompletion} seconds`;
        }
    }

    updateProcessingModalProgress(jobId, status) {
        const progressFill = document.getElementById('progressFill');
        const processingStatus = document.getElementById('processingStatus');
        
        if (status.progress_percentage) {
            progressFill.style.width = `${status.progress_percentage}%`;
        }
        
        if (status.current_step) {
            processingStatus.textContent = status.current_step;
        }
    }

    closeProcessingModal() {
        const modal = document.getElementById('processingModal');
        modal.style.display = 'none';
    }

    incrementDocumentCount() {
        const documentsToday = document.getElementById('documentsToday');
        const current = parseInt(documentsToday.textContent) || 0;
        documentsToday.textContent = current + 1;
    }

    showError(message) {
        // Create error toast
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-toast';
        errorDiv.innerHTML = `
            <div class="error-content">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        // Add styles if not exists
        if (!document.querySelector('#error-toast-styles')) {
            const style = document.createElement('style');
            style.id = 'error-toast-styles';
            style.textContent = `
                .error-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: var(--accent-red);
                    color: white;
                    padding: 1rem;
                    border-radius: 4px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 1001;
                    animation: slideIn 0.3s ease;
                }
                .error-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: 1rem;
                }
                .error-content button {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 1.2rem;
                    cursor: pointer;
                }
                @keyframes slideIn {
                    from { transform: translateX(100%); }
                    to { transform: translateX(0); }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(errorDiv);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Global functions for modal control
function closeModal() {
    document.getElementById('processingModal').style.display = 'none';
}

// Initialize the chat interface when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chatInterface = new PoliticalChatInterface();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // Page is hidden, potentially reduce socket activity
    } else {
        // Page is visible, ensure connection is active
        if (window.chatInterface && window.chatInterface.socket.disconnected) {
            window.chatInterface.socket.connect();
        }
    }
});