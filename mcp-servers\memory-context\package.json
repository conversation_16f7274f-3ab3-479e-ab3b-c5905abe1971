{"name": "memory-context-mcp-server", "version": "1.0.0", "description": "MCP server for advanced memory and context management across n8n workflow executions", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["mcp", "memory-management", "context-tracking", "conversation-memory", "document-context", "user-preferences", "session-management", "knowledge-graph", "n8n-workflows", "ai-memory-tools"], "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "fs-extra": "^11.2.0", "path": "^0.12.7", "crypto": "^1.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "tiktoken": "^1.0.15", "openai": "^4.20.1", "winston": "^3.11.0", "natural": "^6.12.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "axios": "^1.6.2", "lodash": "^4.17.21", "uuid": "^9.0.1", "compression": "^1.7.4", "sqlite3": "^5.1.6", "node-cron": "^3.0.3", "graph-data-structure": "^3.5.0", "ml-distance": "^4.0.1", "pino": "^8.16.1", "pino-pretty": "^10.2.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/Beaulewis1977/n8n-workflow.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/Beaulewis1977/n8n-workflow/issues"}, "homepage": "https://github.com/Beaulewis1977/n8n-workflow#readme"}