# Secure Analytics MCP Server

A comprehensive analytics and monitoring server with OAuth 2.1 authentication, real-time metrics collection, and advanced security features for the political document processing system.

## Overview

The Secure Analytics MCP Server provides:
- Real-time analytics dashboard with multiple visualization types
- OAuth 2.1 compliant authentication and authorization
- Comprehensive security measures including rate limiting and audit logging
- Performance monitoring and metrics collection
- Document generation and quality metrics tracking
- User behavior analysis and insights

## Features

### 🔐 Security Features
- **OAuth 2.1 Authentication**: Full implementation with authorization codes, access tokens, and refresh tokens
- **JWT Token Management**: Secure token generation and validation
- **Rate Limiting**: Configurable limits for different endpoint types
- **Session Management**: Redis-backed secure sessions
- **Audit Logging**: Comprehensive security event tracking
- **Failed Login Protection**: Automatic lockout after multiple failed attempts

### 📊 Analytics Capabilities
- **Real-time Dashboards**: Live metrics and performance monitoring
- **Document Metrics**: Track generation time, quality scores, token usage
- **System Performance**: CPU, memory, response time, throughput monitoring
- **User Behavior Analysis**: Usage patterns, preferences, conversion funnels
- **Custom Reports**: Generate comprehensive analytics reports in multiple formats

### 🛠️ Technical Features
- **Multi-Database Support**: PostgreSQL for persistence, Redis for caching
- **Prometheus Metrics**: Built-in metrics collection and exposure
- **Daily Log Rotation**: Automatic log management with retention policies
- **Data Aggregation**: Scheduled aggregation of analytics data
- **Archival System**: Automatic archival of old data

## Installation

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Environment Variables

```bash
# Server Configuration
MCP_SERVER_PORT=8090
LOG_LEVEL=info

# Database Configuration
POSTGRES_HOST=postgresql
POSTGRES_PORT=5432
POSTGRES_DB=political_conversations
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=your_postgres_password

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# OAuth Configuration
OAUTH_ISSUER=https://analytics.political-system.local
OAUTH_CLIENT_ID=analytics-mcp-client
OAUTH_CLIENT_SECRET=your_client_secret
OAUTH_REDIRECT_URI=http://localhost:8090/auth/callback
TOKEN_EXPIRY=3600
REFRESH_TOKEN_EXPIRY=604800

# Security Configuration
JWT_SECRET=your_jwt_secret
SESSION_SECRET=your_session_secret

# Analytics Configuration
ANALYTICS_RETENTION_DAYS=90
AGGREGATION_INTERVAL=300000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3001,http://localhost:5678
```

### Docker Installation

```bash
docker run -d \
  --name analytics-secure-mcp \
  -p 8090:8090 \
  --env-file .env \
  --network n8n-network \
  analytics-secure-mcp:latest
```

### Manual Installation

```bash
# Clone the repository
git clone <repository-url>
cd mcp-servers/analytics-secure

# Install dependencies
npm install

# Initialize database schema
npm run init-db

# Start the server
npm start
```

## MCP Tools

### 1. get_analytics_dashboard
Retrieve comprehensive analytics dashboard data with real-time metrics.

**Input Schema:**
```json
{
  "dashboard_type": "overview|performance|usage|quality|security",
  "time_range": "1h|24h|7d|30d|90d",
  "real_time": true
}
```

**Example:**
```json
{
  "dashboard_type": "overview",
  "time_range": "24h",
  "real_time": true
}
```

### 2. query_document_metrics
Query detailed document generation performance and quality metrics.

**Input Schema:**
```json
{
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-31T23:59:59Z",
  "metric_types": ["generation_time", "quality_score", "token_usage"],
  "filters": {
    "document_type": "report",
    "category": "political"
  },
  "aggregation": "hourly"
}
```

### 3. get_system_performance
Retrieve system performance analytics including resource usage and response times.

**Input Schema:**
```json
{
  "services": ["n8n", "mcp-servers", "vector-search", "all"],
  "metrics": ["cpu", "memory", "response_time", "throughput"],
  "time_range": "1h"
}
```

### 4. analyze_user_behavior
Analyze user interaction patterns and document usage trends.

**Input Schema:**
```json
{
  "analysis_type": "usage_patterns|document_preferences|session_analysis",
  "user_segment": "all|new_users|power_users",
  "time_period": "30d"
}
```

### 5. generate_analytics_report
Generate comprehensive analytics reports with insights and recommendations.

**Input Schema:**
```json
{
  "report_type": "executive_summary|performance_report|usage_analysis",
  "period": "daily|weekly|monthly",
  "format": "json|markdown|html|pdf",
  "include_recommendations": true
}
```

### 6. track_realtime_event
Track real-time analytics events for immediate processing and alerting.

**Input Schema:**
```json
{
  "event_type": "document_generated|quality_review_completed|error_occurred",
  "event_data": {},
  "priority": "low|medium|high|critical",
  "tags": ["tag1", "tag2"]
}
```

## API Endpoints

### Authentication Endpoints
- `GET /auth/authorize` - OAuth 2.1 authorization endpoint
- `POST /auth/token` - Token exchange endpoint
- `POST /auth/refresh` - Refresh token endpoint
- `POST /auth/revoke` - Token revocation endpoint

### Monitoring Endpoints
- `GET /health` - Health check endpoint
- `GET /metrics` - Prometheus metrics endpoint

### MCP Tool Endpoint
- `POST /mcp/call` - Execute MCP tools

## Security Best Practices

1. **Always use HTTPS in production**
2. **Rotate secrets regularly** (JWT_SECRET, SESSION_SECRET, OAUTH_CLIENT_SECRET)
3. **Configure proper CORS origins** for your environment
4. **Enable audit logging** for compliance
5. **Set appropriate rate limits** based on your usage patterns
6. **Monitor failed login attempts** and adjust lockout policies

## Database Schema

### OAuth Tables
- `oauth_clients` - OAuth client registrations
- `oauth_authorization_codes` - Temporary authorization codes
- `oauth_access_tokens` - Active access tokens
- `oauth_refresh_tokens` - Refresh tokens for token renewal

### Analytics Tables
- `analytics_events` - Raw event data (partitioned by timestamp)
- `analytics_metrics` - Aggregated metrics data
- `security_audit_log` - Security event audit trail
- `failed_login_attempts` - Failed authentication tracking

## Monitoring and Maintenance

### Scheduled Tasks
- **Token Cleanup**: Hourly cleanup of expired tokens
- **Data Aggregation**: 5-minute aggregation of analytics data
- **Data Archival**: Daily archival of old analytics data

### Performance Tuning
- Adjust `AGGREGATION_INTERVAL` for data processing frequency
- Configure `ANALYTICS_RETENTION_DAYS` based on storage capacity
- Monitor PostgreSQL query performance and add indexes as needed

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify PostgreSQL is running and accessible
   - Check database credentials in environment variables
   - Ensure database exists and user has proper permissions

2. **Redis Connection Error**
   - Verify Redis is running
   - Check Redis password configuration
   - Ensure Redis is accessible from the container/host

3. **OAuth Errors**
   - Verify OAuth client configuration
   - Check redirect URI matches configuration
   - Ensure JWT_SECRET is properly set

### Debug Mode
Enable debug logging:
```bash
LOG_LEVEL=debug npm start
```

## License

This MCP server is part of the political document processing system. See the main project license for details.