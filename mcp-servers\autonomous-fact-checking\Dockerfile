FROM node:18-alpine

# Install Chromium for Puppeteer
RUN apk update && apk upgrade && apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    curl \
    dumb-init \
    make \
    g++ \
    sqlite \
    && rm -rf /var/cache/apk/*

# Create app directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /app/logs /app/data

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcpuser -u 1001 -G nodejs

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY . .

# Set proper permissions
RUN chown -R mcpuser:nodejs /app

# Configure Puppeteer to use installed Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Switch to non-root user
USER mcpuser

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8094/health || exit 1

# Expose port
EXPOSE 8094

# Start the application
CMD ["node", "server.js"]