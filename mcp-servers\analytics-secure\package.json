{"name": "@political-system/analytics-secure-mcp", "version": "1.0.0", "description": "Secure Analytics MCP Server with OAuth 2.1 authentication and comprehensive monitoring", "main": "server.js", "type": "module", "author": "<PERSON>", "license": "MIT", "keywords": ["mcp", "analytics", "oauth2.1", "security", "political-documents", "monitoring"], "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "security-audit": "npm audit && snyk test"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "express": "^4.19.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "joi": "^17.11.0", "uuid": "^9.0.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-validator": "^7.0.1", "express-slow-down": "^2.0.1", "redis": "^4.6.12", "pg": "^8.11.3", "prometheus-client": "^1.1.0", "node-cron": "^3.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-oauth2": "^1.7.0", "oauth2-server": "^4.2.0", "crypto": "^1.0.1", "morgan": "^1.10.0", "express-session": "^1.17.3", "connect-redis": "^7.1.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "snyk": "^1.1266.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/beau-lewis/political-document-system"}}