{"name": "vector-search-mcp-server", "version": "1.0.0", "description": "ChromaDB vector search MCP server for political document context retrieval", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "health": "node -e \"console.log('Vector Search MCP Server Health Check')\""}, "dependencies": {"@anthropic-ai/mcp-sdk": "^0.4.0", "chromadb": "^1.8.1", "openai": "^4.28.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "pg": "^8.11.3", "redis": "^4.6.10", "fs-extra": "^11.2.0", "uuid": "^9.0.1", "tiktoken": "^1.0.10", "winston": "^3.11.0", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "vector-search", "chromadb", "political", "documents", "rag", "ai"], "author": "Political Document Processing System", "license": "MIT"}