# MCP Servers Setup Guide

This guide explains how to set up and use the Vibe Coder MCP and n8n MCP servers with Windows.

## Overview

Two MCP servers are configured for this project:

1. **Vibe Coder MCP** - Production-ready MCP server with complete agent integration
2. **n8n MCP Server** - Model Context Protocol server for n8n workflow automation

## Installation Status

### Vibe Coder MCP
- **Location**: `C:\vibe-coder-mcp`
- **Status**: Installed and built
- **Repository**: https://github.com/freshtechbro/Vibe-Coder-MCP

### n8n MCP Server
- **Location**: `C:\dev\n8n_workflow_windows\mcp-servers\n8n-mcp-server`
- **Status**: Installed and built
- **Repository**: https://github.com/leonardsellem/n8n-mcp-server

## Configuration

Both servers are configured in:
- `.cursor/mcp.json` - For Cursor IDE
- `.windsurf/mcp.json` - For Windsurf IDE

### Vibe Coder MCP Configuration

The Vibe Coder MCP is configured with:
- **API Keys**: All necessary API keys (OpenRouter, Moonshot, Perplexity) are included from the .env file
- **Output Directory**: `C:\vibe-coder-mcp\VibeCoderOutput` - where generated files will be saved
- **Code Map Allowed Directory**: `C:\dev\n8n_workflow_windows` - the directory that can be analyzed
- **Task Manager Read Directory**: `C:\dev\n8n_workflow_windows` - the directory for task management operations

### Important: n8n API Key Setup

Before using the n8n MCP server, you need to:

1. Start n8n:
   ```bash
   docker-compose up -d
   ```

2. Access n8n at http://localhost:5678

3. Navigate to Settings → API → API Keys

4. Create a new API key

5. Update the `N8N_API_KEY` value in both:
   - `.cursor/mcp.json`
   - `.windsurf/mcp.json`

## Windows Path Configuration

The MCP configurations use Windows-style paths:
- Vibe Coder MCP: `C:\vibe-coder-mcp\build\index.js`
- n8n MCP Server: `C:\dev\n8n_workflow_windows\mcp-servers\n8n-mcp-server\build\index.js`

These paths are compatible with Windows and will work correctly when the IDE restarts.

## Testing the Servers

### Test Vibe Coder MCP:
```bash
node C:\vibe-coder-mcp\build\index.js --help
```

### Test n8n MCP Server:
```bash
node C:\dev\n8n_workflow_windows\mcp-servers\n8n-mcp-server\build\index.js --help
```

## Quick Setup Script

Run the setup script to verify installation:
```bash
setup-mcp-servers.bat
```

## Troubleshooting

### If servers don't appear in IDE:
1. Restart your IDE (Cursor or Windsurf)
2. Check the IDE's MCP server panel
3. Ensure the paths in mcp.json match your actual installation paths

### If n8n MCP server fails:
1. Verify n8n is running (`docker-compose ps`)
2. Check that the API key is correctly set
3. Ensure the n8n API URL is correct (default: http://localhost:5678/api/v1)

### If Vibe Coder MCP fails:
1. Check that the server is built (`npm run build` in the vibe-coder-mcp directory)
2. Verify the path in mcp.json is correct
3. Check the server.log file in the vibe-coder-mcp directory for errors

## Features

### Vibe Coder MCP
- Complete agent integration
- Multi-transport support
- Development automation tools
- AI-assisted workflows

### n8n MCP Server
- Workflow management (list, create, update, delete)
- Execution management
- Webhook triggering
- Full n8n API integration

## Next Steps

1. Update the n8n API key in the MCP configurations
2. Restart your IDE to load the new MCP servers
3. The MCP servers will appear in your IDE's MCP panel
4. You can now use the MCP tools to interact with Vibe Coder and n8n workflows