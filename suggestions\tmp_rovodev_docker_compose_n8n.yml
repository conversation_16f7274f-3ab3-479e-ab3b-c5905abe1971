version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-political-workflow
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=changeme123
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=America/New_York
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./credentials:/home/<USER>/.n8n/credentials
    networks:
      - n8n-network

  chromadb:
    image: chromadb/chroma:latest
    container_name: chromadb-political
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    networks:
      - n8n-network

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: redis-political
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - n8n-network

volumes:
  n8n_data:
  chromadb_data:
  redis_data:

networks:
  n8n-network:
    driver: bridge