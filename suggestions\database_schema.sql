-- Database Schema for Political Document Conversation System
-- PostgreSQL initialization script

-- Conversation Sessions Table
CREATE TABLE conversation_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL DEFAULT 'beau_le<PERSON>s',
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    conversation_summary TEXT,
    conversation_theme VARCHAR(255),
    active_documents JSONB DEFAULT '[]'::jsonb,
    context_summary TEXT,
    total_interactions INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Conversation History Table
CREATE TABLE conversation_history (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES conversation_sessions(session_id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_message TEXT NOT NULL,
    system_response TEXT NOT NULL,
    documents_referenced JSONB DEFAULT '[]'::jsonb,
    follow_up_suggestions JSONB DEFAULT '[]'::jsonb,
    confidence_score FLOAT,
    processing_time_ms INTEGER,
    tokens_used INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Document Interactions Table
CREATE TABLE document_interactions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) REFERENCES conversation_sessions(session_id) ON DELETE CASCADE,
    document_name VARCHAR(500) NOT NULL,
    document_category VARCHAR(255),
    interaction_type VARCHAR(100), -- 'question', 'analysis', 'comparison', 'summary'
    interaction_count INTEGER DEFAULT 1,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User Preferences Table
CREATE TABLE user_preferences (
    user_id VARCHAR(255) PRIMARY KEY,
    detail_level VARCHAR(50) DEFAULT 'comprehensive', -- 'brief', 'standard', 'comprehensive'
    citation_style VARCHAR(50) DEFAULT 'with_quotes', -- 'minimal', 'standard', 'with_quotes'
    follow_up_questions BOOLEAN DEFAULT true,
    preferred_response_length VARCHAR(50) DEFAULT 'medium', -- 'short', 'medium', 'long'
    notification_preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Document Analytics Table
CREATE TABLE document_analytics (
    id SERIAL PRIMARY KEY,
    document_name VARCHAR(500) NOT NULL,
    document_category VARCHAR(255),
    total_questions INTEGER DEFAULT 0,
    total_references INTEGER DEFAULT 0,
    average_rating FLOAT,
    most_common_topics JSONB DEFAULT '[]'::jsonb,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(document_name)
);

-- System Performance Metrics Table
CREATE TABLE system_metrics (
    id SERIAL PRIMARY KEY,
    metric_date DATE DEFAULT CURRENT_DATE,
    total_conversations INTEGER DEFAULT 0,
    total_questions INTEGER DEFAULT 0,
    average_response_time_ms FLOAT,
    average_confidence_score FLOAT,
    total_tokens_used BIGINT DEFAULT 0,
    unique_documents_accessed INTEGER DEFAULT 0,
    user_satisfaction_score FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(metric_date)
);

-- Indexes for Performance
CREATE INDEX idx_conversation_sessions_user_id ON conversation_sessions(user_id);
CREATE INDEX idx_conversation_sessions_last_activity ON conversation_sessions(last_activity);
CREATE INDEX idx_conversation_history_session_id ON conversation_history(session_id);
CREATE INDEX idx_conversation_history_timestamp ON conversation_history(timestamp);
CREATE INDEX idx_document_interactions_session_id ON document_interactions(session_id);
CREATE INDEX idx_document_interactions_document_name ON document_interactions(document_name);
CREATE INDEX idx_document_interactions_last_accessed ON document_interactions(last_accessed);
CREATE INDEX idx_document_analytics_document_name ON document_analytics(document_name);
CREATE INDEX idx_system_metrics_metric_date ON system_metrics(metric_date);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_conversation_sessions_updated_at 
    BEFORE UPDATE ON conversation_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_interactions_updated_at 
    BEFORE UPDATE ON document_interactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default user preferences for Beau Lewis
INSERT INTO user_preferences (user_id, detail_level, citation_style, follow_up_questions, preferred_response_length)
VALUES ('beau_lewis', 'comprehensive', 'with_quotes', true, 'medium')
ON CONFLICT (user_id) DO NOTHING;

-- Function to clean up old conversation sessions (optional)
CREATE OR REPLACE FUNCTION cleanup_old_sessions(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM conversation_sessions 
    WHERE last_activity < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update document analytics
CREATE OR REPLACE FUNCTION update_document_analytics(
    doc_name VARCHAR(500),
    doc_category VARCHAR(255),
    rating INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO document_analytics (document_name, document_category, total_questions, total_references, average_rating)
    VALUES (doc_name, doc_category, 1, 1, rating)
    ON CONFLICT (document_name) 
    DO UPDATE SET 
        total_questions = document_analytics.total_questions + 1,
        total_references = document_analytics.total_references + 1,
        average_rating = CASE 
            WHEN rating IS NOT NULL THEN 
                COALESCE((document_analytics.average_rating * (document_analytics.total_questions - 1) + rating) / document_analytics.total_questions, rating)
            ELSE document_analytics.average_rating
        END,
        last_updated = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Function to get conversation context for session
CREATE OR REPLACE FUNCTION get_conversation_context(session_uuid VARCHAR(255))
RETURNS TABLE(
    session_info JSONB,
    recent_history JSONB,
    document_context JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        to_jsonb(cs.*) as session_info,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'timestamp', ch.timestamp,
                    'user_message', ch.user_message,
                    'system_response', ch.system_response,
                    'documents_referenced', ch.documents_referenced
                ) ORDER BY ch.timestamp DESC
            ) FROM conversation_history ch 
            WHERE ch.session_id = session_uuid 
            LIMIT 5), '[]'::jsonb
        ) as recent_history,
        COALESCE(
            (SELECT jsonb_agg(
                jsonb_build_object(
                    'document_name', di.document_name,
                    'category', di.document_category,
                    'interaction_count', di.interaction_count,
                    'last_accessed', di.last_accessed
                ) ORDER BY di.last_accessed DESC
            ) FROM document_interactions di 
            WHERE di.session_id = session_uuid), '[]'::jsonb
        ) as document_context
    FROM conversation_sessions cs
    WHERE cs.session_id = session_uuid;
END;
$$ LANGUAGE plpgsql;