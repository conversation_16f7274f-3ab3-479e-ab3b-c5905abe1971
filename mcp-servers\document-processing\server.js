#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import { Client } from 'pg';
import { createClient } from 'redis';
import axios from 'axios';
import FormData from 'form-data';
import mime from 'mime-types';
import CloudConvert from 'cloudconvert';
import { marked } from 'marked';
import puppeteer from 'puppeteer';
import { JSD<PERSON> } from 'jsdom';

/**
 * Document Processing MCP Server
 * Advanced document processing, format conversion, and professional styling using CloudConvert
 */

class DocumentProcessingServer {
  constructor() {
    this.server = new MCPServer({
      name: 'document-processing',
      version: '1.0.0'
    });
    
    // CloudConvert API client
    this.cloudConvert = new CloudConvert(process.env.CLOUDCONVERT_API_KEY);
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    this.tempDir = process.env.TEMP_DIR || '/app/temp';
    this.outputDir = process.env.OUTPUT_DIR || '/app/output';
    
    this.setupTools();
    this.setupResources();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'political_conversations',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      await this.pgClient.connect();
      await this.redisClient.connect();
      console.log('Document Processing Server: Connected to databases');
      
      // Ensure directories exist
      await fs.ensureDir(this.tempDir);
      await fs.ensureDir(this.outputDir);
    } catch (error) {
      console.error('Failed to initialize Document Processing Server:', error);
      throw error;
    }
  }

  setupTools() {
    // Tool: Convert Document Format with CloudConvert
    this.server.addTool({
      name: 'convert_document_cloudconvert',
      description: 'Convert documents between formats using CloudConvert API (MD, DOCX, PDF, HTML)',
      inputSchema: {
        type: 'object',
        properties: {
          input_file_path: {
            type: 'string',
            description: 'Path to input file'
          },
          input_format: {
            type: 'string',
            enum: ['md', 'markdown', 'docx', 'pdf', 'html', 'txt'],
            description: 'Input file format'
          },
          output_format: {
            type: 'string',
            enum: ['pdf', 'docx', 'html', 'txt', 'md'],
            description: 'Desired output format'
          },
          styling_options: {
            type: 'object',
            properties: {
              professional_layout: { type: 'boolean', default: true },
              include_toc: { type: 'boolean', default: true },
              header_footer: { type: 'boolean', default: true },
              page_margins: { type: 'string', enum: ['narrow', 'normal', 'wide'], default: 'normal' },
              font_family: { type: 'string', default: 'Times New Roman' },
              font_size: { type: 'integer', default: 12 },
              line_spacing: { type: 'number', default: 1.5 }
            }
          },
          political_branding: {
            type: 'boolean',
            default: true,
            description: 'Apply Beau Lewis political branding'
          }
        },
        required: ['input_file_path', 'input_format', 'output_format']
      }
    }, this.convertDocumentCloudConvert.bind(this));

    // Tool: Generate Professional PDF
    this.server.addTool({
      name: 'generate_professional_pdf',
      description: 'Generate professionally formatted PDF with political document styling',
      inputSchema: {
        type: 'object',
        properties: {
          markdown_content: {
            type: 'string',
            description: 'Markdown content to convert to PDF'
          },
          document_title: {
            type: 'string',
            description: 'Document title for header'
          },
          document_type: {
            type: 'string',
            enum: ['white_paper', 'policy_brief', 'manifesto', 'speech', 'report'],
            description: 'Type of political document'
          },
          author: {
            type: 'string',
            default: 'Beau Lewis',
            description: 'Document author'
          },
          include_coverpage: {
            type: 'boolean',
            default: true,
            description: 'Include professional cover page'
          },
          watermark: {
            type: 'string',
            description: 'Optional watermark text'
          }
        },
        required: ['markdown_content', 'document_title', 'document_type']
      }
    }, this.generateProfessionalPDF.bind(this));

    // Tool: Create DOCX Template
    this.server.addTool({
      name: 'create_docx_template',
      description: 'Create professionally formatted DOCX document with political styling',
      inputSchema: {
        type: 'object',
        properties: {
          content: {
            type: 'string',
            description: 'Document content (markdown or plain text)'
          },
          template_type: {
            type: 'string',
            enum: ['legislative_brief', 'policy_paper', 'campaign_document', 'press_release'],
            description: 'Document template type'
          },
          metadata: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              author: { type: 'string', default: 'Beau Lewis' },
              date: { type: 'string' },
              classification: { type: 'string', enum: ['public', 'internal', 'confidential'] },
              version: { type: 'string', default: '1.0' }
            }
          }
        },
        required: ['content', 'template_type']
      }
    }, this.createDOCXTemplate.bind(this));

    // Tool: Optimize Document for Platform
    this.server.addTool({
      name: 'optimize_document_for_platform',
      description: 'Optimize document format and styling for specific platforms',
      inputSchema: {
        type: 'object',
        properties: {
          input_file_path: {
            type: 'string',
            description: 'Path to input document'
          },
          target_platform: {
            type: 'string',
            enum: ['web', 'email', 'print', 'mobile', 'social_media'],
            description: 'Target platform for optimization'
          },
          optimization_level: {
            type: 'string',
            enum: ['basic', 'standard', 'aggressive'],
            default: 'standard',
            description: 'Level of optimization'
          }
        },
        required: ['input_file_path', 'target_platform']
      }
    }, this.optimizeDocumentForPlatform.bind(this));

    // Tool: Add Political Document Headers
    this.server.addTool({
      name: 'add_political_headers',
      description: 'Add professional headers, footers, and branding to political documents',
      inputSchema: {
        type: 'object',
        properties: {
          document_path: {
            type: 'string',
            description: 'Path to document file'
          },
          header_type: {
            type: 'string',
            enum: ['campaign', 'policy', 'official', 'movement'],
            description: 'Type of political header'
          },
          include_logo: {
            type: 'boolean',
            default: true,
            description: 'Include political movement logo'
          },
          classification: {
            type: 'string',
            enum: ['public', 'internal', 'draft'],
            default: 'public'
          }
        },
        required: ['document_path', 'header_type']
      }
    }, this.addPoliticalHeaders.bind(this));

    // Tool: Batch Convert Documents
    this.server.addTool({
      name: 'batch_convert_documents',
      description: 'Convert multiple documents in batch with consistent formatting',
      inputSchema: {
        type: 'object',
        properties: {
          input_directory: {
            type: 'string',
            description: 'Directory containing input documents'
          },
          output_format: {
            type: 'string',
            enum: ['pdf', 'docx', 'html'],
            description: 'Target format for all documents'
          },
          apply_consistent_styling: {
            type: 'boolean',
            default: true,
            description: 'Apply consistent political document styling'
          },
          naming_convention: {
            type: 'string',
            enum: ['preserve', 'date_prefix', 'category_prefix'],
            default: 'preserve'
          }
        },
        required: ['input_directory', 'output_format']
      }
    }, this.batchConvertDocuments.bind(this));
  }

  setupResources() {
    this.server.addResource({
      uri: 'processing://conversion_templates',
      name: 'Document Conversion Templates',
      description: 'Professional templates for political document conversion',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'processing://supported_formats',
      name: 'Supported File Formats',
      description: 'List of supported input/output formats via CloudConvert',
      mimeType: 'application/json'
    });
  }

  async convertDocumentCloudConvert({
    input_file_path,
    input_format,
    output_format,
    styling_options = {},
    political_branding = true
  }) {
    try {
      // Validate input file exists
      if (!(await fs.pathExists(input_file_path))) {
        throw new Error(`Input file not found: ${input_file_path}`);
      }

      const jobId = this.generateJobId();
      const outputFilename = `${path.basename(input_file_path, path.extname(input_file_path))}.${output_format}`;
      const outputPath = path.join(this.outputDir, outputFilename);

      console.log(`Starting CloudConvert job ${jobId}: ${input_format} → ${output_format}`);

      // Create CloudConvert job
      let job = await this.cloudConvert.jobs.create({
        tasks: {
          'import-file': {
            operation: 'import/upload'
          },
          'convert-file': {
            operation: 'convert',
            input: 'import-file',
            input_format: input_format,
            output_format: output_format,
            options: this.buildConversionOptions(output_format, styling_options, political_branding)
          },
          'export-file': {
            operation: 'export/url',
            input: 'convert-file'
          }
        }
      });

      // Upload file
      const uploadTask = job.tasks.filter(task => task.name === 'import-file')[0];
      const inputFile = await fs.createReadStream(input_file_path);
      
      await this.cloudConvert.tasks.upload(uploadTask, inputFile, path.basename(input_file_path));

      // Wait for job completion
      job = await this.cloudConvert.jobs.wait(job.id);

      if (job.status === 'finished') {
        // Download converted file
        const exportTask = job.tasks.filter(task => task.name === 'export-file')[0];
        const file = exportTask.result.files[0];
        
        const response = await axios.get(file.url, { responseType: 'stream' });
        const writer = fs.createWriteStream(outputPath);
        response.data.pipe(writer);

        await new Promise((resolve, reject) => {
          writer.on('finish', resolve);
          writer.on('error', reject);
        });

        // Apply additional political styling if needed
        if (political_branding && output_format === 'pdf') {
          await this.applyPoliticalBranding(outputPath);
        }

        return {
          jobId,
          inputFormat: input_format,
          outputFormat: output_format,
          inputPath: input_file_path,
          outputPath,
          filename: outputFilename,
          fileSize: (await fs.stat(outputPath)).size,
          conversionTime: Date.now() - job.created_at,
          cloudConvertJobId: job.id
        };

      } else {
        throw new Error(`CloudConvert job failed: ${job.status}`);
      }

    } catch (error) {
      console.error('Error in CloudConvert conversion:', error);
      throw error;
    }
  }

  async generateProfessionalPDF({
    markdown_content,
    document_title,
    document_type,
    author = 'Beau Lewis',
    include_coverpage = true,
    watermark
  }) {
    try {
      const jobId = this.generateJobId();
      const tempHtmlPath = path.join(this.tempDir, `${jobId}.html`);
      const outputPath = path.join(this.outputDir, `${document_title.replace(/\s+/g, '_')}.pdf`);

      // Convert markdown to HTML with professional styling
      const htmlContent = await this.generateStyledHTML(
        markdown_content,
        document_title,
        document_type,
        author,
        include_coverpage,
        watermark
      );

      // Save HTML temporarily
      await fs.writeFile(tempHtmlPath, htmlContent, 'utf8');

      // Convert HTML to PDF using CloudConvert
      const conversionResult = await this.convertDocumentCloudConvert({
        input_file_path: tempHtmlPath,
        input_format: 'html',
        output_format: 'pdf',
        styling_options: {
          professional_layout: true,
          page_margins: 'normal'
        },
        political_branding: true
      });

      // Clean up temp file
      await fs.remove(tempHtmlPath);

      return {
        jobId,
        document_title,
        document_type,
        author,
        outputPath: conversionResult.outputPath,
        filename: conversionResult.filename,
        fileSize: conversionResult.fileSize,
        include_coverpage,
        watermark: watermark || null
      };

    } catch (error) {
      console.error('Error generating professional PDF:', error);
      throw error;
    }
  }

  async createDOCXTemplate({
    content,
    template_type,
    metadata = {}
  }) {
    try {
      const jobId = this.generateJobId();
      const tempHtmlPath = path.join(this.tempDir, `${jobId}_template.html`);
      const outputPath = path.join(this.outputDir, `${template_type}_${jobId}.docx`);

      // Generate template HTML with appropriate styling
      const templateHTML = await this.generateTemplateHTML(content, template_type, metadata);
      
      // Save HTML temporarily
      await fs.writeFile(tempHtmlPath, templateHTML, 'utf8');

      // Convert to DOCX using CloudConvert
      const conversionResult = await this.convertDocumentCloudConvert({
        input_file_path: tempHtmlPath,
        input_format: 'html',
        output_format: 'docx',
        styling_options: {
          professional_layout: true,
          include_toc: template_type === 'policy_paper',
          header_footer: true
        },
        political_branding: true
      });

      // Clean up temp file
      await fs.remove(tempHtmlPath);

      return {
        jobId,
        template_type,
        metadata,
        outputPath: conversionResult.outputPath,
        filename: conversionResult.filename,
        fileSize: conversionResult.fileSize
      };

    } catch (error) {
      console.error('Error creating DOCX template:', error);
      throw error;
    }
  }

  async optimizeDocumentForPlatform({
    input_file_path,
    target_platform,
    optimization_level = 'standard'
  }) {
    try {
      const jobId = this.generateJobId();
      const inputExt = path.extname(input_file_path);
      const baseName = path.basename(input_file_path, inputExt);
      
      // Determine optimal format for platform
      const platformOptimizations = {
        web: { format: 'html', options: { responsive: true, fast_load: true } },
        email: { format: 'pdf', options: { compact: true, embedded_fonts: true } },
        print: { format: 'pdf', options: { high_quality: true, print_ready: true } },
        mobile: { format: 'html', options: { mobile_optimized: true, small_text: false } },
        social_media: { format: 'pdf', options: { social_preview: true, compact: true } }
      };

      const optimization = platformOptimizations[target_platform];
      const outputPath = path.join(this.outputDir, `${baseName}_${target_platform}.${optimization.format}`);

      // Apply platform-specific optimizations
      const optimizedOptions = this.buildPlatformOptions(target_platform, optimization_level);

      const conversionResult = await this.convertDocumentCloudConvert({
        input_file_path,
        input_format: inputExt.substring(1),
        output_format: optimization.format,
        styling_options: optimizedOptions,
        political_branding: target_platform !== 'social_media'
      });

      return {
        jobId,
        target_platform,
        optimization_level,
        inputPath: input_file_path,
        outputPath: conversionResult.outputPath,
        filename: conversionResult.filename,
        optimizations_applied: optimizedOptions,
        fileSize: conversionResult.fileSize
      };

    } catch (error) {
      console.error('Error optimizing document for platform:', error);
      throw error;
    }
  }

  async addPoliticalHeaders({
    document_path,
    header_type,
    include_logo = true,
    classification = 'public'
  }) {
    try {
      const jobId = this.generateJobId();
      
      // Read existing document
      const content = await fs.readFile(document_path, 'utf8');
      const ext = path.extname(document_path);
      
      // Generate header content based on type
      const headerContent = this.generatePoliticalHeader(header_type, include_logo, classification);
      
      // Apply headers based on document format
      let processedContent;
      if (ext === '.md' || ext === '.markdown') {
        processedContent = `${headerContent}\n\n${content}`;
      } else {
        // For other formats, convert to HTML, add headers, then convert back
        processedContent = await this.addHeadersToDocument(content, ext, headerContent);
      }

      // Save processed document
      const outputPath = path.join(
        this.outputDir, 
        `${path.basename(document_path, ext)}_with_headers${ext}`
      );
      await fs.writeFile(outputPath, processedContent, 'utf8');

      return {
        jobId,
        header_type,
        classification,
        include_logo,
        inputPath: document_path,
        outputPath,
        filename: path.basename(outputPath)
      };

    } catch (error) {
      console.error('Error adding political headers:', error);
      throw error;
    }
  }

  async batchConvertDocuments({
    input_directory,
    output_format,
    apply_consistent_styling = true,
    naming_convention = 'preserve'
  }) {
    try {
      const jobId = this.generateJobId();
      const results = [];
      
      // Get all documents in directory
      const files = await fs.readdir(input_directory);
      const documentFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.md', '.markdown', '.txt', '.html', '.docx', '.pdf'].includes(ext);
      });

      console.log(`Starting batch conversion of ${documentFiles.length} documents`);

      // Process each document
      for (const file of documentFiles) {
        try {
          const inputPath = path.join(input_directory, file);
          const inputExt = path.extname(file).substring(1);
          
          // Generate output filename based on naming convention
          const outputFilename = this.generateOutputFilename(file, output_format, naming_convention);
          
          const conversionResult = await this.convertDocumentCloudConvert({
            input_file_path: inputPath,
            input_format: inputExt,
            output_format,
            styling_options: apply_consistent_styling ? this.getPoliticalStyling() : {},
            political_branding: apply_consistent_styling
          });

          results.push({
            inputFile: file,
            outputFile: conversionResult.filename,
            outputPath: conversionResult.outputPath,
            fileSize: conversionResult.fileSize,
            status: 'success'
          });

        } catch (error) {
          console.error(`Failed to convert ${file}:`, error);
          results.push({
            inputFile: file,
            status: 'failed',
            error: error.message
          });
        }
      }

      const successCount = results.filter(r => r.status === 'success').length;
      const failCount = results.filter(r => r.status === 'failed').length;

      return {
        jobId,
        input_directory,
        output_format,
        total_files: documentFiles.length,
        successful_conversions: successCount,
        failed_conversions: failCount,
        results,
        apply_consistent_styling,
        naming_convention
      };

    } catch (error) {
      console.error('Error in batch document conversion:', error);
      throw error;
    }
  }

  // Helper methods

  buildConversionOptions(outputFormat, stylingOptions, politicalBranding) {
    const options = {};

    if (outputFormat === 'pdf') {
      options.page_range = '1-';
      options.margin_top = stylingOptions.page_margins === 'narrow' ? '0.5in' : 
                          stylingOptions.page_margins === 'wide' ? '1.5in' : '1in';
      options.margin_bottom = options.margin_top;
      options.margin_left = options.margin_top;
      options.margin_right = options.margin_top;
      
      if (politicalBranding) {
        options.header_html = this.getPoliticalHeaderHTML();
        options.footer_html = this.getPoliticalFooterHTML();
      }
    }

    if (outputFormat === 'docx') {
      if (stylingOptions.font_family) {
        options.font_family = stylingOptions.font_family;
      }
      if (stylingOptions.font_size) {
        options.font_size = stylingOptions.font_size;
      }
    }

    return options;
  }

  async generateStyledHTML(markdownContent, title, documentType, author, includeCoverpage, watermark) {
    const htmlContent = marked(markdownContent);
    
    const coverPage = includeCoverpage ? this.generateCoverPage(title, documentType, author) : '';
    const styles = this.getPoliticalDocumentCSS(documentType);
    const watermarkCSS = watermark ? this.getWatermarkCSS(watermark) : '';

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        ${styles}
        ${watermarkCSS}
    </style>
</head>
<body>
    ${coverPage}
    <div class="document-content">
        ${htmlContent}
    </div>
</body>
</html>`;
  }

  async generateTemplateHTML(content, templateType, metadata) {
    const templateStyles = this.getTemplateCSS(templateType);
    const headerSection = this.generateTemplateHeader(templateType, metadata);
    
    // Convert content to HTML if it's markdown
    let htmlContent = content;
    if (content.includes('#') || content.includes('*')) {
      htmlContent = marked(content);
    }

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${metadata.title || templateType}</title>
    <style>${templateStyles}</style>
</head>
<body>
    ${headerSection}
    <div class="template-content">
        ${htmlContent}
    </div>
</body>
</html>`;
  }

  buildPlatformOptions(platform, level) {
    const platformConfigs = {
      web: {
        basic: { responsive: true },
        standard: { responsive: true, optimize_images: true },
        aggressive: { responsive: true, optimize_images: true, minify_css: true }
      },
      email: {
        basic: { inline_css: true },
        standard: { inline_css: true, optimize_size: true },
        aggressive: { inline_css: true, optimize_size: true, compress_images: true }
      },
      print: {
        basic: { high_dpi: true },
        standard: { high_dpi: true, print_margins: true },
        aggressive: { high_dpi: true, print_margins: true, optimize_fonts: true }
      }
    };

    return platformConfigs[platform]?.[level] || {};
  }

  generatePoliticalHeader(headerType, includeLogo, classification) {
    const headers = {
      campaign: `---
layout: campaign_document
organization: Beau Lewis for Economic Justice
classification: ${classification}
logo: ${includeLogo ? 'beau_lewis_logo.png' : 'none'}
---`,
      policy: `---
layout: policy_document  
department: Policy Research & Development
classification: ${classification}
logo: ${includeLogo ? 'policy_logo.png' : 'none'}
---`,
      official: `---
layout: official_document
authority: Office of Beau Lewis
classification: ${classification}
logo: ${includeLogo ? 'official_seal.png' : 'none'}
---`,
      movement: `---
layout: movement_document
organization: Economic Justice Movement
classification: ${classification}
logo: ${includeLogo ? 'movement_logo.png' : 'none'}
---`
    };

    return headers[headerType] || headers.movement;
  }

  async addHeadersToDocument(content, ext, headerContent) {
    // Implementation for adding headers to different document formats
    // This would vary based on the document type
    return `${headerContent}\n\n${content}`;
  }

  generateOutputFilename(inputFile, outputFormat, namingConvention) {
    const baseName = path.basename(inputFile, path.extname(inputFile));
    const date = new Date().toISOString().split('T')[0];
    
    switch (namingConvention) {
      case 'date_prefix':
        return `${date}_${baseName}.${outputFormat}`;
      case 'category_prefix':
        return `political_${baseName}.${outputFormat}`;
      default:
        return `${baseName}.${outputFormat}`;
    }
  }

  getPoliticalStyling() {
    return {
      professional_layout: true,
      include_toc: true,
      header_footer: true,
      page_margins: 'normal',
      font_family: 'Times New Roman',
      font_size: 12,
      line_spacing: 1.5
    };
  }

  getPoliticalHeaderHTML() {
    return `
<div style="text-align: center; font-size: 10pt; margin-bottom: 20px;">
  <strong>Beau Lewis for Economic Justice</strong><br>
  Building an America That Works for Everyone
</div>`;
  }

  getPoliticalFooterHTML() {
    return `
<div style="text-align: center; font-size: 8pt; margin-top: 20px;">
  Page <span class="pageNumber"></span> of <span class="totalPages"></span> | 
  Economic Justice • Democratic Renewal • American Social Trust Fund
</div>`;
  }

  generateCoverPage(title, documentType, author) {
    return `
<div class="cover-page">
    <div class="logo-section">
        <h1>Beau Lewis</h1>
        <h2>Economic Justice Movement</h2>
    </div>
    <div class="title-section">
        <h1 class="document-title">${title}</h1>
        <h2 class="document-type">${documentType.replace('_', ' ').toUpperCase()}</h2>
    </div>
    <div class="author-section">
        <p>By ${author}</p>
        <p>${new Date().toLocaleDateString()}</p>
    </div>
    <div class="mission-statement">
        <p><em>"Building an America where economic systems serve people, not the other way around"</em></p>
    </div>
</div>
<div class="page-break"></div>`;
  }

  getPoliticalDocumentCSS(documentType) {
    return `
body {
    font-family: 'Times New Roman', serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    color: #333;
}

.cover-page {
    text-align: center;
    padding: 100px 0;
    page-break-after: always;
}

.cover-page .logo-section h1 {
    font-size: 36pt;
    color: #1e3a8a;
    margin-bottom: 10px;
}

.cover-page .logo-section h2 {
    font-size: 18pt;
    color: #dc2626;
    margin-bottom: 50px;
}

.document-title {
    font-size: 28pt;
    color: #1e3a8a;
    margin: 50px 0 20px 0;
}

.document-type {
    font-size: 16pt;
    color: #666;
    margin-bottom: 50px;
}

.author-section {
    font-size: 14pt;
    margin: 50px 0;
}

.mission-statement {
    font-size: 12pt;
    font-style: italic;
    color: #666;
    margin-top: 100px;
}

.document-content {
    max-width: 8.5in;
    margin: 0 auto;
}

h1, h2, h3 {
    color: #1e3a8a;
}

h1 {
    border-bottom: 2px solid #dc2626;
    padding-bottom: 10px;
}

.page-break {
    page-break-before: always;
}

@media print {
    .cover-page {
        page-break-after: always;
    }
}`;
  }

  getWatermarkCSS(watermarkText) {
    return `
.watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 72pt;
    color: rgba(0, 0, 0, 0.1);
    z-index: -1;
    pointer-events: none;
}

.watermark::before {
    content: '${watermarkText}';
}`;
  }

  getTemplateCSS(templateType) {
    const baseCSS = `
body {
    font-family: 'Times New Roman', serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    color: #333;
}

.template-header {
    border-bottom: 2px solid #1e3a8a;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.template-content {
    max-width: 8.5in;
    margin: 0 auto;
}`;

    const templateSpecificCSS = {
      legislative_brief: `
.template-header {
    text-align: center;
}
.urgency-indicator {
    background-color: #dc2626;
    color: white;
    padding: 5px 10px;
    display: inline-block;
    margin-bottom: 10px;
}`,
      policy_paper: `
.template-header {
    text-align: left;
}
.abstract {
    background-color: #f3f4f6;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #1e3a8a;
}`,
      campaign_document: `
.template-header {
    background: linear-gradient(135deg, #1e3a8a, #dc2626);
    color: white;
    padding: 20px;
    text-align: center;
}`,
      press_release: `
.template-header {
    text-align: center;
    border-bottom: 3px solid #dc2626;
}
.contact-info {
    text-align: right;
    font-size: 10pt;
    margin-top: 20px;
}`
    };

    return baseCSS + (templateSpecificCSS[templateType] || '');
  }

  generateTemplateHeader(templateType, metadata) {
    const date = metadata.date || new Date().toLocaleDateString();
    const author = metadata.author || 'Beau Lewis';
    const title = metadata.title || 'Political Document';

    const headers = {
      legislative_brief: `
<div class="template-header">
    <div class="urgency-indicator">LEGISLATIVE BRIEF</div>
    <h1>${title}</h1>
    <p><strong>Prepared by:</strong> ${author} | <strong>Date:</strong> ${date}</p>
    <p><strong>Classification:</strong> ${metadata.classification || 'Public'}</p>
</div>`,
      policy_paper: `
<div class="template-header">
    <h1>${title}</h1>
    <div class="abstract">
        <strong>Policy Paper</strong> • Version ${metadata.version || '1.0'} • ${date}<br>
        Author: ${author} • Classification: ${metadata.classification || 'Public'}
    </div>
</div>`,
      campaign_document: `
<div class="template-header">
    <h1>BEAU LEWIS FOR ECONOMIC JUSTICE</h1>
    <h2>${title}</h2>
    <p>${date}</p>
</div>`,
      press_release: `
<div class="template-header">
    <h1>PRESS RELEASE</h1>
    <h2>${title}</h2>
    <div class="contact-info">
        <p><strong>Contact:</strong> Beau Lewis Campaign<br>
        <strong>Date:</strong> ${date}</p>
    </div>
</div>`
    };

    return headers[templateType] || headers.policy_paper;
  }

  async applyPoliticalBranding(pdfPath) {
    // Additional post-processing for political branding
    // This could include adding logos, watermarks, etc.
    console.log(`Applied political branding to ${pdfPath}`);
  }

  generateJobId() {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async start() {
    await this.initialize();
    await this.server.start();
    console.log('Document Processing MCP Server started');
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    await this.server.stop();
  }
}

// Start the server
const server = new DocumentProcessingServer();

process.on('SIGINT', async () => {
  console.log('Shutting down Document Processing Server...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Document Processing Server...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);