#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { createClient } from 'redis';
import { Client } from 'pg';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import joi from 'joi';
import morgan from 'morgan';
import session from 'express-session';
import RedisStore from 'connect-redis';
import cron from 'node-cron';
import crypto from 'crypto';
import dotenv from 'dotenv';
import axios from 'axios';

dotenv.config();

/**
 * Economic Analysis MCP Server
 * Comprehensive economic analysis tools for political and policy analysis
 * Features economic indicators, policy impact assessment, budget analysis, and market research
 */

class EconomicAnalysisMCPServer {
  constructor() {
    this.server = new MCPServer({
      name: 'economic-analysis',
      version: '1.0.0'
    });

    // Initialize Express app with security middleware
    this.app = express();
    this.setupSecurity();
    this.setupLogging();
    this.setupValidation();
    
    // Initialize database clients
    this.redisClient = null;
    this.pgClient = null;
    
    // Economic data sources configuration
    this.dataSources = {
      fred: {
        apiKey: process.env.FRED_API_KEY,
        baseUrl: 'https://api.stlouisfed.org/fred'
      },
      worldBank: {
        baseUrl: 'https://api.worldbank.org/v2'
      },
      bea: {
        apiKey: process.env.BEA_API_KEY,
        baseUrl: 'https://apps.bea.gov/api/data'
      },
      census: {
        apiKey: process.env.CENSUS_API_KEY,
        baseUrl: 'https://api.census.gov/data'
      }
    };

    // Security configuration
    this.securityConfig = {
      jwtSecret: process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex'),
      saltRounds: 12,
      maxLoginAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      sessionSecret: process.env.SESSION_SECRET || crypto.randomBytes(64).toString('hex')
    };

    // Economic analysis configuration
    this.analysisConfig = {
      defaultTimeRange: {
        start: '2020-01-01',
        end: new Date().toISOString().split('T')[0]
      },
      cacheTTL: parseInt(process.env.CACHE_TTL) || 3600, // 1 hour
      maxDataPoints: parseInt(process.env.MAX_DATA_POINTS) || 1000,
      indicators: {
        gdp: ['GDP', 'GDPC1', 'GDPPOT'],
        inflation: ['CPIAUCSL', 'CPILFESL', 'GDPDEF', 'PCEPILFE'],
        employment: ['UNRATE', 'CIVPART', 'EMRATIO', 'PAYEMS'],
        monetary: ['FEDFUNDS', 'TB3MS', 'GS10', 'M2SL'],
        fiscal: ['GFDEBTN', 'FYFSGDA188S', 'FYONGDA188S'],
        trade: ['BOPGSTB', 'EXUSUK', 'IMPCH'],
        housing: ['HOUST', 'CSUSHPISA', 'MORTGAGE30US']
      }
    };

    this.setupMCPTools();
    this.setupExpressRoutes();
    this.setupDataCache();
  }

  setupSecurity() {
    // Helmet for security headers
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "https://api.stlouisfed.org", "https://api.worldbank.org", "https://apps.bea.gov", "https://api.census.gov"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: ********,
        includeSubDomains: true,
        preload: true
      }
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001', 'http://localhost:5678'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization', 'MCP-Protocol-Version'],
      maxAge: 86400 // 24 hours
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => req.path === '/health'
    });

    const apiLimiter = rateLimit({
      windowMs: 15 * 60 * 1000,
      max: 50, // Stricter limit for API endpoints
      message: {
        error: 'Too many API requests',
        retryAfter: '15 minutes'
      }
    });

    // Speed limiting
    const speedLimiter = slowDown({
      windowMs: 15 * 60 * 1000,
      delayAfter: 25,
      delayMs: 500
    });

    this.app.use('/api/', apiLimiter);
    this.app.use(limiter);
    this.app.use(speedLimiter);

    // Body parsing with size limits
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  setupLogging() {
    // Create logger with daily rotation
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'economic-analysis-mcp' },
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        }),
        new DailyRotateFile({
          filename: '/app/logs/economic-analysis-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '20m',
          maxFiles: '14d',
          auditFile: '/app/logs/audit.json'
        }),
        new DailyRotateFile({
          filename: '/app/logs/economic-analysis-error-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          level: 'error',
          maxSize: '20m',
          maxFiles: '30d'
        })
      ]
    });

    // HTTP request logging
    this.app.use(morgan('combined', {
      stream: {
        write: (message) => this.logger.info(message.trim())
      }
    }));
  }

  setupValidation() {
    // Common validation schemas
    this.validationSchemas = {
      indicatorAnalysis: joi.object({
        indicators: joi.array().items(joi.string()).min(1).required(),
        start_date: joi.date().iso().optional(),
        end_date: joi.date().iso().min(joi.ref('start_date')).optional(),
        frequency: joi.string().valid('daily', 'weekly', 'monthly', 'quarterly', 'annual').default('monthly'),
        transformation: joi.string().valid('level', 'growth', 'percent_change', 'log').default('level'),
        aggregation: joi.string().valid('average', 'sum', 'min', 'max').default('average')
      }),
      
      policyImpactAssessment: joi.object({
        policy_description: joi.string().min(10).required(),
        policy_type: joi.string().valid('fiscal', 'monetary', 'regulatory', 'trade', 'healthcare', 'education', 'infrastructure').required(),
        time_horizon: joi.string().valid('short_term', 'medium_term', 'long_term').default('medium_term'),
        affected_sectors: joi.array().items(joi.string()).optional(),
        budget_impact: joi.number().optional(),
        implementation_timeline: joi.string().optional()
      }),

      budgetAnalysis: joi.object({
        budget_type: joi.string().valid('federal', 'state', 'local', 'department', 'program').required(),
        jurisdiction: joi.string().required(),
        fiscal_year: joi.number().integer().min(2000).max(2050).required(),
        categories: joi.array().items(joi.string()).optional(),
        analysis_type: joi.string().valid('trend', 'composition', 'efficiency', 'comparison').default('trend')
      }),

      marketResearch: joi.object({
        market_type: joi.string().valid('labor', 'housing', 'financial', 'commodity', 'sector').required(),
        geographic_scope: joi.string().valid('national', 'regional', 'state', 'metro', 'local').default('national'),
        time_period: joi.string().valid('current', 'historical', 'forecast').default('current'),
        metrics: joi.array().items(joi.string()).optional(),
        sector: joi.string().optional()
      }),

      costBenefitAnalysis: joi.object({
        project_description: joi.string().min(10).required(),
        project_type: joi.string().valid('infrastructure', 'social_program', 'regulation', 'technology', 'education', 'healthcare').required(),
        costs: joi.object({
          initial: joi.number().min(0).required(),
          annual_operating: joi.number().min(0).optional(),
          maintenance: joi.number().min(0).optional()
        }).required(),
        benefits: joi.object({
          quantifiable: joi.array().items(joi.object({
            type: joi.string().required(),
            annual_value: joi.number().required(),
            description: joi.string().optional()
          })).optional(),
          qualitative: joi.array().items(joi.string()).optional()
        }).required(),
        time_horizon: joi.number().integer().min(1).max(50).default(10),
        discount_rate: joi.number().min(0).max(0.15).default(0.03)
      })
    };
  }

  async initialize() {
    try {
      // Initialize Redis client
      this.redisClient = createClient({
        host: process.env.REDIS_HOST || 'redis',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        db: 3, // Use separate DB for economic analysis
        retry_unfulfilled_commands: true,
        retry_delay_on_cluster_down: 300,
        retry_delay_on_failover: 100,
        max_attempts: 3
      });

      this.redisClient.on('error', (err) => {
        this.logger.error('Redis connection error:', err);
      });

      await this.redisClient.connect();

      // Initialize PostgreSQL client
      this.pgClient = new Client({
        host: process.env.POSTGRES_HOST || 'postgresql',
        port: process.env.POSTGRES_PORT || 5432,
        database: process.env.POSTGRES_DB || 'political_conversations',
        user: process.env.POSTGRES_USER || 'n8n_user',
        password: process.env.POSTGRES_PASSWORD,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        connectionTimeoutMillis: 5000,
        idleTimeoutMillis: 30000,
        max: 20
      });

      await this.pgClient.connect();

      // Initialize database schema
      await this.initializeDatabase();

      // Setup session store with Redis
      this.app.use(session({
        store: new RedisStore({ client: this.redisClient }),
        secret: this.securityConfig.sessionSecret,
        resave: false,
        saveUninitialized: false,
        rolling: true,
        cookie: {
          secure: process.env.NODE_ENV === 'production',
          httpOnly: true,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
          sameSite: 'strict'
        }
      }));

      // Schedule data refresh tasks
      this.scheduleDataRefreshTasks();

      this.logger.info('Economic Analysis MCP Server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Economic Analysis MCP Server:', error);
      throw error;
    }
  }

  async initializeDatabase() {
    const queries = [
      // Economic indicators table
      `CREATE TABLE IF NOT EXISTS economic_indicators (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        indicator_code VARCHAR(100) NOT NULL,
        indicator_name VARCHAR(255) NOT NULL,
        source VARCHAR(100) NOT NULL,
        frequency VARCHAR(20) NOT NULL,
        units VARCHAR(100),
        seasonal_adjustment VARCHAR(100),
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Economic data points table
      `CREATE TABLE IF NOT EXISTS economic_data_points (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        indicator_id UUID NOT NULL REFERENCES economic_indicators(id),
        date DATE NOT NULL,
        value NUMERIC NOT NULL,
        revision_date TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Policy impact assessments table
      `CREATE TABLE IF NOT EXISTS policy_impact_assessments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        policy_description TEXT NOT NULL,
        policy_type VARCHAR(100) NOT NULL,
        time_horizon VARCHAR(50) NOT NULL,
        affected_sectors TEXT[],
        budget_impact NUMERIC,
        implementation_timeline TEXT,
        assessment_results JSONB NOT NULL,
        confidence_score NUMERIC CHECK (confidence_score >= 0 AND confidence_score <= 1),
        created_by VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Budget analyses table
      `CREATE TABLE IF NOT EXISTS budget_analyses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        budget_type VARCHAR(100) NOT NULL,
        jurisdiction VARCHAR(255) NOT NULL,
        fiscal_year INTEGER NOT NULL,
        categories TEXT[],
        analysis_type VARCHAR(100) NOT NULL,
        budget_data JSONB NOT NULL,
        analysis_results JSONB NOT NULL,
        total_budget NUMERIC,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Market research table
      `CREATE TABLE IF NOT EXISTS market_research (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        market_type VARCHAR(100) NOT NULL,
        geographic_scope VARCHAR(100) NOT NULL,
        time_period VARCHAR(100) NOT NULL,
        sector VARCHAR(255),
        research_data JSONB NOT NULL,
        key_findings TEXT[],
        trends_identified TEXT[],
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Cost-benefit analyses table
      `CREATE TABLE IF NOT EXISTS cost_benefit_analyses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        project_description TEXT NOT NULL,
        project_type VARCHAR(100) NOT NULL,
        costs JSONB NOT NULL,
        benefits JSONB NOT NULL,
        time_horizon INTEGER NOT NULL,
        discount_rate NUMERIC NOT NULL,
        npv NUMERIC,
        bcr NUMERIC,
        irr NUMERIC,
        payback_period NUMERIC,
        analysis_results JSONB NOT NULL,
        sensitivity_analysis JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Economic forecasts table
      `CREATE TABLE IF NOT EXISTS economic_forecasts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        indicator_id UUID NOT NULL REFERENCES economic_indicators(id),
        forecast_method VARCHAR(100) NOT NULL,
        forecast_horizon INTEGER NOT NULL,
        forecast_data JSONB NOT NULL,
        confidence_intervals JSONB,
        model_metrics JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const query of queries) {
      await this.pgClient.query(query);
    }

    // Create indexes for performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_economic_indicators_code ON economic_indicators(indicator_code)',
      'CREATE INDEX IF NOT EXISTS idx_economic_data_points_indicator ON economic_data_points(indicator_id)',
      'CREATE INDEX IF NOT EXISTS idx_economic_data_points_date ON economic_data_points(date)',
      'CREATE INDEX IF NOT EXISTS idx_policy_assessments_type ON policy_impact_assessments(policy_type)',
      'CREATE INDEX IF NOT EXISTS idx_budget_analyses_type_year ON budget_analyses(budget_type, fiscal_year)',
      'CREATE INDEX IF NOT EXISTS idx_market_research_type ON market_research(market_type)',
      'CREATE INDEX IF NOT EXISTS idx_cost_benefit_type ON cost_benefit_analyses(project_type)',
      'CREATE INDEX IF NOT EXISTS idx_economic_forecasts_indicator ON economic_forecasts(indicator_id)'
    ];

    for (const index of indexes) {
      await this.pgClient.query(index);
    }

    this.logger.info('Economic analysis database schema initialized successfully');
  }

  setupMCPTools() {
    // Tool: Economic Indicator Analysis
    this.server.addTool({
      name: 'economic_indicator_analysis',
      description: 'Analyze economic indicators and trends with comprehensive statistical analysis',
      inputSchema: {
        type: 'object',
        properties: {
          indicators: {
            type: 'array',
            items: { type: 'string' },
            description: 'List of economic indicator codes (e.g., GDP, UNRATE, CPIAUCSL)'
          },
          start_date: {
            type: 'string',
            format: 'date',
            description: 'Start date for analysis (YYYY-MM-DD)'
          },
          end_date: {
            type: 'string',
            format: 'date',
            description: 'End date for analysis (YYYY-MM-DD)'
          },
          frequency: {
            type: 'string',
            enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annual'],
            default: 'monthly',
            description: 'Data frequency'
          },
          transformation: {
            type: 'string',
            enum: ['level', 'growth', 'percent_change', 'log'],
            default: 'level',
            description: 'Data transformation to apply'
          },
          include_forecast: {
            type: 'boolean',
            default: false,
            description: 'Include economic forecasts'
          },
          statistical_tests: {
            type: 'boolean',
            default: true,
            description: 'Include statistical tests and correlations'
          }
        },
        required: ['indicators']
      }
    }, this.economicIndicatorAnalysis.bind(this));

    // Tool: Policy Impact Assessment
    this.server.addTool({
      name: 'policy_impact_assessment',
      description: 'Assess economic impact of policy proposals using economic modeling',
      inputSchema: {
        type: 'object',
        properties: {
          policy_description: {
            type: 'string',
            description: 'Detailed description of the policy proposal'
          },
          policy_type: {
            type: 'string',
            enum: ['fiscal', 'monetary', 'regulatory', 'trade', 'healthcare', 'education', 'infrastructure'],
            description: 'Type of policy being assessed'
          },
          time_horizon: {
            type: 'string',
            enum: ['short_term', 'medium_term', 'long_term'],
            default: 'medium_term',
            description: 'Time horizon for impact assessment'
          },
          affected_sectors: {
            type: 'array',
            items: { type: 'string' },
            description: 'Economic sectors affected by the policy'
          },
          budget_impact: {
            type: 'number',
            description: 'Estimated budget impact in dollars'
          },
          implementation_timeline: {
            type: 'string',
            description: 'Timeline for policy implementation'
          },
          baseline_scenario: {
            type: 'object',
            description: 'Baseline economic scenario for comparison'
          }
        },
        required: ['policy_description', 'policy_type']
      }
    }, this.policyImpactAssessment.bind(this));

    // Tool: Budget Analysis
    this.server.addTool({
      name: 'budget_analysis',
      description: 'Analyze government budgets and spending patterns with detailed breakdown',
      inputSchema: {
        type: 'object',
        properties: {
          budget_type: {
            type: 'string',
            enum: ['federal', 'state', 'local', 'department', 'program'],
            description: 'Type of budget to analyze'
          },
          jurisdiction: {
            type: 'string',
            description: 'Government jurisdiction (e.g., United States, California, New York City)'
          },
          fiscal_year: {
            type: 'integer',
            minimum: 2000,
            maximum: 2050,
            description: 'Fiscal year for analysis'
          },
          categories: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific budget categories to analyze'
          },
          analysis_type: {
            type: 'string',
            enum: ['trend', 'composition', 'efficiency', 'comparison'],
            default: 'trend',
            description: 'Type of budget analysis to perform'
          },
          comparison_years: {
            type: 'array',
            items: { type: 'integer' },
            description: 'Additional years for comparison analysis'
          }
        },
        required: ['budget_type', 'jurisdiction', 'fiscal_year']
      }
    }, this.budgetAnalysis.bind(this));

    // Tool: Market Research
    this.server.addTool({
      name: 'market_research',
      description: 'Research market conditions and economic data across various sectors',
      inputSchema: {
        type: 'object',
        properties: {
          market_type: {
            type: 'string',
            enum: ['labor', 'housing', 'financial', 'commodity', 'sector'],
            description: 'Type of market to research'
          },
          geographic_scope: {
            type: 'string',
            enum: ['national', 'regional', 'state', 'metro', 'local'],
            default: 'national',
            description: 'Geographic scope of research'
          },
          time_period: {
            type: 'string',
            enum: ['current', 'historical', 'forecast'],
            default: 'current',
            description: 'Time period for market research'
          },
          sector: {
            type: 'string',
            description: 'Specific sector or industry to focus on'
          },
          metrics: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific metrics to analyze'
          },
          include_trends: {
            type: 'boolean',
            default: true,
            description: 'Include trend analysis'
          },
          competitive_analysis: {
            type: 'boolean',
            default: false,
            description: 'Include competitive market analysis'
          }
        },
        required: ['market_type']
      }
    }, this.marketResearch.bind(this));

    // Tool: Cost-Benefit Analysis
    this.server.addTool({
      name: 'cost_benefit_analysis',
      description: 'Perform comprehensive economic cost-benefit analysis for projects and policies',
      inputSchema: {
        type: 'object',
        properties: {
          project_description: {
            type: 'string',
            description: 'Detailed description of the project or policy'
          },
          project_type: {
            type: 'string',
            enum: ['infrastructure', 'social_program', 'regulation', 'technology', 'education', 'healthcare'],
            description: 'Type of project being analyzed'
          },
          costs: {
            type: 'object',
            properties: {
              initial: { type: 'number', minimum: 0 },
              annual_operating: { type: 'number', minimum: 0 },
              maintenance: { type: 'number', minimum: 0 },
              opportunity: { type: 'number', minimum: 0 }
            },
            required: ['initial'],
            description: 'Cost structure of the project'
          },
          benefits: {
            type: 'object',
            properties: {
              quantifiable: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    type: { type: 'string' },
                    annual_value: { type: 'number' },
                    description: { type: 'string' }
                  },
                  required: ['type', 'annual_value']
                }
              },
              qualitative: {
                type: 'array',
                items: { type: 'string' }
              }
            },
            description: 'Benefit structure of the project'
          },
          time_horizon: {
            type: 'integer',
            minimum: 1,
            maximum: 50,
            default: 10,
            description: 'Analysis time horizon in years'
          },
          discount_rate: {
            type: 'number',
            minimum: 0,
            maximum: 0.15,
            default: 0.03,
            description: 'Discount rate for NPV calculation'
          },
          sensitivity_analysis: {
            type: 'boolean',
            default: true,
            description: 'Include sensitivity analysis'
          }
        },
        required: ['project_description', 'project_type', 'costs', 'benefits']
      }
    }, this.costBenefitAnalysis.bind(this));
  }

  // MCP Tool Implementations

  async economicIndicatorAnalysis(params) {
    try {
      const { indicators, start_date, end_date, frequency = 'monthly', transformation = 'level', include_forecast = false, statistical_tests = true } = params;
      
      this.logger.info(`Analyzing economic indicators: ${indicators.join(', ')}`);
      
      // Validate parameters
      const { error } = this.validationSchemas.indicatorAnalysis.validate(params);
      if (error) {
        throw new Error(`Invalid parameters: ${error.details[0].message}`);
      }

      const analysisId = uuidv4();
      const results = {
        analysis_id: analysisId,
        indicators: [],
        correlations: {},
        statistical_summary: {},
        trends: {},
        forecasts: {}
      };

      // Fetch data for each indicator
      for (const indicatorCode of indicators) {
        const indicatorData = await this.fetchEconomicIndicator(indicatorCode, start_date, end_date, frequency);
        
        if (indicatorData && indicatorData.data.length > 0) {
          // Apply transformation
          const transformedData = this.applyDataTransformation(indicatorData.data, transformation);
          
          // Calculate statistical metrics
          const stats = this.calculateStatisticalMetrics(transformedData);
          
          // Identify trends
          const trends = this.identifyTrends(transformedData);
          
          // Generate forecast if requested
          let forecast = null;
          if (include_forecast) {
            forecast = await this.generateForecast(indicatorCode, transformedData);
          }

          results.indicators.push({
            code: indicatorCode,
            name: indicatorData.name,
            source: indicatorData.source,
            data: transformedData,
            statistics: stats,
            trends: trends,
            forecast: forecast
          });
        }
      }

      // Calculate correlations between indicators if multiple provided
      if (indicators.length > 1 && statistical_tests) {
        results.correlations = await this.calculateCorrelations(results.indicators);
        results.statistical_summary = this.generateStatisticalSummary(results.indicators);
      }

      // Store analysis results
      await this.storeAnalysisResults(analysisId, 'economic_indicator_analysis', results);

      return {
        success: true,
        analysis_id: analysisId,
        results: results,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error in economic indicator analysis:', error);
      throw error;
    }
  }

  async policyImpactAssessment(params) {
    try {
      const { policy_description, policy_type, time_horizon = 'medium_term', affected_sectors = [], budget_impact, implementation_timeline, baseline_scenario } = params;
      
      this.logger.info(`Assessing policy impact: ${policy_type} - ${policy_description.substring(0, 100)}...`);
      
      // Validate parameters
      const { error } = this.validationSchemas.policyImpactAssessment.validate(params);
      if (error) {
        throw new Error(`Invalid parameters: ${error.details[0].message}`);
      }

      const assessmentId = uuidv4();
      
      // Economic modeling based on policy type
      const economicModel = await this.buildEconomicModel(policy_type, baseline_scenario);
      
      // Simulate policy impact
      const impactScenarios = await this.simulatePolicyImpact(
        economicModel,
        policy_type,
        policy_description,
        time_horizon,
        affected_sectors,
        budget_impact
      );

      // Analyze macroeconomic effects
      const macroEffects = await this.analyzeMacroeconomicEffects(impactScenarios, time_horizon);
      
      // Assess sectoral impacts
      const sectoralImpacts = await this.assessSectoralImpacts(affected_sectors, impactScenarios);
      
      // Calculate confidence score
      const confidenceScore = this.calculateConfidenceScore(impactScenarios, policy_type);

      const results = {
        assessment_id: assessmentId,
        policy_summary: {
          description: policy_description,
          type: policy_type,
          time_horizon: time_horizon,
          affected_sectors: affected_sectors,
          budget_impact: budget_impact,
          implementation_timeline: implementation_timeline
        },
        impact_scenarios: impactScenarios,
        macroeconomic_effects: macroEffects,
        sectoral_impacts: sectoralImpacts,
        confidence_score: confidenceScore,
        key_findings: this.extractKeyFindings(impactScenarios, macroEffects),
        recommendations: this.generatePolicyRecommendations(impactScenarios, policy_type)
      };

      // Store assessment results
      await this.pgClient.query(
        `INSERT INTO policy_impact_assessments 
         (id, policy_description, policy_type, time_horizon, affected_sectors, budget_impact, implementation_timeline, assessment_results, confidence_score)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [assessmentId, policy_description, policy_type, time_horizon, affected_sectors, budget_impact, implementation_timeline, results, confidenceScore]
      );

      return {
        success: true,
        assessment_id: assessmentId,
        results: results,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error in policy impact assessment:', error);
      throw error;
    }
  }

  async budgetAnalysis(params) {
    try {
      const { budget_type, jurisdiction, fiscal_year, categories = [], analysis_type = 'trend', comparison_years = [] } = params;
      
      this.logger.info(`Analyzing ${budget_type} budget for ${jurisdiction}, FY ${fiscal_year}`);
      
      // Validate parameters
      const { error } = this.validationSchemas.budgetAnalysis.validate(params);
      if (error) {
        throw new Error(`Invalid parameters: ${error.details[0].message}`);
      }

      const analysisId = uuidv4();
      
      // Fetch budget data
      const budgetData = await this.fetchBudgetData(budget_type, jurisdiction, fiscal_year, categories);
      
      // Perform analysis based on type
      let analysisResults = {};
      
      switch (analysis_type) {
        case 'trend':
          analysisResults = await this.performTrendAnalysis(budgetData, comparison_years);
          break;
        case 'composition':
          analysisResults = await this.performCompositionAnalysis(budgetData);
          break;
        case 'efficiency':
          analysisResults = await this.performEfficiencyAnalysis(budgetData, jurisdiction);
          break;
        case 'comparison':
          analysisResults = await this.performComparisonAnalysis(budgetData, comparison_years);
          break;
      }

      // Calculate budget metrics
      const budgetMetrics = this.calculateBudgetMetrics(budgetData);
      
      // Identify budget insights
      const insights = this.generateBudgetInsights(budgetData, analysisResults, analysis_type);

      const results = {
        analysis_id: analysisId,
        budget_summary: {
          type: budget_type,
          jurisdiction: jurisdiction,
          fiscal_year: fiscal_year,
          total_budget: budgetData.total_budget,
          categories: categories
        },
        budget_data: budgetData,
        analysis_results: analysisResults,
        metrics: budgetMetrics,
        insights: insights,
        recommendations: this.generateBudgetRecommendations(analysisResults, budget_type)
      };

      // Store analysis results
      await this.pgClient.query(
        `INSERT INTO budget_analyses 
         (id, budget_type, jurisdiction, fiscal_year, categories, analysis_type, budget_data, analysis_results, total_budget)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [analysisId, budget_type, jurisdiction, fiscal_year, categories, analysis_type, budgetData, analysisResults, budgetData.total_budget]
      );

      return {
        success: true,
        analysis_id: analysisId,
        results: results,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error in budget analysis:', error);
      throw error;
    }
  }

  async marketResearch(params) {
    try {
      const { market_type, geographic_scope = 'national', time_period = 'current', sector, metrics = [], include_trends = true, competitive_analysis = false } = params;
      
      this.logger.info(`Researching ${market_type} market with ${geographic_scope} scope`);
      
      // Validate parameters
      const { error } = this.validationSchemas.marketResearch.validate(params);
      if (error) {
        throw new Error(`Invalid parameters: ${error.details[0].message}`);
      }

      const researchId = uuidv4();
      
      // Fetch market data based on market type
      const marketData = await this.fetchMarketData(market_type, geographic_scope, time_period, sector, metrics);
      
      // Analyze market conditions
      const marketConditions = await this.analyzeMarketConditions(marketData, market_type);
      
      // Identify trends if requested
      let trends = {};
      if (include_trends) {
        trends = await this.identifyMarketTrends(marketData, market_type);
      }
      
      // Perform competitive analysis if requested
      let competitiveAnalysis = {};
      if (competitive_analysis && sector) {
        competitiveAnalysis = await this.performCompetitiveAnalysis(sector, geographic_scope);
      }
      
      // Generate key findings
      const keyFindings = this.extractMarketFindings(marketData, marketConditions, trends);
      
      const results = {
        research_id: researchId,
        market_summary: {
          type: market_type,
          geographic_scope: geographic_scope,
          time_period: time_period,
          sector: sector
        },
        market_data: marketData,
        conditions: marketConditions,
        trends: trends,
        competitive_analysis: competitiveAnalysis,
        key_findings: keyFindings,
        opportunities: this.identifyMarketOpportunities(marketData, trends),
        risks: this.identifyMarketRisks(marketData, trends)
      };

      // Store research results
      await this.pgClient.query(
        `INSERT INTO market_research 
         (id, market_type, geographic_scope, time_period, sector, research_data, key_findings, trends_identified)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
        [researchId, market_type, geographic_scope, time_period, sector, results, keyFindings, Object.keys(trends)]
      );

      return {
        success: true,
        research_id: researchId,
        results: results,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error in market research:', error);
      throw error;
    }
  }

  async costBenefitAnalysis(params) {
    try {
      const { project_description, project_type, costs, benefits, time_horizon = 10, discount_rate = 0.03, sensitivity_analysis = true } = params;
      
      this.logger.info(`Performing cost-benefit analysis for ${project_type} project`);
      
      // Validate parameters
      const { error } = this.validationSchemas.costBenefitAnalysis.validate(params);
      if (error) {
        throw new Error(`Invalid parameters: ${error.details[0].message}`);
      }

      const analysisId = uuidv4();
      
      // Calculate present values
      const presentValueCosts = this.calculatePresentValue(costs, time_horizon, discount_rate);
      const presentValueBenefits = this.calculatePresentValue(benefits, time_horizon, discount_rate);
      
      // Calculate key metrics
      const npv = presentValueBenefits - presentValueCosts;
      const bcr = presentValueCosts > 0 ? presentValueBenefits / presentValueCosts : 0;
      const irr = this.calculateIRR(costs, benefits, time_horizon);
      const paybackPeriod = this.calculatePaybackPeriod(costs, benefits);
      
      // Perform sensitivity analysis if requested
      let sensitivityResults = {};
      if (sensitivity_analysis) {
        sensitivityResults = await this.performSensitivityAnalysis(
          costs, benefits, time_horizon, discount_rate, npv, bcr
        );
      }
      
      // Generate recommendations
      const recommendations = this.generateCBARecommendations(npv, bcr, irr, paybackPeriod, sensitivityResults);
      
      const results = {
        analysis_id: analysisId,
        project_summary: {
          description: project_description,
          type: project_type,
          time_horizon: time_horizon,
          discount_rate: discount_rate
        },
        financial_metrics: {
          npv: npv,
          bcr: bcr,
          irr: irr,
          payback_period: paybackPeriod,
          present_value_costs: presentValueCosts,
          present_value_benefits: presentValueBenefits
        },
        cost_breakdown: this.analyzeCostStructure(costs, time_horizon, discount_rate),
        benefit_breakdown: this.analyzeBenefitStructure(benefits, time_horizon, discount_rate),
        sensitivity_analysis: sensitivityResults,
        risk_assessment: this.assessProjectRisks(costs, benefits, project_type),
        recommendations: recommendations
      };

      // Store analysis results
      await this.pgClient.query(
        `INSERT INTO cost_benefit_analyses 
         (id, project_description, project_type, costs, benefits, time_horizon, discount_rate, npv, bcr, irr, payback_period, analysis_results, sensitivity_analysis)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)`,
        [analysisId, project_description, project_type, costs, benefits, time_horizon, discount_rate, npv, bcr, irr, paybackPeriod, results, sensitivityResults]
      );

      return {
        success: true,
        analysis_id: analysisId,
        results: results,
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error('Error in cost-benefit analysis:', error);
      throw error;
    }
  }

  // Helper Methods for Data Fetching and Processing

  async fetchEconomicIndicator(indicatorCode, startDate, endDate, frequency) {
    try {
      // Check cache first
      const cacheKey = `indicator:${indicatorCode}:${startDate}:${endDate}:${frequency}`;
      const cached = await this.redisClient.get(cacheKey);
      
      if (cached) {
        return JSON.parse(cached);
      }

      // Try FRED API first
      let data = await this.fetchFromFRED(indicatorCode, startDate, endDate, frequency);
      
      // Fallback to other sources if FRED doesn't have the data
      if (!data) {
        data = await this.fetchFromAlternativeSources(indicatorCode, startDate, endDate, frequency);
      }

      if (data) {
        // Cache the result
        await this.redisClient.setex(cacheKey, this.analysisConfig.cacheTTL, JSON.stringify(data));
      }

      return data;
    } catch (error) {
      this.logger.error(`Error fetching indicator ${indicatorCode}:`, error);
      return null;
    }
  }

  async fetchFromFRED(indicatorCode, startDate, endDate, frequency) {
    if (!this.dataSources.fred.apiKey) {
      return null;
    }

    try {
      const response = await axios.get(`${this.dataSources.fred.baseUrl}/series/observations`, {
        params: {
          series_id: indicatorCode,
          api_key: this.dataSources.fred.apiKey,
          file_type: 'json',
          observation_start: startDate,
          observation_end: endDate,
          frequency: frequency === 'monthly' ? 'm' : frequency === 'quarterly' ? 'q' : frequency === 'annual' ? 'a' : 'd'
        }
      });

      if (response.data && response.data.observations) {
        return {
          name: indicatorCode, // Would be enhanced with actual series name
          source: 'FRED',
          data: response.data.observations.map(obs => ({
            date: obs.date,
            value: parseFloat(obs.value) || null
          })).filter(item => item.value !== null)
        };
      }
    } catch (error) {
      this.logger.error(`FRED API error for ${indicatorCode}:`, error.message);
    }

    return null;
  }

  async fetchFromAlternativeSources(indicatorCode, startDate, endDate, frequency) {
    // Implement other data sources (World Bank, BEA, Census, etc.)
    // This is a placeholder for additional data source implementations
    return null;
  }

  applyDataTransformation(data, transformation) {
    switch (transformation) {
      case 'growth':
        return this.calculateGrowthRates(data);
      case 'percent_change':
        return this.calculatePercentChange(data);
      case 'log':
        return data.map(item => ({
          ...item,
          value: item.value > 0 ? Math.log(item.value) : null
        }));
      default:
        return data;
    }
  }

  calculateGrowthRates(data) {
    const result = [];
    for (let i = 1; i < data.length; i++) {
      if (data[i-1].value && data[i].value) {
        result.push({
          date: data[i].date,
          value: ((data[i].value - data[i-1].value) / data[i-1].value) * 100
        });
      }
    }
    return result;
  }

  calculatePercentChange(data) {
    const result = [];
    for (let i = 1; i < data.length; i++) {
      if (data[i-1].value && data[i].value) {
        result.push({
          date: data[i].date,
          value: ((data[i].value - data[i-1].value) / data[i-1].value) * 100
        });
      }
    }
    return result;
  }

  calculateStatisticalMetrics(data) {
    const values = data.map(item => item.value).filter(v => v !== null);
    
    if (values.length === 0) return {};

    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    const sortedValues = [...values].sort((a, b) => a - b);
    const median = sortedValues.length % 2 === 0 
      ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
      : sortedValues[Math.floor(sortedValues.length / 2)];

    return {
      count: values.length,
      mean: mean,
      median: median,
      std_dev: stdDev,
      variance: variance,
      min: Math.min(...values),
      max: Math.max(...values),
      range: Math.max(...values) - Math.min(...values)
    };
  }

  identifyTrends(data) {
    if (data.length < 2) return { trend: 'insufficient_data' };

    const values = data.map(item => item.value).filter(v => v !== null);
    const n = values.length;
    
    // Simple linear regression for trend identification
    const xValues = Array.from({length: n}, (_, i) => i);
    const xMean = xValues.reduce((a, b) => a + b, 0) / n;
    const yMean = values.reduce((a, b) => a + b, 0) / n;
    
    const slope = xValues.reduce((sum, x, i) => sum + (x - xMean) * (values[i] - yMean), 0) /
                  xValues.reduce((sum, x) => sum + Math.pow(x - xMean, 2), 0);
    
    const trend = slope > 0.01 ? 'increasing' : slope < -0.01 ? 'decreasing' : 'stable';
    
    return {
      trend: trend,
      slope: slope,
      strength: Math.abs(slope),
      correlation: this.calculateCorrelationCoefficient(xValues, values)
    };
  }

  calculateCorrelationCoefficient(x, y) {
    const n = x.length;
    const xMean = x.reduce((a, b) => a + b, 0) / n;
    const yMean = y.reduce((a, b) => a + b, 0) / n;
    
    const numerator = x.reduce((sum, xi, i) => sum + (xi - xMean) * (y[i] - yMean), 0);
    const denominatorX = Math.sqrt(x.reduce((sum, xi) => sum + Math.pow(xi - xMean, 2), 0));
    const denominatorY = Math.sqrt(y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0));
    
    return numerator / (denominatorX * denominatorY);
  }

  async generateForecast(indicatorCode, data) {
    // Implement time series forecasting (ARIMA, exponential smoothing, etc.)
    // This is a simplified implementation
    if (data.length < 12) {
      return { error: 'Insufficient data for forecasting' };
    }

    const recentValues = data.slice(-12).map(item => item.value);
    const trend = this.identifyTrends(data.slice(-12));
    
    // Simple trend extrapolation forecast
    const lastValue = recentValues[recentValues.length - 1];
    const forecast = [];
    
    for (let i = 1; i <= 6; i++) { // 6-period forecast
      const forecastValue = lastValue + (trend.slope * i);
      forecast.push({
        period: i,
        value: forecastValue,
        confidence_lower: forecastValue * 0.95,
        confidence_upper: forecastValue * 1.05
      });
    }

    return {
      method: 'trend_extrapolation',
      periods: 6,
      forecast: forecast,
      model_metrics: {
        trend_strength: trend.strength,
        correlation: trend.correlation
      }
    };
  }

  async calculateCorrelations(indicators) {
    const correlations = {};
    
    for (let i = 0; i < indicators.length; i++) {
      for (let j = i + 1; j < indicators.length; j++) {
        const indicator1 = indicators[i];
        const indicator2 = indicators[j];
        
        // Align data by date
        const alignedData = this.alignDataByDate(indicator1.data, indicator2.data);
        
        if (alignedData.x.length > 1) {
          const correlation = this.calculateCorrelationCoefficient(alignedData.x, alignedData.y);
          correlations[`${indicator1.code}_${indicator2.code}`] = correlation;
        }
      }
    }
    
    return correlations;
  }

  alignDataByDate(data1, data2) {
    const aligned = { x: [], y: [] };
    const data2Map = new Map(data2.map(item => [item.date, item.value]));
    
    for (const item1 of data1) {
      if (data2Map.has(item1.date)) {
        aligned.x.push(item1.value);
        aligned.y.push(data2Map.get(item1.date));
      }
    }
    
    return aligned;
  }

  generateStatisticalSummary(indicators) {
    return {
      total_indicators: indicators.length,
      date_range: {
        start: Math.min(...indicators.map(ind => new Date(ind.data[0]?.date || 0))),
        end: Math.max(...indicators.map(ind => new Date(ind.data[ind.data.length - 1]?.date || 0)))
      },
      data_points: indicators.reduce((total, ind) => total + ind.data.length, 0),
      completeness: indicators.map(ind => ({
        indicator: ind.code,
        completeness: (ind.data.filter(item => item.value !== null).length / ind.data.length) * 100
      }))
    };
  }

  // Additional helper methods would be implemented for:
  // - buildEconomicModel
  // - simulatePolicyImpact
  // - analyzeMacroeconomicEffects
  // - assessSectoralImpacts
  // - calculateConfidenceScore
  // - fetchBudgetData
  // - performTrendAnalysis, performCompositionAnalysis, etc.
  // - fetchMarketData
  // - analyzeMarketConditions
  // - calculatePresentValue
  // - calculateIRR
  // - performSensitivityAnalysis
  // ... and many more

  // Placeholder implementations for brevity
  async buildEconomicModel(policyType, baselineScenario) {
    return { model_type: policyType, baseline: baselineScenario || {} };
  }

  async simulatePolicyImpact(model, policyType, description, timeHorizon, sectors, budgetImpact) {
    return {
      gdp_impact: budgetImpact ? budgetImpact * 0.8 : 0,
      employment_impact: sectors.includes('labor') ? 0.02 : 0,
      inflation_impact: policyType === 'fiscal' ? 0.001 : 0
    };
  }

  async analyzeMacroeconomicEffects(scenarios, timeHorizon) {
    return {
      gdp_effect: scenarios.gdp_impact || 0,
      employment_effect: scenarios.employment_impact || 0,
      inflation_effect: scenarios.inflation_impact || 0,
      time_horizon: timeHorizon
    };
  }

  async assessSectoralImpacts(sectors, scenarios) {
    return sectors.reduce((acc, sector) => {
      acc[sector] = { impact: 'moderate', confidence: 0.7 };
      return acc;
    }, {});
  }

  calculateConfidenceScore(scenarios, policyType) {
    // Simple confidence scoring based on data availability and model type
    return 0.75; // Placeholder
  }

  extractKeyFindings(scenarios, macroEffects) {
    return [
      `Estimated GDP impact: ${scenarios.gdp_impact || 0}%`,
      `Employment effect: ${macroEffects.employment_effect || 0}%`,
      `Inflation impact: ${macroEffects.inflation_effect || 0}%`
    ];
  }

  generatePolicyRecommendations(scenarios, policyType) {
    return [
      'Monitor implementation closely',
      'Consider phased rollout',
      'Establish clear success metrics'
    ];
  }

  async fetchBudgetData(budgetType, jurisdiction, fiscalYear, categories) {
    // Placeholder implementation - would fetch from government APIs
    return {
      total_budget: **********,
      categories: {
        defense: 300000000,
        healthcare: 250000000,
        education: 200000000,
        infrastructure: 150000000,
        other: 100000000
      },
      fiscal_year: fiscalYear,
      jurisdiction: jurisdiction
    };
  }

  async performTrendAnalysis(budgetData, comparisonYears) {
    return {
      growth_rate: 0.03,
      trend_direction: 'increasing',
      volatility: 0.15
    };
  }

  async performCompositionAnalysis(budgetData) {
    const total = budgetData.total_budget;
    return Object.keys(budgetData.categories).reduce((acc, category) => {
      acc[category] = {
        amount: budgetData.categories[category],
        percentage: (budgetData.categories[category] / total) * 100
      };
      return acc;
    }, {});
  }

  async performEfficiencyAnalysis(budgetData, jurisdiction) {
    return {
      efficiency_score: 0.75,
      benchmarks: {},
      recommendations: []
    };
  }

  async performComparisonAnalysis(budgetData, comparisonYears) {
    return {
      year_over_year: {},
      trend_analysis: {}
    };
  }

  calculateBudgetMetrics(budgetData) {
    return {
      per_capita: budgetData.total_budget / 1000000, // Assuming 1M population
      debt_to_budget_ratio: 0.15,
      efficiency_metrics: {}
    };
  }

  generateBudgetInsights(budgetData, analysisResults, analysisType) {
    return [
      'Budget shows steady growth',
      'Healthcare spending is increasing',
      'Infrastructure investment needed'
    ];
  }

  generateBudgetRecommendations(analysisResults, budgetType) {
    return [
      'Increase transparency in budget allocation',
      'Consider performance-based budgeting',
      'Review spending efficiency'
    ];
  }

  async fetchMarketData(marketType, geographicScope, timePeriod, sector, metrics) {
    // Placeholder - would fetch from various market data sources
    return {
      market_size: **********,
      growth_rate: 0.05,
      key_players: ['Company A', 'Company B'],
      market_share: { 'Company A': 0.3, 'Company B': 0.25 }
    };
  }

  async analyzeMarketConditions(marketData, marketType) {
    return {
      condition: 'stable',
      growth_outlook: 'positive',
      risk_level: 'medium'
    };
  }

  async identifyMarketTrends(marketData, marketType) {
    return {
      primary_trend: 'digital_transformation',
      secondary_trends: ['sustainability', 'automation'],
      trend_strength: 'strong'
    };
  }

  async performCompetitiveAnalysis(sector, geographicScope) {
    return {
      competition_level: 'high',
      market_concentration: 'moderate',
      barriers_to_entry: 'medium'
    };
  }

  extractMarketFindings(marketData, conditions, trends) {
    return [
      'Market showing steady growth',
      'Digital transformation driving change',
      'Competition remains intense'
    ];
  }

  identifyMarketOpportunities(marketData, trends) {
    return [
      'Emerging technology adoption',
      'Underserved market segments',
      'Partnership opportunities'
    ];
  }

  identifyMarketRisks(marketData, trends) {
    return [
      'Regulatory changes',
      'Economic downturn impact',
      'Technology disruption'
    ];
  }

  calculatePresentValue(cashFlows, timeHorizon, discountRate) {
    if (typeof cashFlows === 'object' && !Array.isArray(cashFlows)) {
      // Handle cost/benefit object structure
      let totalPV = 0;
      if (cashFlows.initial) totalPV += cashFlows.initial;
      if (cashFlows.annual_operating) {
        for (let year = 1; year <= timeHorizon; year++) {
          totalPV += cashFlows.annual_operating / Math.pow(1 + discountRate, year);
        }
      }
      return totalPV;
    }
    return 0; // Simplified
  }

  calculateIRR(costs, benefits, timeHorizon) {
    // Simplified IRR calculation
    return 0.08; // 8% placeholder
  }

  calculatePaybackPeriod(costs, benefits) {
    // Simplified payback period calculation
    return 5; // 5 years placeholder
  }

  async performSensitivityAnalysis(costs, benefits, timeHorizon, discountRate, npv, bcr) {
    return {
      discount_rate_sensitivity: {
        '2%': { npv: npv * 1.1, bcr: bcr * 1.1 },
        '4%': { npv: npv * 0.9, bcr: bcr * 0.9 }
      },
      cost_sensitivity: {
        '+10%': { npv: npv * 0.9, bcr: bcr * 0.9 },
        '-10%': { npv: npv * 1.1, bcr: bcr * 1.1 }
      }
    };
  }

  generateCBARecommendations(npv, bcr, irr, paybackPeriod, sensitivityResults) {
    const recommendations = [];
    
    if (npv > 0) {
      recommendations.push('Project has positive net present value - recommended for approval');
    }
    
    if (bcr > 1) {
      recommendations.push('Benefit-cost ratio exceeds 1.0 - project is economically viable');
    }
    
    if (paybackPeriod < 7) {
      recommendations.push('Reasonable payback period supports project viability');
    }
    
    return recommendations;
  }

  analyzeCostStructure(costs, timeHorizon, discountRate) {
    return {
      initial_costs: costs.initial || 0,
      operating_costs_pv: this.calculatePresentValue({ annual_operating: costs.annual_operating }, timeHorizon, discountRate),
      maintenance_costs_pv: this.calculatePresentValue({ annual_operating: costs.maintenance }, timeHorizon, discountRate)
    };
  }

  analyzeBenefitStructure(benefits, timeHorizon, discountRate) {
    const result = {
      quantifiable_benefits: 0,
      qualitative_benefits: benefits.qualitative || []
    };
    
    if (benefits.quantifiable) {
      result.quantifiable_benefits = benefits.quantifiable.reduce((total, benefit) => {
        return total + this.calculatePresentValue({ annual_operating: benefit.annual_value }, timeHorizon, discountRate);
      }, 0);
    }
    
    return result;
  }

  assessProjectRisks(costs, benefits, projectType) {
    return {
      financial_risk: 'medium',
      implementation_risk: 'medium',
      market_risk: 'low',
      regulatory_risk: projectType === 'regulation' ? 'high' : 'low'
    };
  }

  async storeAnalysisResults(analysisId, analysisType, results) {
    const cacheKey = `analysis:${analysisType}:${analysisId}`;
    await this.redisClient.setex(cacheKey, this.analysisConfig.cacheTTL * 2, JSON.stringify(results));
  }

  setupDataCache() {
    // Setup cache management for economic data
    this.dataCache = new Map();
    
    // Schedule cache cleanup every hour
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.dataCache.entries()) {
        if (now - entry.timestamp > this.analysisConfig.cacheTTL * 1000) {
          this.dataCache.delete(key);
        }
      }
    }, 60 * 60 * 1000); // 1 hour
  }

  setupExpressRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'economic-analysis-mcp',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        uptime: process.uptime(),
        data_sources: {
          fred: !!this.dataSources.fred.apiKey,
          bea: !!this.dataSources.bea.apiKey,
          census: !!this.dataSources.census.apiKey
        }
      });
    });

    // API status endpoint
    this.app.get('/api/status', (req, res) => {
      res.json({
        database_connected: !!this.pgClient,
        redis_connected: !!this.redisClient,
        last_data_update: new Date().toISOString()
      });
    });
  }

  scheduleDataRefreshTasks() {
    // Refresh key economic indicators daily at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        await this.refreshKeyIndicators();
        this.logger.info('Key economic indicators refreshed successfully');
      } catch (error) {
        this.logger.error('Failed to refresh economic indicators:', error);
      }
    });

    // Clean up old analysis results weekly
    cron.schedule('0 3 * * 0', async () => {
      try {
        await this.cleanupOldAnalyses();
        this.logger.info('Old analysis results cleaned up successfully');
      } catch (error) {
        this.logger.error('Failed to cleanup old analyses:', error);
      }
    });
  }

  async refreshKeyIndicators() {
    const keyIndicators = ['GDP', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS', 'PAYEMS'];
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    for (const indicator of keyIndicators) {
      try {
        const data = await this.fetchEconomicIndicator(indicator, startDate, endDate, 'monthly');
        if (data) {
          // Store/update in database
          await this.updateIndicatorData(indicator, data);
        }
      } catch (error) {
        this.logger.error(`Failed to refresh indicator ${indicator}:`, error);
      }
    }
  }

  async updateIndicatorData(indicatorCode, data) {
    // Update indicator metadata
    await this.pgClient.query(
      `INSERT INTO economic_indicators (indicator_code, indicator_name, source, frequency, last_updated)
       VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
       ON CONFLICT (indicator_code) DO UPDATE SET
       last_updated = CURRENT_TIMESTAMP`,
      [indicatorCode, data.name, data.source, 'monthly']
    );

    // Update data points
    for (const point of data.data) {
      await this.pgClient.query(
        `INSERT INTO economic_data_points (indicator_id, date, value)
         SELECT id, $2, $3 FROM economic_indicators WHERE indicator_code = $1
         ON CONFLICT (indicator_id, date) DO UPDATE SET
         value = EXCLUDED.value`,
        [indicatorCode, point.date, point.value]
      );
    }
  }

  async cleanupOldAnalyses() {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 90); // Keep 90 days

    const tables = [
      'policy_impact_assessments',
      'budget_analyses', 
      'market_research',
      'cost_benefit_analyses'
    ];

    for (const table of tables) {
      await this.pgClient.query(
        `DELETE FROM ${table} WHERE created_at < $1`,
        [cutoffDate]
      );
    }
  }
}

// Initialize and start the server
const server = new EconomicAnalysisMCPServer();

async function start() {
  try {
    await server.initialize();
    await server.server.start();
    
    const port = process.env.MCP_SERVER_PORT || 8091;
    server.app.listen(port, () => {
      server.logger.info(`Economic Analysis MCP Server running on port ${port}`);
    });
    
    console.log('Economic Analysis MCP Server started successfully');
  } catch (error) {
    console.error('Failed to start Economic Analysis MCP Server:', error);
    process.exit(1);
  }
}

start();