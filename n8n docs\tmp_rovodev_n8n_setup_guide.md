# n8n Political Document Processing Workflow - Setup Guide

## Prerequisites Checklist
- ✅ Google Drive folders created (political_in, political_out)
- ✅ AI APIs available (Gemini, OpenAI, Anthropic)
- ❌ n8n installation
- ❌ ChromaDB setup
- ❌ CloudConvert API
- ❌ n8n-mcp-server configuration

## Phase 1: Install n8n

### Option A: Docker Installation (Recommended)
```bash
# Create n8n directory
mkdir n8n-political-workflow
cd n8n-political-workflow

# Create docker-compose.yml for n8n
```

### Option B: NPM Installation
```bash
npm install n8n -g
```

## Phase 2: Install ChromaDB

### Docker Installation
```bash
# ChromaDB for vector storage/RAG
docker run -d --name chromadb -p 8000:8000 chromadb/chroma:latest
```

## Phase 3: CloudConvert Setup
1. Go to https://cloudconvert.com/
2. Sign up for free account (1000 conversions/month free)
3. Get API key from dashboard
4. Test with simple conversion

## Phase 4: n8n-mcp-server Setup
This connects n8n to your AI agents via MCP protocol.

## Phase 5: Workflow Configuration
- Google Drive integration
- AI agent communication
- Document processing logic
- RAG implementation

## Next Steps
Let's start with Phase 1 - which installation method do you prefer?