# Memory Context MCP Server

Advanced memory and context management server for n8n political document processing workflows. Provides sophisticated conversation memory, document context tracking, user preferences, session management, and knowledge graph capabilities.

## 🚀 Features

### Core Memory Tools
- **Conversation Memory Store** - Store and retrieve conversation history with semantic search
- **Document Context Tracker** - Track document contexts across workflow executions  
- **User Preference Manager** - Manage personalized user settings and preferences
- **Session Context Manager** - Handle workflow session state and progress tracking
- **Knowledge Graph Builder** - Build and query knowledge relationships

### Advanced Capabilities
- **Semantic Search** - OpenAI embeddings for intelligent memory retrieval
- **Natural Language Processing** - Sentiment analysis and entity extraction
- **Graph Analytics** - Knowledge relationship modeling and traversal
- **Context Persistence** - PostgreSQL, Redis, and SQLite integration
- **Automated Maintenance** - Scheduled cleanup and optimization

## 📋 MCP Tools Available

### 1. conversation_memory_store
Store and retrieve conversation memories with context analysis.

**Actions:**
- `store` - Store new conversation message with analysis
- `retrieve` - Get conversation history by ID
- `search` - Semantic search across conversations
- `analyze` - Get conversation statistics and insights

**Parameters:**
```json
{
  "action": "store|retrieve|search|analyze",
  "conversation_id": "unique-conversation-id",
  "user_id": "user-identifier", 
  "message_content": "message text or search query",
  "context_type": "question|answer|document_request",
  "limit": 10
}
```

### 2. document_context_tracker
Track and analyze document contexts across workflows.

**Actions:**
- `track` - Analyze and store document context
- `retrieve` - Get document context by ID
- `search` - Find related documents by content
- `analyze` - Get document complexity and sentiment

**Parameters:**
```json
{
  "action": "track|retrieve|search|analyze",
  "document_id": "document-identifier",
  "document_content": "full document text",
  "search_query": "search terms"
}
```

### 3. user_preference_manager
Manage user preferences and personalization.

**Actions:**
- `get` - Retrieve user preferences
- `set` - Store complete preference set
- `update` - Update specific preference
- `analyze` - Analyze preference patterns

**Parameters:**
```json
{
  "action": "get|set|update|analyze",
  "user_id": "user-identifier",
  "preferences": {
    "communication_style": "formal|casual|technical",
    "preferred_topics": ["politics", "policy"],
    "response_length": "short|medium|detailed",
    "technical_level": "basic|intermediate|advanced"
  }
}
```

### 4. session_context_manager
Manage workflow session contexts and state.

**Actions:**
- `create` - Create new session context
- `get` - Retrieve session by ID
- `update` - Update session state
- `cleanup` - Remove old sessions

**Parameters:**
```json
{
  "action": "create|get|update|cleanup",
  "session_id": "session-identifier",
  "user_id": "user-identifier",
  "workflow_context": {"current_step": 1, "data": {}},
  "session_goals": "process political document",
  "progress_state": {"completed": ["step1"], "current": "step2"}
}
```

### 5. knowledge_graph_builder
Build and query knowledge graphs from data.

**Actions:**
- `add_node` - Add entity or concept node
- `add_edge` - Create relationship between nodes
- `query` - Search graph relationships
- `visualize` - Get graph visualization data

**Parameters:**
```json
{
  "action": "add_node|add_edge|query|visualize",
  "node_id": "entity-identifier",
  "node_type": "person|concept|document|topic",
  "from_node": "source-node-id",
  "to_node": "target-node-id", 
  "relationship_type": "mentions|relates_to|authored_by",
  "query": "search terms",
  "depth": 2
}
```

## 🔧 Configuration

### Environment Variables

**Required:**
```bash
# Database Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=memory_context_db
POSTGRES_USER=n8n_user
POSTGRES_PASSWORD=n8n_secure_password

# Redis Configuration  
REDIS_HOST=redis
REDIS_PORT=6379

# OpenAI API (for embeddings)
OPENAI_API_KEY=your_openai_api_key
```

**Optional:**
```bash
# Server Configuration
PORT=8095
LOG_LEVEL=info

# Development
NODE_ENV=production
DEBUG=memory-context:*
```

### Database Schema

The server automatically creates these PostgreSQL tables:
- `conversation_memory` - Conversation history with embeddings
- `document_contexts` - Document analysis and metadata
- `user_preferences` - User personalization settings
- `session_contexts` - Workflow session state
- `knowledge_graph_nodes` - Graph entities
- `knowledge_graph_edges` - Graph relationships

## 🏃‍♂️ Usage Examples

### Store Conversation Memory
```javascript
// Store a user question with context
{
  "action": "store",
  "conversation_id": "conv_123",
  "user_id": "user_456", 
  "message_content": "What are the key points in the healthcare policy document?",
  "context_type": "question"
}
```

### Track Document Context
```javascript
// Analyze and track a new document
{
  "action": "track",
  "document_id": "doc_789",
  "document_content": "Healthcare policy document content..."
}
```

### Manage User Preferences
```javascript
// Set user communication preferences
{
  "action": "set",
  "user_id": "user_456",
  "preferences": {
    "communication_style": "technical",
    "preferred_topics": ["healthcare", "policy"],
    "response_length": "detailed",
    "technical_level": "advanced"
  }
}
```

### Build Knowledge Graph
```javascript
// Add relationship between concepts
{
  "action": "add_edge",
  "from_node": "healthcare_policy",
  "to_node": "medicare_expansion", 
  "relationship_type": "includes"
}
```

## 🐳 Docker Integration

The server runs on port 8095 with health checks at `/health`.

**Docker Compose Integration:**
```yaml
memory-context:
  build: ./mcp-servers/memory-context
  ports:
    - "8095:8095"
  environment:
    - POSTGRES_HOST=postgres
    - REDIS_HOST=redis
    - OPENAI_API_KEY=${OPENAI_API_KEY}
  depends_on:
    - postgres
    - redis
```

## 📊 Health Monitoring

**Health Check Endpoint:** `GET /health`

Returns system status including:
- Service health status
- Database connection status
- Memory usage statistics
- Active session counts

## 🔒 Security Features

- **Input Validation** - All tool inputs validated and sanitized
- **SQL Injection Protection** - Parameterized queries throughout
- **Memory Limits** - Automatic cleanup of old data
- **Error Handling** - Comprehensive error catching and logging
- **Credential Security** - No credentials in logs or responses

## 🧪 Testing

**Manual Testing:**
```bash
# Health check
curl http://localhost:8095/health

# Test MCP tools through n8n workflow or MCP client
```

**Integration with n8n:**
- Configure as MCP server in n8n workflow
- Use tools in document processing pipelines
- Access memory data through workflow nodes

## 📈 Performance

- **Conversation Memory**: Optimized with embeddings for semantic search
- **Document Context**: Efficient text analysis with caching
- **Knowledge Graph**: In-memory graph structure for fast traversal
- **Session Management**: Automatic cleanup prevents memory leaks
- **Database Optimization**: Indexed queries for fast retrieval

## 🤝 Integration

This server integrates with:
- **n8n Workflows** - Primary integration for document processing
- **PostgreSQL** - Persistent storage for all memory data
- **Redis** - Caching and session management  
- **OpenAI API** - Embeddings for semantic search
- **Other MCP Servers** - Shares data with document-intelligence, web-research

## 📚 API Documentation

Full MCP tool documentation available through the `/health` endpoint and MCP resource discovery.

---

**Status: Production Ready** ✅

The Memory Context MCP Server provides enterprise-grade memory management for sophisticated n8n political document processing workflows.