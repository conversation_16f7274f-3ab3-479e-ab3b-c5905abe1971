@echo off
echo ======================================
echo MCP Server Setup Script for Windows
echo ======================================
echo.

echo [1] Checking Vibe Coder MCP...
if exist "C:\vibe-coder-mcp\build\index.js" (
    echo    ✓ Vibe Coder MCP is installed at C:\vibe-coder-mcp
) else (
    echo    ✗ Vibe Coder MCP not found at C:\vibe-coder-mcp
    echo    Please ensure Vibe Coder MCP is installed and built
)

echo.
echo [2] Checking n8n MCP Server...
if exist "C:\dev\n8n_workflow_windows\mcp-servers\n8n-mcp-server\build\index.js" (
    echo    ✓ n8n MCP Server is installed
) else (
    echo    ✗ n8n MCP Server not built
    echo    Building n8n MCP Server...
    cd /d "C:\dev\n8n_workflow_windows\mcp-servers\n8n-mcp-server"
    npm install
    npm run build
)

echo.
echo [3] Configuration Status:
echo    - Cursor MCP config: .cursor\mcp.json
echo    - Windsurf MCP config: .windsurf\mcp.json
echo.
echo    NOTE: You need to update the N8N_API_KEY in the MCP configs
echo    1. Start n8n: docker-compose up -d
echo    2. Navigate to http://localhost:5678
echo    3. Go to Settings → API → API Keys
echo    4. Create a new API key
echo    5. Update N8N_API_KEY in both .cursor\mcp.json and .windsurf\mcp.json

echo.
echo [4] Testing MCP Servers...
echo.
echo    To test Vibe Coder MCP:
echo    node C:\vibe-coder-mcp\build\index.js --help
echo.
echo    To test n8n MCP Server:
echo    node C:\dev\n8n_workflow_windows\mcp-servers\n8n-mcp-server\build\index.js --help

echo.
echo ======================================
echo Setup Complete!
echo ======================================
pause