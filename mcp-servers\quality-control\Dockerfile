FROM node:18-alpine

WORKDIR /app

# Install system dependencies for AI processing
RUN apk add --no-cache \
    python3 \
    py3-pip \
    build-base \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm ci --only=production

# Copy server code
COPY server.js ./

# Create directories
RUN mkdir -p /app/reports /app/data

# Expose port
EXPOSE 8080

# Run as non-root user
USER node

CMD ["node", "server.js"]