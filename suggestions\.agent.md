# RovoDev Political Document Processing System

## Project Overview

### Purpose
This project is a comprehensive AI-powered political document processing system designed to embody <PERSON>'s vision for economic justice and democratic renewal. The system automatically processes political documents through specialized AI agents that understand and apply manifesto principles to create high-quality political content.

### Core Mission
- **Economic Justice** - America's wealth belongs to Americans, not billionaires
- **Democratic Renewal** - Government serves people, not corporations
- **Universal Rights** - Healthcare, education, and housing as human rights
- **American Social Trust Fund** - Public ownership of resources for public benefit

### Technology Stack
- **n8n** - Workflow orchestration and automation
- **AI Models** - Claude 4 Sonnet, o3, Gemini 2.5 Pro via MCP server
- **ChromaDB** - Vector database for RAG (Retrieval Augmented Generation)
- **Docker** - Containerized deployment
- **Google Drive API** - Document storage and monitoring
- **CloudConvert API** - Markdown to DOCX conversion

## Key Files and Directories

### Core Implementation Documents
- `n8n_implementation_guide_complete.md` - **PRIMARY BLUEPRINT** - Complete technical guide for building the system
- `rovodev_n8n_master_design.md` - High-level system architecture and design
- `n8n_workflow_setup.md` - Detailed n8n workflow configuration

### Manifesto Integration System (10,000 token context)
- `manifesto_for_agents.md` - Primary AI agent guidance document
- `core_essence.md` - Fundamental beliefs and vision (800 words)
- `style_guide.md` - Writing guidelines and tone (400 words)
- `voice_guidelines_beau_lewis.md` - Detailed rhetorical patterns and voice analysis

### Conversational System Documents
- `conversational_rag_enhancement.md` - Chat interface and memory system design
- `enhanced_mcp_ecosystem.md` - Professional MCP servers for world-class capabilities
- `database_schema.sql` - PostgreSQL schema for conversation memory
- `chat_interface_specs.md` - Web-based chat interface specifications

### Category-Specific Supplements (400 tokens each)
- `category_supplement_healthcare.md` - Scandinavian-inspired universal coverage
- `category_supplement_education.md` - Free education Head Start through graduate school
- `category_supplement_economic_policy.md` - ASTF and automation taxation
- `category_supplement_housing.md` - 3 million homes through public construction
- `category_supplement_jobs_automation.md` - Worker dignity and automation responsibility
- `category_supplement_constitutional_amendments.md` - 21 amendments for renewal
- `category_supplement_ethics_accountability.md` - Government integrity
- `category_supplement_rights_repair_grow.md` - Ownership and agricultural rights
- `category_supplement_funding_revenue.md` - How we pay for everything

### Source Documents
- `rovodev_manifesto.md` - Original manifesto for economic justice and democratic renewal
- `white_papers_markdown/` - Organized collection of political white papers by category
  - `constitution/` - Constitutional amendments and reforms
  - `education/` - Education policy and reform documents
  - `ethics/` - Government ethics and accountability
  - `healthcare/` - Universal healthcare and Medicare for All
  - `housing/` - Public housing and affordability
  - `social trust fund/` - ASTF implementation and strategy
  - `others/` - Miscellaneous political documents and notes

### Technical Configuration
- `tmp_rovodev_docker_compose_n8n.yml` - Docker container configuration
- `tmp_rovodev_ai_model_research_analysis.md` - AI model selection and optimization
- `tmp_rovodev_folder_structure_design.md` - Google Drive organization strategy

## Development Guidelines

### AI Agent Integration
- **Token Budget** - 10,000 tokens per AI agent for rich context
- **Manifesto Fidelity** - Every output must authentically represent Beau Lewis's vision
- **Voice Consistency** - Maintain passionate but professional tone across all documents
- **Cross-Policy Integration** - Connect all policies back to ASTF and economic justice themes

### Content Standards
- **Moral Clarity** - Clear distinction between justice and injustice
- **Non-Partisan Appeal** - "It's not red vs. blue, it's the 1% vs. you"
- **Evidence-Based** - International examples, economic data, expert consensus
- **Action-Oriented** - Concrete policies with implementation pathways

### Document Processing Workflow
1. **File Monitoring** - Google Drive political_in/ folder
2. **AI Analysis** - Document intent and manifesto alignment
3. **Task Routing** - Edit, combine, generate white paper, or create new
4. **Specialized Processing** - Category-specific AI agents with manifesto context
5. **Quality Control** - o3 model ensures alignment and professional standards
6. **Format Conversion** - CloudConvert MD → DOCX with professional templates
7. **Output Storage** - Google Drive political_out/ folder
8. **Notification** - Email alerts with processing summary

### Quality Assurance
- **Manifesto Alignment Score** - AI assessment (target 9/10)
- **Voice Authenticity** - Signature phrases and rhetorical patterns
- **Factual Accuracy** - Verification against reliable sources
- **Cross-Document Consistency** - Coherent policy framework
- **Professional Formatting** - Publication-ready DOCX output

## Best Practices

### Working with Manifesto Documents
- Always load core_essence.md + style_guide.md as foundation (1,500 tokens)
- Add relevant category supplements based on document topic
- Include voice_guidelines_beau_lewis.md for authentic tone
- Reference signature phrases: "It's not red vs. blue, it's the 1% vs. you"

### AI Model Selection
- **Lead Orchestrator** - o3 for complex reasoning and planning
- **Content Generation** - Claude 4 Sonnet for policy writing excellence
- **Research** - Gemini 2.5 Pro for web research and fact-checking
- **Quality Control** - o3 for superior reasoning about quality assessment

### Document Naming Conventions
- Original: `healthcare_whitepaper_2024-01-15_v1.docx`
- QC Edit: `healthcare_whitepaper_qualityedit_2024-01-15_v1.docx`
- Revision: `healthcare_whitepaper_qualityedit_2024-01-15_v2.docx`

### Error Handling
- **Global Error Workflow** - Automatic recovery and human notification
- **Health Monitoring** - 15-minute system checks
- **Quality Fallbacks** - Human review for low-confidence AI outputs
- **Version Control** - Never overwrite originals, always create new versions

## Development Workflow

### Setting Up the System
1. **Read Implementation Guide** - Start with `n8n_implementation_guide_complete.md`
2. **Deploy Infrastructure** - Docker containers for n8n, ChromaDB, Redis
3. **Configure APIs** - Google Drive, CloudConvert, AI model access
4. **Set Up MCP Server** - n8n MCP server for AI agent communication
5. **Build Workflows** - Follow detailed node specifications
6. **Test End-to-End** - Validate manifesto fidelity and output quality

### Manifesto Updates
- Update core documents when Beau Lewis's vision evolves
- Maintain consistency across all category supplements
- Test AI agents with updated context to ensure alignment
- Version control manifesto changes for tracking

### Performance Optimization
- **Token Efficiency** - Optimize 10,000-token context loading
- **Processing Speed** - Target <5 minutes per document
- **Quality Metrics** - Monitor alignment scores and user satisfaction
- **Cost Management** - Track AI model usage and optimize expensive models

## Security Considerations

### Data Protection
- **OAuth2** - Secure Google Drive access with minimal permissions
- **API Key Management** - Secure credential storage in n8n
- **Document Privacy** - Appropriate access controls and permissions
- **Audit Trails** - Complete processing logs for accountability

### System Integrity
- **Constitutional Protection** - ASTF protected from political interference
- **Independent Operation** - System operates without presidential control
- **Transparent Governance** - Public oversight and accountability mechanisms
- **Democratic Values** - Technology serves people, not powerful interests

## Success Metrics

### Technical Performance
- **Processing Time** - <5 minutes per document
- **System Uptime** - 99.5% availability
- **Error Rate** - <2% processing failures
- **Quality Score** - 9/10 average manifesto alignment

### Content Impact
- **Voice Authenticity** - Consistent Beau Lewis tone and style
- **Policy Coherence** - Integrated ASTF-centered framework
- **Professional Quality** - Publication-ready output
- **Democratic Engagement** - Content that builds movement for change

## Notes for Developers

### Critical Success Factors
- **Manifesto Fidelity** - System must faithfully represent political vision
- **Voice Consistency** - Passionate conviction with professional credibility
- **Quality Assurance** - Multiple validation layers ensure high standards
- **Scalability** - Handle increasing document volume efficiently
- **Reliability** - Robust operation for mission-critical political work

### Common Pitfalls to Avoid
- **Token Waste** - Inefficient context loading reducing quality
- **Voice Drift** - AI outputs that don't sound like Beau Lewis
- **Policy Inconsistency** - Documents that contradict each other
- **Technical Failures** - System downtime during critical periods
- **Security Lapses** - Unauthorized access to sensitive political content

This system represents a technological foundation for building a movement that can truly create an America that works for everyone. Every line of code and every AI interaction should advance the cause of economic justice and democratic renewal.

**"Together, we will build an America that works for everyone. Together, we will restore the promise of America. Together, we will win."**