# Setup and Configuration

This section covers everything you need to know to set up and configure the n8n MCP Server.

## Topics

- [Installation](./installation.md): Instructions for installing the n8n MCP Server from npm or from source.
- [Configuration](./configuration.md): Information on configuring the server, including environment variables and n8n API setup.
- [Troubleshooting](./troubleshooting.md): Solutions to common issues you might encounter.

## Quick Start

For a quick start, follow these steps:

1. Install the server: `npm install -g @leonardsellem/n8n-mcp-server`
2. Create a `.env` file with your n8n API URL and API key
3. Run the server: `n8n-mcp-server`
4. Register the server with your AI assistant platform
