#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import axios from 'axios';
import * as cheerio from 'cheerio';
import OpenAI from 'openai';
import { Client } from 'pg';
import { createClient } from 'redis';
import fs from 'fs-extra';
import path from 'path';
import { withCircuitBreaker, logStructuredError, getHealthCheck } from '../shared/error-handling.js';

/**
 * Research Integration MCP Server
 * Handles research queries, fact-checking, and data integration for political content
 */

class ResearchIntegrationServer {
  constructor() {
    this.server = new MCPServer({
      name: 'research-integration',
      version: '1.0.0'
    });
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    // Research APIs
    this.perplexityApiKey = process.env.PERPLEXITY_API_KEY;
    this.serpApiKey = process.env.SERP_API_KEY;
    
    this.cacheDir = process.env.CACHE_DIR || '/app/cache';
    
    this.setupTools();
    this.setupResources();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'political_conversations',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      // Connect to PostgreSQL with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.pgClient.connect();
      });
      
      // Connect to Redis with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.redisClient.connect();
      });
      
      console.log('Research Integration Server: Connected to databases');
      
      // Ensure cache directory exists
      await fs.ensureDir(this.cacheDir);
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.initialize', {
        component: 'database_connection'
      });
      throw error;
    }
  }

  setupTools() {
    // Tool: Research Topic
    this.server.addTool({
      name: 'research_topic',
      description: 'Research a political topic with multiple sources and fact-checking',
      inputSchema: {
        type: 'object',
        properties: {
          topic: {
            type: 'string',
            description: 'Research topic or question'
          },
          research_depth: {
            type: 'string',
            enum: ['quick', 'standard', 'comprehensive'],
            default: 'standard',
            description: 'Depth of research to perform'
          },
          focus_areas: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['policy_examples', 'international_comparisons', 'economic_data', 'academic_research', 'recent_developments']
            },
            description: 'Specific areas to focus research on'
          },
          time_frame: {
            type: 'string',
            enum: ['recent', 'historical', 'both'],
            default: 'both',
            description: 'Time frame for research'
          }
        },
        required: ['topic']
      }
    }, this.researchTopic.bind(this));

    // Tool: Fact Check Statement
    this.server.addTool({
      name: 'fact_check_statement',
      description: 'Fact-check political statements and claims',
      inputSchema: {
        type: 'object',
        properties: {
          statement: {
            type: 'string',
            description: 'Statement or claim to fact-check'
          },
          context: {
            type: 'string',
            description: 'Additional context for the fact-check'
          },
          strictness: {
            type: 'string',
            enum: ['lenient', 'standard', 'strict'],
            default: 'standard',
            description: 'Fact-checking strictness level'
          }
        },
        required: ['statement']
      }
    }, this.factCheckStatement.bind(this));

    // Tool: Get International Examples
    this.server.addTool({
      name: 'get_international_examples',
      description: 'Find international examples of successful policies',
      inputSchema: {
        type: 'object',
        properties: {
          policy_area: {
            type: 'string',
            description: 'Policy area to research'
          },
          countries: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific countries to focus on (optional)'
          },
          success_metrics: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['cost_effectiveness', 'coverage', 'outcomes', 'satisfaction', 'sustainability']
            },
            description: 'Success metrics to evaluate'
          }
        },
        required: ['policy_area']
      }
    }, this.getInternationalExamples.bind(this));

    // Tool: Economic Data Research
    this.server.addTool({
      name: 'economic_data_research',
      description: 'Research economic data and statistics for policy arguments',
      inputSchema: {
        type: 'object',
        properties: {
          data_type: {
            type: 'string',
            enum: ['wealth_inequality', 'healthcare_costs', 'education_costs', 'automation_impact', 'resource_extraction'],
            description: 'Type of economic data to research'
          },
          geographic_scope: {
            type: 'string',
            enum: ['us_national', 'us_state', 'international', 'comparative'],
            default: 'us_national',
            description: 'Geographic scope of data'
          },
          time_period: {
            type: 'string',
            enum: ['current', 'decade', 'historical_trend'],
            default: 'decade',
            description: 'Time period for data analysis'
          }
        },
        required: ['data_type']
      }
    }, this.economicDataResearch.bind(this));

    // Tool: Policy Impact Analysis
    this.server.addTool({
      name: 'policy_impact_analysis',
      description: 'Analyze potential impacts of proposed policies',
      inputSchema: {
        type: 'object',
        properties: {
          policy_description: {
            type: 'string',
            description: 'Description of the proposed policy'
          },
          analysis_scope: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['economic', 'social', 'political', 'environmental', 'implementation']
            },
            description: 'Scope of impact analysis'
          },
          stakeholder_groups: {
            type: 'array',
            items: { type: 'string' },
            description: 'Stakeholder groups to consider'
          }
        },
        required: ['policy_description']
      }
    }, this.policyImpactAnalysis.bind(this));

    // Tool: Source Credibility Check
    this.server.addTool({
      name: 'source_credibility_check',
      description: 'Evaluate credibility and bias of information sources',
      inputSchema: {
        type: 'object',
        properties: {
          source_url: {
            type: 'string',
            description: 'URL or source identifier to evaluate'
          },
          source_name: {
            type: 'string',
            description: 'Name of the source organization'
          },
          evaluation_criteria: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['bias', 'accuracy', 'transparency', 'expertise', 'methodology']
            },
            description: 'Criteria for evaluation'
          }
        }
      }
    }, this.sourceCredibilityCheck.bind(this));
  }

  setupResources() {
    this.server.addResource({
      uri: 'research://cached_reports',
      name: 'Cached Research Reports',
      description: 'Previously generated research reports and analyses',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'research://fact_checks',
      name: 'Fact Check Database',
      description: 'Database of fact-checked statements and claims',
      mimeType: 'application/json'
    });
  }

  async researchTopic({ topic, research_depth = 'standard', focus_areas = [], time_frame = 'both' }) {
    try {
      const researchId = this.generateResearchId();
      
      // Check cache first with circuit breaker protection
      const cachedResearch = await withCircuitBreaker('database', async () => {
        return await this.getCachedResearch(topic);
      });
      
      if (cachedResearch && research_depth === 'quick') {
        return cachedResearch;
      }

      const research = {
        id: researchId,
        topic,
        research_depth,
        focus_areas,
        time_frame,
        sources: [],
        findings: {},
        summary: '',
        confidence_score: 0,
        last_updated: new Date().toISOString()
      };

      // Perform research based on depth with circuit breaker protection
      switch (research_depth) {
        case 'quick':
          research.sources = await withCircuitBreaker('external_api', async () => {
            return await this.quickWebSearch(topic);
          });
          break;
        case 'standard':
          research.sources = await withCircuitBreaker('external_api', async () => {
            return await this.standardResearch(topic, focus_areas);
          });
          break;
        case 'comprehensive':
          research.sources = await withCircuitBreaker('external_api', async () => {
            return await this.comprehensiveResearch(topic, focus_areas, time_frame);
          });
          break;
      }

      // Analyze and synthesize findings with AI circuit breaker protection
      research.findings = await withCircuitBreaker('openai', async () => {
        return await this.synthesizeFindings(research.sources, focus_areas);
      });
      
      research.summary = await withCircuitBreaker('openai', async () => {
        return await this.generateResearchSummary(research);
      });
      
      research.confidence_score = this.calculateConfidenceScore(research.sources);

      // Cache the research with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        return await this.cacheResearch(topic, research);
      });

      return research;

    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.researchTopic', {
        component: 'research_topic',
        topic: topic,
        research_depth: research_depth
      });
      throw error;
    }
  }

  async factCheckStatement({ statement, context = '', strictness = 'standard' }) {
    try {
      const factCheckId = this.generateFactCheckId();
      
      // Check if already fact-checked with circuit breaker protection
      const cached = await withCircuitBreaker('database', async () => {
        return await this.getCachedFactCheck(statement);
      });
      
      if (cached) {
        return cached;
      }

      // Perform fact-checking with circuit breaker protection for external API calls
      const sources = await withCircuitBreaker('external_api', async () => {
        return await this.gatherFactCheckSources(statement, context);
      });
      
      const analysis = await withCircuitBreaker('openai', async () => {
        return await this.analyzeFactualAccuracy(statement, sources, strictness);
      });
      
      const factCheck = {
        id: factCheckId,
        statement,
        context,
        strictness,
        verdict: analysis.verdict, // 'true', 'mostly_true', 'mixed', 'mostly_false', 'false', 'unverifiable'
        confidence: analysis.confidence,
        sources: sources,
        explanation: analysis.explanation,
        corrections: analysis.corrections || [],
        last_updated: new Date().toISOString()
      };

      // Cache the fact-check with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        return await this.cacheFactCheck(statement, factCheck);
      });
      
      // Store in database with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        return await this.storeFactCheck(factCheck);
      });

      return factCheck;

    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.factCheckStatement', {
        component: 'fact_check',
        statement_length: statement.length,
        strictness: strictness
      });
      throw error;
    }
  }

  async getInternationalExamples({ policy_area, countries = [], success_metrics = [] }) {
    try {
      // Focus on Nordic countries and other successful models
      const defaultCountries = ['sweden', 'norway', 'denmark', 'finland', 'germany', 'canada', 'netherlands'];
      const searchCountries = countries.length > 0 ? countries : defaultCountries;

      const examples = [];

      for (const country of searchCountries) {
        const searchQuery = `${policy_area} policy ${country} success outcomes`;
        
        // Research country policy with circuit breaker protection for external API calls
        const countryData = await withCircuitBreaker('external_api', async () => {
          return await this.researchCountryPolicy(searchQuery, country, policy_area);
        });
        
        if (countryData.relevant) {
          examples.push({
            country,
            policy_area,
            description: countryData.description,
            outcomes: countryData.outcomes,
            metrics: countryData.metrics,
            lessons: countryData.lessons,
            applicability_to_us: countryData.applicability,
            sources: countryData.sources
          });
        }
      }

      // Sort by relevance and success metrics
      examples.sort((a, b) => (b.applicability_to_us || 0) - (a.applicability_to_us || 0));

      // Generate analysis with AI circuit breaker protection
      const summary = await withCircuitBreaker('openai', async () => {
        return await this.summarizeInternationalExamples(examples);
      });
      
      const recommendations = await withCircuitBreaker('openai', async () => {
        return await this.generatePolicyRecommendations(examples, policy_area);
      });

      return {
        policy_area,
        examples,
        summary,
        recommendations
      };

    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.getInternationalExamples', {
        component: 'international_examples',
        policy_area: policy_area,
        countries_count: countries.length
      });
      throw error;
    }
  }

  async economicDataResearch({ data_type, geographic_scope = 'us_national', time_period = 'decade' }) {
    try {
      const dataSources = {
        wealth_inequality: ['bureau_of_labor_statistics', 'federal_reserve', 'census_bureau'],
        healthcare_costs: ['cms', 'oecd', 'kaiser_foundation'],
        education_costs: ['department_of_education', 'college_board'],
        automation_impact: ['brookings', 'mckinsey', 'oxford_economics'],
        resource_extraction: ['eia', 'usgs', 'epa']
      };

      const sources = dataSources[data_type] || [];
      const dataPoints = [];

      for (const source of sources) {
        try {
          // Fetch economic data with circuit breaker protection for external API calls
          const data = await withCircuitBreaker('external_api', async () => {
            return await this.fetchEconomicData(source, data_type, geographic_scope, time_period);
          });
          
          if (data) {
            dataPoints.push(data);
          }
        } catch (error) {
          logStructuredError(error, 'ResearchIntegrationServer.economicDataResearch.fetchData', {
            component: 'economic_data_fetch',
            source: source,
            data_type: data_type
          });
        }
      }

      // Analyze economic data with AI circuit breaker protection
      const analysis = await withCircuitBreaker('openai', async () => {
        return await this.analyzeEconomicData(dataPoints, data_type);
      });

      return {
        data_type,
        geographic_scope,
        time_period,
        data_points: dataPoints,
        analysis,
        key_statistics: analysis.key_stats,
        trends: analysis.trends,
        policy_implications: analysis.implications,
        sources_used: sources
      };

    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.economicDataResearch', {
        component: 'economic_data_research',
        data_type: data_type,
        geographic_scope: geographic_scope
      });
      throw error;
    }
  }

  async policyImpactAnalysis({ policy_description, analysis_scope = [], stakeholder_groups = [] }) {
    try {
      const analysis = {
        policy_description,
        analysis_scope,
        stakeholder_groups,
        impacts: {},
        overall_assessment: '',
        recommendations: [],
        risks: [],
        implementation_considerations: []
      };

      // Analyze each scope area with AI circuit breaker protection
      for (const scope of analysis_scope) {
        analysis.impacts[scope] = await withCircuitBreaker('openai', async () => {
          return await this.analyzeImpactScope(policy_description, scope, stakeholder_groups);
        });
      }

      // Generate overall assessment with AI circuit breaker protection
      analysis.overall_assessment = await withCircuitBreaker('openai', async () => {
        return await this.generateOverallAssessment(analysis.impacts);
      });
      
      analysis.recommendations = await withCircuitBreaker('openai', async () => {
        return await this.generateImplementationRecommendations(analysis);
      });
      
      analysis.risks = await withCircuitBreaker('openai', async () => {
        return await this.identifyPolicyRisks(policy_description, analysis.impacts);
      });

      return analysis;

    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.policyImpactAnalysis', {
        component: 'policy_impact_analysis',
        policy_description_length: policy_description.length,
        analysis_scope_count: analysis_scope.length
      });
      throw error;
    }
  }

  async sourceCredibilityCheck({ source_url, source_name, evaluation_criteria = [] }) {
    try {
      const credibilityAssessment = {
        source_url,
        source_name,
        evaluation_criteria,
        scores: {},
        overall_score: 0,
        recommendation: '',
        notes: []
      };

      // Default criteria if none specified
      if (evaluation_criteria.length === 0) {
        evaluation_criteria = ['bias', 'accuracy', 'transparency', 'expertise'];
      }

      // Evaluate each criterion with circuit breaker protection for AI analysis
      for (const criterion of evaluation_criteria) {
        credibilityAssessment.scores[criterion] = await withCircuitBreaker('openai', async () => {
          return await this.evaluateSourceCriterion(source_url, source_name, criterion);
        });
      }

      // Calculate overall score
      const scores = Object.values(credibilityAssessment.scores);
      credibilityAssessment.overall_score = scores.reduce((a, b) => a + b, 0) / scores.length;

      // Generate recommendation
      if (credibilityAssessment.overall_score >= 8) {
        credibilityAssessment.recommendation = 'highly_reliable';
      } else if (credibilityAssessment.overall_score >= 6) {
        credibilityAssessment.recommendation = 'reliable_with_caveats';
      } else if (credibilityAssessment.overall_score >= 4) {
        credibilityAssessment.recommendation = 'use_with_caution';
      } else {
        credibilityAssessment.recommendation = 'not_recommended';
      }

      return credibilityAssessment;

    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.sourceCredibilityCheck', {
        component: 'source_credibility_check',
        source_url: source_url,
        criteria_count: evaluation_criteria.length
      });
      throw error;
    }
  }

  // Helper methods

  async quickWebSearch(query) {
    // Implementation for quick web search
    return [];
  }

  async standardResearch(topic, focusAreas) {
    // Implementation for standard depth research
    return [];
  }

  async comprehensiveResearch(topic, focusAreas, timeFrame) {
    // Implementation for comprehensive research
    return [];
  }

  async synthesizeFindings(sources, focusAreas) {
    // Implementation for synthesizing research findings
    return {};
  }

  async generateResearchSummary(research) {
    // Implementation for generating research summary
    return 'Research summary would be generated here';
  }

  calculateConfidenceScore(sources) {
    // Implementation for calculating confidence score based on sources
    return Math.min(10, sources.length * 0.8 + Math.random() * 2);
  }

  async gatherFactCheckSources(statement, context) {
    // Implementation for gathering fact-checking sources
    return [];
  }

  async analyzeFactualAccuracy(statement, sources, strictness) {
    // Implementation for analyzing factual accuracy
    return {
      verdict: 'mostly_true',
      confidence: 0.85,
      explanation: 'Analysis would be performed here'
    };
  }

  async researchCountryPolicy(query, country, policyArea) {
    // Implementation for researching specific country policies
    return {
      relevant: true,
      description: 'Policy description',
      outcomes: [],
      metrics: {},
      lessons: [],
      applicability: 0.8,
      sources: []
    };
  }

  async summarizeInternationalExamples(examples) {
    // Implementation for summarizing international examples
    return 'Summary of international examples';
  }

  async generatePolicyRecommendations(examples, policyArea) {
    // Implementation for generating policy recommendations
    return [];
  }

  async fetchEconomicData(source, dataType, scope, period) {
    // Implementation for fetching economic data from various sources
    return null;
  }

  async analyzeEconomicData(dataPoints, dataType) {
    // Implementation for analyzing economic data
    return {
      key_stats: {},
      trends: [],
      implications: []
    };
  }

  async analyzeImpactScope(policyDescription, scope, stakeholderGroups) {
    // Implementation for analyzing policy impact in specific scope
    return {};
  }

  async generateOverallAssessment(impacts) {
    // Implementation for generating overall policy assessment
    return 'Overall assessment would be generated here';
  }

  async generateImplementationRecommendations(analysis) {
    // Implementation for generating implementation recommendations
    return [];
  }

  async identifyPolicyRisks(policyDescription, impacts) {
    // Implementation for identifying policy risks
    return [];
  }

  async evaluateSourceCriterion(sourceUrl, sourceName, criterion) {
    // Implementation for evaluating source on specific criterion
    return Math.random() * 10; // Placeholder
  }

  // Cache management methods

  async getCachedResearch(topic) {
    try {
      const cacheKey = `research:${this.hashString(topic)}`;
      const cached = await this.redisClient.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.getCachedResearch', {
        component: 'cache_get',
        topic: topic
      });
      return null;
    }
  }

  async cacheResearch(topic, research) {
    try {
      const cacheKey = `research:${this.hashString(topic)}`;
      await this.redisClient.setex(cacheKey, 86400, JSON.stringify(research)); // Cache for 24 hours
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.cacheResearch', {
        component: 'cache_set',
        topic: topic
      });
    }
  }

  async getCachedFactCheck(statement) {
    try {
      const cacheKey = `factcheck:${this.hashString(statement)}`;
      const cached = await this.redisClient.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.getCachedFactCheck', {
        component: 'cache_get_factcheck',
        statement_length: statement.length
      });
      return null;
    }
  }

  async cacheFactCheck(statement, factCheck) {
    try {
      const cacheKey = `factcheck:${this.hashString(statement)}`;
      await this.redisClient.setex(cacheKey, 604800, JSON.stringify(factCheck)); // Cache for 1 week
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.cacheFactCheck', {
        component: 'cache_set_factcheck',
        statement_length: statement.length
      });
    }
  }

  async storeFactCheck(factCheck) {
    // Store fact check in permanent database with circuit breaker protection
    try {
      await this.pgClient.query(`
        INSERT INTO fact_checks (
          id, statement, context, strictness, verdict, confidence,
          sources, explanation, corrections, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP)
        ON CONFLICT (id) DO UPDATE SET
          verdict = $5, confidence = $6, explanation = $8, updated_at = CURRENT_TIMESTAMP
      `, [
        factCheck.id,
        factCheck.statement,
        factCheck.context,
        factCheck.strictness,
        factCheck.verdict,
        factCheck.confidence,
        JSON.stringify(factCheck.sources),
        factCheck.explanation,
        JSON.stringify(factCheck.corrections)
      ]);
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.storeFactCheck', {
        component: 'database_store_factcheck',
        fact_check_id: factCheck.id
      });
      throw error;
    }
  }

  generateResearchId() {
    return `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateFactCheckId() {
    return `factcheck_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  async start() {
    await this.initialize();
    await this.server.start();
    console.log('Research Integration MCP Server started');
  }

  async stop() {
    try {
      if (this.pgClient) {
        await withCircuitBreaker('database', async () => {
          await this.pgClient.end();
        });
      }
      if (this.redisClient) {
        await withCircuitBreaker('database', async () => {
          await this.redisClient.quit();
        });
      }
      await this.server.stop();
    } catch (error) {
      logStructuredError(error, 'ResearchIntegrationServer.stop', {
        component: 'shutdown'
      });
    }
  }
}

// Start the server
const server = new ResearchIntegrationServer();

process.on('SIGINT', async () => {
  console.log('Shutting down Research Integration Server...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Research Integration Server...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);