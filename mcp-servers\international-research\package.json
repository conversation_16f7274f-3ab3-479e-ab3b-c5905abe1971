{"name": "@political-system/international-research-mcp", "version": "1.0.0", "description": "International Research MCP Server for comprehensive global policy analysis, diplomatic relations, and geopolitical research", "main": "server.js", "type": "module", "author": "<PERSON>", "license": "MIT", "keywords": ["mcp", "international-research", "global-policy", "diplomatic-analysis", "international-law", "comparative-governance", "geopolitical-analysis", "political-documents", "world-bank-api", "un-data", "oecd-statistics", "treaty-research", "diplomatic-relations"], "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "security-audit": "npm audit && snyk test", "data-refresh": "node scripts/refresh-international-data.js", "seed-countries": "node scripts/seed-countries.js"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "express": "^4.19.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "joi": "^17.11.0", "uuid": "^9.0.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-validator": "^7.0.1", "express-slow-down": "^2.0.1", "redis": "^4.6.12", "pg": "^8.11.3", "node-cron": "^3.0.3", "crypto": "^1.0.1", "morgan": "^1.10.0", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "axios": "^1.6.2", "natural": "^6.12.0", "sentiment": "^5.0.2", "csv-parser": "^3.0.0", "xml2js": "^0.6.2", "cheerio": "^1.0.0-rc.12", "moment": "^2.29.4", "lodash": "^4.17.21", "string-similarity": "^4.0.4", "country-list": "^2.3.0", "iso-3166-1": "^2.1.1", "world-countries": "^4.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "snyk": "^1.1266.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/beau-lewis/political-document-system"}, "bugs": {"url": "https://github.com/beau-lewis/political-document-system/issues"}, "homepage": "https://github.com/beau-lewis/political-document-system#readme", "config": {"data_sources": {"worldbank": "World Bank Open Data API", "un_statistics": "UN Statistics Division API", "oecd": "OECD Statistics API", "eurostat": "Eurostat API", "treaties": "UN Treaty Collection API", "gdelt": "GDELT Project API", "acled": "Armed Conflict Location & Event Data API", "transparency": "Transparency International API"}, "supported_regions": ["africa", "asia", "europe", "americas", "oceania"], "supported_languages": ["en", "fr", "es", "de", "zh", "ar", "ru", "pt", "ja", "hi"]}, "funding": {"type": "individual", "url": "https://github.com/sponsors/beau-lewis"}}