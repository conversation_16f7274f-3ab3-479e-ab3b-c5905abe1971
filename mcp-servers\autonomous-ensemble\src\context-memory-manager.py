#!/usr/bin/env python3
"""
Context Memory Manager for Political Document Processing
Advanced memory management with hierarchical caching and context sharing
"""

import asyncio
import json
import logging
import hashlib
import pickle
import gzip
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
import redis
import sqlite3
import threading
from contextlib import asynccontextmanager
from collections import defaultdict
import weakref

class MemoryType(Enum):
    CONTEXT = "context"
    RESULT = "result"
    METADATA = "metadata"
    KNOWLEDGE = "knowledge"
    TEMPORARY = "temporary"

class Priority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class MemoryEntry:
    """Memory entry representation"""
    key: str
    value: Any
    memory_type: MemoryType
    priority: Priority
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: Optional[int] = None  # Time to live in seconds
    size_bytes: int = 0
    tags: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ContextWindow:
    """Context window for conversation/task context"""
    window_id: str
    session_id: str
    task_type: str
    context_data: Dict[str, Any]
    created_at: datetime
    last_updated: datetime
    token_count: int = 0
    max_tokens: int = 100000
    priority: Priority = Priority.MEDIUM
    active: bool = True

class MemoryStats:
    """Memory usage statistics"""
    def __init__(self):
        self.total_entries = 0
        self.total_size_bytes = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.evictions = 0
        self.memory_by_type = defaultdict(int)
        self.memory_by_priority = defaultdict(int)

class ContextMemoryManager:
    """
    Advanced memory management system for political document processing
    Implements hierarchical caching with Redis and SQLite
    """
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379,
                 db_path: str = "context_memory.db"):
        # Redis connection for L1 cache
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=False)
        
        # SQLite for L2 persistent storage
        self.db_path = db_path
        self.db_lock = threading.Lock()
        self._initialize_database()
        
        # In-memory structures
        self.memory_entries: Dict[str, MemoryEntry] = {}
        self.context_windows: Dict[str, ContextWindow] = {}
        self.access_patterns: Dict[str, List[datetime]] = defaultdict(list)
        
        # Configuration
        self.max_memory_mb = 1024  # 1GB max memory
        self.max_redis_entries = 10000
        self.cleanup_interval = 300  # 5 minutes
        self.stats = MemoryStats()
        
        # Background tasks
        self.cleanup_task = None
        self.monitoring_task = None
        self.running = False
        
        # Event handlers
        self.event_handlers = {
            "memory_pressure": [],
            "context_updated": [],
            "knowledge_learned": []
        }
        
        # Initialize logger
        self.logger = logging.getLogger(__name__)
        
        # Weak references for garbage collection
        self.weak_refs = weakref.WeakValueDictionary()
    
    def _initialize_database(self):
        """Initialize SQLite database for persistent storage"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS memory_entries (
                    key TEXT PRIMARY KEY,
                    value BLOB,
                    memory_type TEXT,
                    priority INTEGER,
                    created_at TEXT,
                    last_accessed TEXT,
                    access_count INTEGER,
                    ttl INTEGER,
                    size_bytes INTEGER,
                    tags TEXT,
                    dependencies TEXT,
                    metadata TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS context_windows (
                    window_id TEXT PRIMARY KEY,
                    session_id TEXT,
                    task_type TEXT,
                    context_data TEXT,
                    created_at TEXT,
                    last_updated TEXT,
                    token_count INTEGER,
                    max_tokens INTEGER,
                    priority INTEGER,
                    active INTEGER
                )
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_memory_type ON memory_entries(memory_type)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_priority ON memory_entries(priority)
            ''')
            
            conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_session ON context_windows(session_id)
            ''')
            
            conn.commit()
    
    async def start(self):
        """Start the memory manager"""
        self.running = True
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Context Memory Manager started")
    
    async def stop(self):
        """Stop the memory manager"""
        self.running = False
        if self.cleanup_task:
            self.cleanup_task.cancel()
        if self.monitoring_task:
            self.monitoring_task.cancel()
        self.logger.info("Context Memory Manager stopped")
    
    async def store(self, key: str, value: Any, memory_type: MemoryType,
                   priority: Priority = Priority.MEDIUM, ttl: Optional[int] = None,
                   tags: List[str] = None, dependencies: List[str] = None,
                   metadata: Dict[str, Any] = None) -> bool:
        """Store a value in memory with hierarchical caching"""
        try:
            # Serialize value
            serialized_value = pickle.dumps(value)
            compressed_value = gzip.compress(serialized_value)
            size_bytes = len(compressed_value)
            
            # Create memory entry
            entry = MemoryEntry(
                key=key,
                value=value,
                memory_type=memory_type,
                priority=priority,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                ttl=ttl,
                size_bytes=size_bytes,
                tags=tags or [],
                dependencies=dependencies or [],
                metadata=metadata or {}
            )
            
            # Store in memory
            self.memory_entries[key] = entry
            
            # Store in Redis (L1 cache)
            await self._store_in_redis(key, compressed_value, ttl)
            
            # Store in SQLite (L2 persistent storage)
            await self._store_in_sqlite(entry)
            
            # Update statistics
            self.stats.total_entries += 1
            self.stats.total_size_bytes += size_bytes
            self.stats.memory_by_type[memory_type.value] += 1
            self.stats.memory_by_priority[priority.value] += 1
            
            # Check for memory pressure
            await self._check_memory_pressure()
            
            self.logger.debug(f"Stored {key} in memory ({size_bytes} bytes)")
            return True
            
        except Exception as e:
            self.logger.error(f"Error storing {key}: {e}")
            return False
    
    async def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve a value from memory with LRU caching"""
        try:
            # Check in-memory cache first
            if key in self.memory_entries:
                entry = self.memory_entries[key]
                entry.last_accessed = datetime.now()
                entry.access_count += 1
                self.stats.cache_hits += 1
                
                # Update access pattern
                self.access_patterns[key].append(datetime.now())
                
                return entry.value
            
            # Check Redis cache
            redis_value = await self._retrieve_from_redis(key)
            if redis_value:
                # Deserialize
                decompressed = gzip.decompress(redis_value)
                value = pickle.loads(decompressed)
                
                # Update in-memory cache
                entry = await self._get_entry_from_sqlite(key)
                if entry:
                    entry.value = value
                    entry.last_accessed = datetime.now()
                    entry.access_count += 1
                    self.memory_entries[key] = entry
                    self.stats.cache_hits += 1
                    
                    # Update access pattern
                    self.access_patterns[key].append(datetime.now())
                    
                    return value
            
            # Check SQLite
            entry = await self._get_entry_from_sqlite(key)
            if entry:
                # Update caches
                await self._promote_to_redis(key, entry)
                entry.last_accessed = datetime.now()
                entry.access_count += 1
                self.memory_entries[key] = entry
                self.stats.cache_misses += 1
                
                # Update access pattern
                self.access_patterns[key].append(datetime.now())
                
                return entry.value
            
            # Not found
            self.stats.cache_misses += 1
            return None
            
        except Exception as e:
            self.logger.error(f"Error retrieving {key}: {e}")
            return None
    
    async def _store_in_redis(self, key: str, value: bytes, ttl: Optional[int]):
        """Store value in Redis with optional TTL"""
        try:
            if ttl:
                await asyncio.to_thread(self.redis_client.setex, key, ttl, value)
            else:
                await asyncio.to_thread(self.redis_client.set, key, value)
        except Exception as e:
            self.logger.error(f"Error storing {key} in Redis: {e}")
    
    async def _retrieve_from_redis(self, key: str) -> Optional[bytes]:
        """Retrieve value from Redis"""
        try:
            return await asyncio.to_thread(self.redis_client.get, key)
        except Exception as e:
            self.logger.error(f"Error retrieving {key} from Redis: {e}")
            return None
    
    async def _store_in_sqlite(self, entry: MemoryEntry):
        """Store entry in SQLite database"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    # Serialize complex fields
                    compressed_value = gzip.compress(pickle.dumps(entry.value))
                    tags_json = json.dumps(entry.tags)
                    dependencies_json = json.dumps(entry.dependencies)
                    metadata_json = json.dumps(entry.metadata)
                    
                    conn.execute('''
                        INSERT OR REPLACE INTO memory_entries
                        (key, value, memory_type, priority, created_at, last_accessed,
                         access_count, ttl, size_bytes, tags, dependencies, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        entry.key,
                        compressed_value,
                        entry.memory_type.value,
                        entry.priority.value,
                        entry.created_at.isoformat(),
                        entry.last_accessed.isoformat(),
                        entry.access_count,
                        entry.ttl,
                        entry.size_bytes,
                        tags_json,
                        dependencies_json,
                        metadata_json
                    ))
                    conn.commit()
        except Exception as e:
            self.logger.error(f"Error storing {entry.key} in SQLite: {e}")
    
    async def _get_entry_from_sqlite(self, key: str) -> Optional[MemoryEntry]:
        """Retrieve entry from SQLite database"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('''
                        SELECT key, value, memory_type, priority, created_at, last_accessed,
                               access_count, ttl, size_bytes, tags, dependencies, metadata
                        FROM memory_entries WHERE key = ?
                    ''', (key,))
                    
                    row = cursor.fetchone()
                    if row:
                        # Deserialize
                        compressed_value = row[1]
                        value = pickle.loads(gzip.decompress(compressed_value))
                        
                        entry = MemoryEntry(
                            key=row[0],
                            value=value,
                            memory_type=MemoryType(row[2]),
                            priority=Priority(row[3]),
                            created_at=datetime.fromisoformat(row[4]),
                            last_accessed=datetime.fromisoformat(row[5]),
                            access_count=row[6],
                            ttl=row[7],
                            size_bytes=row[8],
                            tags=json.loads(row[9]),
                            dependencies=json.loads(row[10]),
                            metadata=json.loads(row[11])
                        )
                        return entry
                    
                    return None
        except Exception as e:
            self.logger.error(f"Error retrieving {key} from SQLite: {e}")
            return None
    
    async def _promote_to_redis(self, key: str, entry: MemoryEntry):
        """Promote entry from SQLite to Redis"""
        try:
            serialized_value = pickle.dumps(entry.value)
            compressed_value = gzip.compress(serialized_value)
            await self._store_in_redis(key, compressed_value, entry.ttl)
        except Exception as e:
            self.logger.error(f"Error promoting {key} to Redis: {e}")
    
    async def create_context_window(self, session_id: str, task_type: str,
                                   context_data: Dict[str, Any],
                                   max_tokens: int = 100000,
                                   priority: Priority = Priority.MEDIUM) -> str:
        """Create a new context window"""
        window_id = f"ctx_{session_id}_{int(datetime.now().timestamp())}"
        
        window = ContextWindow(
            window_id=window_id,
            session_id=session_id,
            task_type=task_type,
            context_data=context_data,
            created_at=datetime.now(),
            last_updated=datetime.now(),
            token_count=self._estimate_token_count(context_data),
            max_tokens=max_tokens,
            priority=priority
        )
        
        self.context_windows[window_id] = window
        await self._store_context_window(window)
        
        # Emit event
        await self._emit_event("context_updated", {
            "window_id": window_id,
            "session_id": session_id,
            "action": "created"
        })
        
        self.logger.info(f"Created context window {window_id}")
        return window_id
    
    async def update_context_window(self, window_id: str, 
                                   context_data: Dict[str, Any]) -> bool:
        """Update context window with new data"""
        if window_id not in self.context_windows:
            return False
        
        window = self.context_windows[window_id]
        
        # Merge context data
        window.context_data.update(context_data)
        window.last_updated = datetime.now()
        window.token_count = self._estimate_token_count(window.context_data)
        
        # Check token limit
        if window.token_count > window.max_tokens:
            await self._compress_context_window(window)
        
        # Store updated window
        await self._store_context_window(window)
        
        # Emit event
        await self._emit_event("context_updated", {
            "window_id": window_id,
            "session_id": window.session_id,
            "action": "updated",
            "token_count": window.token_count
        })
        
        return True
    
    async def get_context_window(self, window_id: str) -> Optional[ContextWindow]:
        """Get context window by ID"""
        if window_id in self.context_windows:
            return self.context_windows[window_id]
        
        # Try to load from database
        window = await self._load_context_window(window_id)
        if window:
            self.context_windows[window_id] = window
            return window
        
        return None
    
    async def _compress_context_window(self, window: ContextWindow):
        """Compress context window by removing old or less important data"""
        # Implement context compression logic
        # For now, just remove old entries
        context_data = window.context_data
        
        # Keep only recent messages/data
        if "messages" in context_data:
            messages = context_data["messages"]
            if len(messages) > 50:
                # Keep last 30 messages
                context_data["messages"] = messages[-30:]
        
        # Archive old context to knowledge base
        archived_context = {
            "window_id": window.window_id,
            "session_id": window.session_id,
            "archived_at": datetime.now().isoformat(),
            "summary": self._summarize_context(window.context_data)
        }
        
        await self.store(
            f"archived_context_{window.window_id}",
            archived_context,
            MemoryType.KNOWLEDGE,
            Priority.LOW,
            tags=["archived", "context", window.task_type]
        )
        
        # Update token count
        window.token_count = self._estimate_token_count(window.context_data)
        
        self.logger.info(f"Compressed context window {window.window_id}")
    
    def _estimate_token_count(self, data: Dict[str, Any]) -> int:
        """Estimate token count for context data"""
        text = json.dumps(data, default=str)
        return len(text.split()) * 1.3  # Rough estimate
    
    def _summarize_context(self, context_data: Dict[str, Any]) -> str:
        """Create a summary of context data"""
        # Simple summarization - in practice, use an LLM
        summary = f"Context summary: {len(context_data)} entries"
        if "messages" in context_data:
            summary += f", {len(context_data['messages'])} messages"
        return summary
    
    async def _store_context_window(self, window: ContextWindow):
        """Store context window in database"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute('''
                        INSERT OR REPLACE INTO context_windows
                        (window_id, session_id, task_type, context_data, created_at,
                         last_updated, token_count, max_tokens, priority, active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        window.window_id,
                        window.session_id,
                        window.task_type,
                        json.dumps(window.context_data, default=str),
                        window.created_at.isoformat(),
                        window.last_updated.isoformat(),
                        window.token_count,
                        window.max_tokens,
                        window.priority.value,
                        1 if window.active else 0
                    ))
                    conn.commit()
        except Exception as e:
            self.logger.error(f"Error storing context window {window.window_id}: {e}")
    
    async def _load_context_window(self, window_id: str) -> Optional[ContextWindow]:
        """Load context window from database"""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute('''
                        SELECT window_id, session_id, task_type, context_data, created_at,
                               last_updated, token_count, max_tokens, priority, active
                        FROM context_windows WHERE window_id = ?
                    ''', (window_id,))
                    
                    row = cursor.fetchone()
                    if row:
                        window = ContextWindow(
                            window_id=row[0],
                            session_id=row[1],
                            task_type=row[2],
                            context_data=json.loads(row[3]),
                            created_at=datetime.fromisoformat(row[4]),
                            last_updated=datetime.fromisoformat(row[5]),
                            token_count=row[6],
                            max_tokens=row[7],
                            priority=Priority(row[8]),
                            active=bool(row[9])
                        )
                        return window
                    
                    return None
        except Exception as e:
            self.logger.error(f"Error loading context window {window_id}: {e}")
            return None
    
    async def _cleanup_loop(self):
        """Background cleanup of expired and unused entries"""
        while self.running:
            try:
                await self._cleanup_expired_entries()
                await self._cleanup_unused_entries()
                await asyncio.sleep(self.cleanup_interval)
            except Exception as e:
                self.logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_expired_entries(self):
        """Remove expired entries"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, entry in self.memory_entries.items():
            if entry.ttl:
                expiry_time = entry.created_at + timedelta(seconds=entry.ttl)
                if current_time > expiry_time:
                    expired_keys.append(key)
        
        for key in expired_keys:
            await self.delete(key)
            self.logger.debug(f"Expired entry {key} removed")
    
    async def _cleanup_unused_entries(self):
        """Remove unused entries based on LRU policy"""
        if len(self.memory_entries) <= self.max_redis_entries:
            return
        
        # Sort by last accessed time and priority
        sorted_entries = sorted(
            self.memory_entries.items(),
            key=lambda x: (x[1].priority.value, x[1].last_accessed)
        )
        
        # Remove least recently used entries
        removal_count = len(self.memory_entries) - self.max_redis_entries
        for i in range(removal_count):
            key, entry = sorted_entries[i]
            
            # Don't remove critical entries
            if entry.priority == Priority.CRITICAL:
                continue
            
            await self.delete(key)
            self.stats.evictions += 1
            self.logger.debug(f"Evicted entry {key}")
    
    async def _monitoring_loop(self):
        """Background monitoring and optimization"""
        while self.running:
            try:
                await self._monitor_memory_usage()
                await self._optimize_access_patterns()
                await asyncio.sleep(60)  # Monitor every minute
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    async def _monitor_memory_usage(self):
        """Monitor memory usage and emit warnings"""
        total_size_mb = self.stats.total_size_bytes / (1024 * 1024)
        
        if total_size_mb > self.max_memory_mb * 0.8:
            await self._emit_event("memory_pressure", {
                "current_size_mb": total_size_mb,
                "max_size_mb": self.max_memory_mb,
                "usage_percent": (total_size_mb / self.max_memory_mb) * 100
            })
    
    async def _optimize_access_patterns(self):
        """Optimize memory based on access patterns"""
        # Analyze access patterns and promote frequently accessed items
        for key, access_times in self.access_patterns.items():
            # Clean old access times
            cutoff_time = datetime.now() - timedelta(hours=1)
            recent_accesses = [t for t in access_times if t > cutoff_time]
            self.access_patterns[key] = recent_accesses
            
            # Promote frequently accessed items
            if len(recent_accesses) > 10 and key in self.memory_entries:
                entry = self.memory_entries[key]
                if entry.priority != Priority.CRITICAL:
                    entry.priority = Priority.HIGH
                    await self._store_in_sqlite(entry)
    
    async def _check_memory_pressure(self):
        """Check for memory pressure and trigger cleanup"""
        total_size_mb = self.stats.total_size_bytes / (1024 * 1024)
        
        if total_size_mb > self.max_memory_mb * 0.9:
            await self._emergency_cleanup()
    
    async def _emergency_cleanup(self):
        """Emergency cleanup when memory pressure is high"""
        # Remove temporary entries first
        temp_keys = [
            key for key, entry in self.memory_entries.items()
            if entry.memory_type == MemoryType.TEMPORARY
        ]
        
        for key in temp_keys:
            await self.delete(key)
        
        # Remove low priority entries
        low_priority_keys = [
            key for key, entry in self.memory_entries.items()
            if entry.priority == Priority.LOW
        ]
        
        for key in low_priority_keys[:len(low_priority_keys)//2]:
            await self.delete(key)
        
        self.logger.warning("Emergency cleanup performed")
    
    async def delete(self, key: str) -> bool:
        """Delete an entry from all storage layers"""
        try:
            # Remove from memory
            if key in self.memory_entries:
                entry = self.memory_entries[key]
                self.stats.total_size_bytes -= entry.size_bytes
                self.stats.total_entries -= 1
                del self.memory_entries[key]
            
            # Remove from Redis
            await asyncio.to_thread(self.redis_client.delete, key)
            
            # Remove from SQLite
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute('DELETE FROM memory_entries WHERE key = ?', (key,))
                    conn.commit()
            
            # Clean access patterns
            if key in self.access_patterns:
                del self.access_patterns[key]
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting {key}: {e}")
            return False
    
    async def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        return {
            "total_entries": self.stats.total_entries,
            "total_size_mb": self.stats.total_size_bytes / (1024 * 1024),
            "max_size_mb": self.max_memory_mb,
            "usage_percent": (self.stats.total_size_bytes / (1024 * 1024)) / self.max_memory_mb * 100,
            "cache_hit_rate": self.stats.cache_hits / max(1, self.stats.cache_hits + self.stats.cache_misses),
            "cache_hits": self.stats.cache_hits,
            "cache_misses": self.stats.cache_misses,
            "evictions": self.stats.evictions,
            "memory_by_type": dict(self.stats.memory_by_type),
            "memory_by_priority": dict(self.stats.memory_by_priority),
            "context_windows": len(self.context_windows)
        }
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]):
        """Emit an event to registered handlers"""
        handlers = self.event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                await handler(data)
            except Exception as e:
                self.logger.error(f"Error in event handler: {e}")
    
    def add_event_handler(self, event_type: str, handler):
        """Add an event handler"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    async def search_by_tags(self, tags: List[str]) -> List[str]:
        """Search for entries by tags"""
        matching_keys = []
        
        for key, entry in self.memory_entries.items():
            if any(tag in entry.tags for tag in tags):
                matching_keys.append(key)
        
        return matching_keys
    
    async def get_dependencies(self, key: str) -> List[str]:
        """Get dependencies for a key"""
        if key in self.memory_entries:
            return self.memory_entries[key].dependencies
        return []
    
    async def invalidate_dependents(self, key: str):
        """Invalidate entries that depend on this key"""
        for entry_key, entry in self.memory_entries.items():
            if key in entry.dependencies:
                await self.delete(entry_key)
                self.logger.debug(f"Invalidated dependent entry {entry_key}")


# Example usage
async def main():
    memory_manager = ContextMemoryManager()
    await memory_manager.start()
    
    # Store some data
    await memory_manager.store(
        "document_analysis_001",
        {"content": "Political document analysis...", "sentiment": "neutral"},
        MemoryType.RESULT,
        Priority.HIGH,
        tags=["document", "analysis", "political"]
    )
    
    # Create context window
    window_id = await memory_manager.create_context_window(
        "session_001",
        "document_analysis",
        {"messages": ["Hello", "Analyze this document"], "user": "analyst"}
    )
    
    # Retrieve data
    result = await memory_manager.retrieve("document_analysis_001")
    print(f"Retrieved: {result}")
    
    # Get stats
    stats = await memory_manager.get_memory_stats()
    print(f"Memory stats: {stats}")
    
    await memory_manager.stop()

if __name__ == "__main__":
    asyncio.run(main())