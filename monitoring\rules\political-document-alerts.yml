groups:
  - name: political-document-processing
    rules:
      # Service Health Alerts
      - alert: ServiceDown
        expr: up{job=~"n8n|mcp-.*"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute."

      # Document Processing Alerts
      - alert: DocumentProcessingStalled
        expr: rate(political_documents_processed_total[5m]) == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Document processing has stalled"
          description: "No documents have been processed in the last 5 minutes."

      - alert: HighDocumentProcessingErrors
        expr: rate(political_document_generation_errors_total[5m]) > 0.1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "High document processing error rate"
          description: "Document processing error rate is {{ $value }} errors per second."

      # ChromaDB Vector Search Alerts
      - alert: SlowVectorSearch
        expr: histogram_quantile(0.95, rate(chromadb_search_duration_seconds_bucket[5m])) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Vector search is slow"
          description: "95th percentile search time is {{ $value }}s, which exceeds 10s threshold."

      - alert: ChromaDBUnhealthy
        expr: up{job="chromadb"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "ChromaDB is down"
          description: "ChromaDB vector database is not responding."

      # MCP Server Performance Alerts
      - alert: HighMCPResponseTime
        expr: histogram_quantile(0.95, rate(mcp_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High MCP server response time"
          description: "MCP server {{ $labels.job }} 95th percentile response time is {{ $value }}s."

      # Authentication and Security Alerts
      - alert: HighSecurityViolations
        expr: rate(security_violations_total[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High security violation rate"
          description: "Security violation rate is {{ $value }} violations per second."

      - alert: OAuthFailures
        expr: rate(oauth_authentication_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High OAuth authentication failure rate"
          description: "OAuth failure rate is {{ $value }} failures per second."

      # Resource Usage Alerts
      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes{name=~".*political.*"} / container_spec_memory_limit_bytes{name=~".*political.*"}) > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage for {{ $labels.name }}"
          description: "Container {{ $labels.name }} is using {{ $value | humanizePercentage }} of available memory."

      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total{name=~".*political.*"}[5m]) > 0.8
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage for {{ $labels.name }}"
          description: "Container {{ $labels.name }} is using {{ $value | humanizePercentage }} CPU."

      # Database Alerts
      - alert: PostgreSQLConnectionsHigh
        expr: postgresql_connections_active / postgresql_connections_max > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL connection pool nearly exhausted"
          description: "PostgreSQL is using {{ $value | humanizePercentage }} of available connections."

      # AI Model Performance Alerts
      - alert: AIModelHighLatency
        expr: histogram_quantile(0.95, rate(ai_model_response_time_seconds_bucket[5m])) > 30
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High AI model response time"
          description: "AI model {{ $labels.model }} 95th percentile response time is {{ $value }}s."

      - alert: FactCheckingAccuracyLow
        expr: fact_check_accuracy_score < 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Fact-checking accuracy below threshold"
          description: "Fact-checking accuracy is {{ $value }}, below 85% threshold."