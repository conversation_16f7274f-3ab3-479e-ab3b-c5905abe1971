# MVP Manifesto-Aligned Document Processor - Complete Documentation

## Overview

The MVP Manifesto-Aligned Document Processor is a complete n8n workflow system that processes political documents through AI agents guided by New American Patriotism principles. This system ensures all document analysis, enhancement, and output serves the manifesto's transformative goals.

## Quick Start

### Prerequisites
- n8n instance (cloud or self-hosted)
- Anthropic API key (for Claude <PERSON>)
- Basic understanding of JSON and webhook operations

### Installation Steps

1. **Import Workflow**
   ```bash
   # In n8n interface:
   # 1. Go to Workflows
   # 2. Click "Import from File"
   # 3. Upload mvp_n8n_implementation_fixed.json
   ```

2. **Configure API Credentials**
   ```bash
   # In n8n Credentials:
   # 1. Add "Anthropic API" credential
   # 2. Enter your API key
   # 3. Test connection
   ```

3. **Activate Webhook**
   ```bash
   # The workflow creates webhook endpoint:
   # https://your-n8n-instance.com/webhook/process-document
   ```

### Basic Usage

#### Submit Document for Processing
```bash
curl -X POST https://your-n8n-instance.com/webhook/process-document \
  -H "Content-Type: application/json" \
  -d '{
    "body": {
      "content": "Your document text here...",
      "type": "policy_document"
    }
  }'
```

#### Response Format
```json
{
  "success": true,
  "title": "Healthcare as a Human Right: Manifesto Analysis",
  "content": "Enhanced document content...",
  "manifesto_grade": "A",
  "quality_control_passed": true,
  "metadata": {
    "processed_date": "2024-01-15T10:30:00Z",
    "manifesto_alignment_grade": "A",
    "passes_quality_control": true
  }
}
```

## Workflow Architecture

### Phase 1: Document Input & Context Loading

#### Node 1: Document Input (Webhook)
- **Purpose**: Accept document submissions via HTTP POST
- **Endpoint**: `/webhook/process-document`
- **Accepts**: JSON with `body.content` field
- **Rate Limiting**: Enabled for security

#### Node 2: Load Manifesto Context
- **Purpose**: Inject manifesto principles into processing pipeline
- **Function**: Loads core principles and formats for AI context
- **Output**: Enhanced JSON with manifesto context and principles

**Key Principles Loaded**:
- New American Patriotism: Love of fellow citizens over blind loyalty
- Transformative over incremental: Constitutional-level solutions
- People over profit: Working families first, universal access
- Unity over division: Build bridges while confronting unjust systems
- Radical transparency: Complete accountability, no immunity
- Universal rights: Healthcare, education, housing as human rights

#### Node 3: Extract Document Content
- **Purpose**: Normalize and analyze incoming content
- **Handles**: Multiple input formats (text, JSON, markdown)
- **Analysis**: Word count, content type detection, basic categorization
- **Output**: Structured document data with metadata

### Phase 2: AI Analysis & Routing

#### Node 4: Lead Coordinator Agent (Claude AI)
- **Model**: Claude-3-Sonnet
- **Purpose**: Analyze document and determine optimal processing strategy
- **Input**: Document content + manifesto context
- **Output**: JSON with alignment score, recommended action, reasoning

**Analysis Criteria**:
- Manifesto alignment assessment (0.0-1.0 score)
- Content theme identification
- Recommended processing action (analyze/enhance/critique)
- Reasoning for routing decision

#### Node 5: Routing Decision Engine
- **Type**: Switch node with conditional logic
- **Function**: Routes to appropriate specialist agent based on analysis
- **Routing Options**:
  - Route 0: Policy Analysis Agent (for "analyze" action)
  - Route 1: Editorial Enhancement Agent (for "enhance" action)  
  - Route 2: Research Agent (for "critique" action)
  - Route 3: Human Review Queue (fallback)

### Phase 3: Specialized Processing

#### Route 0: Policy Analysis Agent
- **Specialization**: Constitutional policy analysis
- **Focus Areas**:
  - Constitutional amendment opportunities
  - Universal program potential
  - Anti-corruption measures
  - Economic justice implications
  - Individual sovereignty protections
- **Output**: Detailed policy analysis with constitutional recommendations

#### Route 1: Editorial Enhancement Agent
- **Specialization**: Message enhancement for manifesto alignment
- **Enhancement Criteria**:
  - Passionate but principled tone
  - Accessible language for working families
  - Hopeful vision with concrete steps
  - Systemic solutions emphasis
  - Unity-building while confronting injustice
- **Output**: Enhanced document with manifesto-aligned messaging

#### Route 2: Research Agent
- **Specialization**: Research-backed critical analysis
- **Research Priorities**:
  - Constitutional amendment precedents
  - Universal program success stories globally
  - Corporate power concentration evidence
  - Working family impact data
  - Implementation feasibility analysis
- **Output**: Research findings supporting manifesto conclusions

#### Route 3: Human Review Queue
- **Trigger**: When AI coordinator cannot determine optimal path
- **Function**: Queue document for human review with metadata
- **Information Provided**: Analysis summary, priority level, timestamps

### Phase 4: Quality Control & Output

#### Node 7: Quality Control Validator
- **Purpose**: Validate AI output against manifesto principles
- **Validation Metrics**:
  - Transformative score (constitutional/systemic content)
  - People-first score (working families/universal programs)
  - Average alignment score
- **Grading**: A-D scale based on alignment scores
- **Pass Threshold**: 50% average alignment score

**Scoring Algorithm**:
```javascript
// Transformative score components
- Constitutional content: 30% weight
- Amendment language: 20% weight  
- Systemic solutions: 20% weight
- Universal programs: 30% weight

// People-first score components
- Working families mentions: 30% weight
- Healthcare/education/housing: 30% weight
- Public good emphasis: 20% weight
- Corporate power critique: 20% weight
```

#### Node 8: Output Formatter
- **Purpose**: Professional presentation of final output
- **Features**:
  - Auto-generated manifesto-aligned titles
  - Consistent formatting with signature
  - Metadata embedding
  - Quality control indicators

**Title Generation Logic**:
- Healthcare content → "Healthcare as a Human Right: Manifesto Analysis"
- Education content → "Universal Education: Building Prosperity"  
- Housing content → "Right to Housing: Constitutional Framework"
- Campaign/election → "Reclaiming Democracy: Campaign Finance Reform"
- Default → "Policy Analysis: Advancing New American Patriotism"

#### Node 9: Distribution
- **Purpose**: Final output delivery and logging
- **Output Format**: Structured JSON with all metadata
- **Logging**: Processing completion with key metrics
- **Integration Points**: Ready for email/webhook/file storage

## Configuration Guide

### API Configuration

#### Anthropic API Setup
```json
{
  "name": "anthropicApi",
  "type": "anthropicApi",
  "data": {
    "apiKey": "your-anthropic-api-key-here"
  }
}
```

#### Webhook Security
```json
{
  "authentication": "headerAuth",
  "httpMethod": "POST",
  "responseMode": "responseNode",
  "options": {
    "allowedOrigins": "your-domain.com",
    "rateLimit": "100/hour"
  }
}
```

### Custom Manifesto Principles

To update manifesto principles, modify the `Load Manifesto Context` node:

```javascript
const manifestoPrinciples = {
  "new_american_patriotism": "Your updated definition...",
  "transformative_over_incremental": "Your updated definition...",
  // Add new principles here
  "custom_principle": "Your custom principle..."
};
```

### Quality Control Thresholds

Adjust validation thresholds in `Quality Control Validator` node:

```javascript
// Modify these values to change sensitivity
const gradeThresholds = {
  A: 0.8,  // 80% alignment required for A grade
  B: 0.6,  // 60% alignment required for B grade  
  C: 0.4,  // 40% alignment required for C grade
  D: 0.2   // Below 20% receives D grade
};

const passThreshold = 0.5; // 50% required to pass QC
```

## Advanced Usage

### Batch Processing

Process multiple documents by sending array:

```bash
curl -X POST https://your-n8n-instance.com/webhook/process-document \
  -H "Content-Type: application/json" \
  -d '{
    "body": {
      "documents": [
        {"content": "Document 1...", "type": "policy"},
        {"content": "Document 2...", "type": "speech"},
        {"content": "Document 3...", "type": "research"}
      ]
    }
  }'
```

### Custom Processing Instructions

Include specific instructions in your request:

```json
{
  "body": {
    "content": "Your document...",
    "type": "policy_document",
    "processing_instructions": {
      "focus_area": "constitutional_amendments",
      "output_style": "technical_brief",
      "target_audience": "policy_makers"
    }
  }
}
```

### Integration with External Systems

#### Email Notifications
Add email node after Distribution:
```json
{
  "type": "@n8n/n8n-nodes-base.emailSend",
  "parameters": {
    "to": "<EMAIL>",
    "subject": "Document Processing Complete: {{ $json.title }}",
    "text": "Grade: {{ $json.manifesto_grade }}\n\n{{ $json.content }}"
  }
}
```

#### File Storage
Add Google Drive node for permanent storage:
```json
{
  "type": "@n8n/n8n-nodes-base.googleDrive",
  "parameters": {
    "operation": "upload",
    "name": "{{ $json.title }}.md",
    "parents": "processed-documents-folder-id"
  }
}
```

## Monitoring & Maintenance

### Performance Metrics

Monitor these key indicators:

1. **Processing Success Rate**: Percentage of documents completing successfully
2. **Manifesto Alignment Scores**: Average grade distribution (A/B/C/D)
3. **Quality Control Pass Rate**: Percentage passing QC validation
4. **Agent Routing Distribution**: Which agents handle most documents
5. **Processing Time**: Average time from input to output

### Log Analysis

Check n8n execution logs for:

```json
{
  "processing_complete": {
    "title": "Document title",
    "grade": "A|B|C|D", 
    "passed_qc": true|false,
    "agent_type": "policy|editorial|research",
    "processing_time_ms": 15000
  }
}
```

### Error Handling

Common issues and solutions:

1. **API Rate Limits**
   ```
   Error: "Rate limit exceeded"
   Solution: Add delay nodes between requests
   ```

2. **Invalid JSON from AI**
   ```
   Error: "Cannot parse JSON response"
   Solution: Add error handling in routing logic
   ```

3. **Long Documents**
   ```
   Error: "Token limit exceeded"  
   Solution: Add document chunking in content extraction
   ```

## Extension Guide

### Adding New Specialist Agents

1. **Create New Agent Node**
   ```json
   {
     "name": "Legislative Analysis Agent",
     "type": "@n8n/n8n-nodes-base.httpRequest",
     "parameters": {
       "url": "https://api.anthropic.com/v1/messages",
       "jsonBody": "{\"model\": \"claude-3-sonnet-********\", \"messages\": [{\"role\": \"user\", \"content\": \"SPECIALIZATION: Legislative Analysis...\"}]}"
     }
   }
   ```

2. **Update Routing Logic**
   ```javascript
   // Add new case to switch node
   switch(recommended_action) {
     case 'legislative_analysis':
       return [4]; // New Legislative Agent route
     // ... existing cases
   }
   ```

3. **Update Coordinator Prompt**
   ```javascript
   // Add new action to coordinator's recommended_action options
   "recommended_action": "analyze|enhance|critique|legislative_analysis"
   ```

### Custom Validation Rules

Add new validation criteria:

```javascript
function validateAgainstManifesto(content) {
  // Existing validations...
  
  // Add new validation
  const environmentalScore = (
    (text.includes('climate') ? 0.3 : 0) +
    (text.includes('renewable') ? 0.3 : 0) +
    (text.includes('green jobs') ? 0.4 : 0)
  );
  
  return {
    // ... existing scores
    environmental_score: Math.min(environmentalScore, 1.0)
  };
}
```

### External Tool Integration

#### Web Research Integration
```javascript
// Add to Research Agent
const webSearchResults = await fetch('https://api.perplexity.ai/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + perplexityApiKey,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    model: 'pplx-7b-online',
    messages: [{ role: 'user', content: researchQuery }]
  })
});
```

#### Document Database Integration
```javascript
// Add document storage after processing
const dbResult = await fetch('https://your-api.com/documents', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: formattedOutput.title,
    content: formattedOutput.content,
    manifesto_grade: overallGrade,
    processed_date: new Date().toISOString()
  })
});
```

## Testing Strategy

### Unit Testing

Test individual nodes with sample data:

```bash
# Test manifesto context loading
curl -X POST https://your-n8n-instance.com/test/load-context \
  -d '{"test": "data"}'

# Test document extraction  
curl -X POST https://your-n8n-instance.com/test/extract-content \
  -d '{"body": {"content": "Sample document text"}}'
```

### Integration Testing

Test complete workflow with known documents:

```bash
# Test manifesto-aligned document (should score high)
curl -X POST https://your-n8n-instance.com/webhook/process-document \
  -d '{
    "body": {
      "content": "Constitutional amendment to end corporate money in politics and establish universal healthcare as a human right for all working families...",
      "type": "test_manifesto_aligned"
    }
  }'

# Test corporate/status-quo document (should be flagged for enhancement)
curl -X POST https://your-n8n-instance.com/webhook/process-document \
  -d '{
    "body": {
      "content": "Market-based healthcare solutions with incremental reforms and public-private partnerships...",
      "type": "test_status_quo"
    }
  }'
```

### Expected Test Results

1. **Manifesto-Aligned Content**:
   - Grade: A or B
   - Quality Control: PASSED
   - High transformative and people-first scores

2. **Status-Quo Content**:
   - Grade: C or D  
   - Routed to Enhancement Agent
   - Enhanced output with manifesto principles

3. **Ambiguous Content**:
   - Routed to Human Review Queue
   - Pending review status
   - Complete metadata for human evaluation

## Troubleshooting

### Common Issues

#### 1. Workflow Not Triggering
```
Symptoms: No response from webhook
Diagnosis: Check webhook URL and activation status
Solution: Ensure webhook node is properly configured and workflow is active
```

#### 2. AI API Errors
```
Symptoms: "API key invalid" or "Model not found"
Diagnosis: Check Anthropic API credentials
Solution: Verify API key in credentials and model availability
```

#### 3. Low Quality Scores
```
Symptoms: All documents receiving C/D grades
Diagnosis: Validation thresholds may be too strict
Solution: Adjust scoring thresholds in Quality Control node
```

#### 4. Memory/Timeout Issues
```
Symptoms: Workflow fails on large documents
Diagnosis: Document size exceeding limits
Solution: Implement document chunking or increase timeout limits
```

### Debug Mode

Enable detailed logging by adding debug nodes:

```javascript
// Add after each major step
console.log('Debug - Current state:', JSON.stringify(item.json, null, 2));
return item;
```

### Support

For additional support:
1. Check n8n execution logs
2. Review manifesto principles alignment
3. Test with simplified document content
4. Verify all API credentials are current

This documentation provides everything needed to deploy, use, and extend the MVP Manifesto-Aligned Document Processor successfully. 