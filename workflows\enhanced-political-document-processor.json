{"name": "Enhanced Political Document Processor", "active": true, "nodes": [{"parameters": {"triggerOn": "webhookReceived", "httpMethod": "POST", "path": "process-document-enhanced", "options": {}}, "id": "webhook-trigger", "name": "Document Processing Webhook", "type": "n8n-nodes-base.webhook", "position": [240, 300], "webhookId": "enhanced-political-doc-processor"}, {"parameters": {"functionCode": "// Generate unique job ID\nconst timestamp = Date.now();\nconst random = Math.random().toString(36).substr(2, 9);\nconst jobId = `job_${timestamp}_${random}`;\n\nreturn {\n  jobId,\n  timestamp,\n  requestData: $input.all()[0].json.body\n};"}, "id": "generate-job-id", "name": "Generate Job ID", "type": "n8n-nodes-base.code", "position": [450, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO document_processing_jobs (job_id, filename, file_path, document_type, processing_status, task_type, manifesto_category, token_tier, processing_start, processing_metadata) VALUES ($1, $2, $3, $4, 'pending', $5, $6, $7, CURRENT_TIMESTAMP, $8)", "additionalFields": {"queryParameters": "={{ [$node[\"Generate Job ID\"].json.jobId, $json.body.filename, $json.body.filePath, $json.body.documentType, $json.body.taskType, $json.body.category, $json.body.tokenTier, JSON.stringify($json.body.metadata || {})] }}"}}, "id": "create-job-record", "name": "Create Job Record", "type": "n8n-nodes-base.postgres", "position": [660, 300], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.requestData.taskType }}", "rightValue": "generate_whitepaper", "operator": {"type": "string", "operation": "equals", "singleValue": true}}, {"id": "condition-2", "leftValue": "={{ $json.requestData.taskType }}", "rightValue": "generate_policy_brief", "operator": {"type": "string", "operation": "equals", "singleValue": true}}, {"id": "condition-3", "leftValue": "={{ $json.requestData.taskType }}", "rightValue": "edit_document", "operator": {"type": "string", "operation": "equals", "singleValue": true}}]}, "options": {"looseTypeValidation": true}}, "id": "task-router", "name": "Task Router", "type": "n8n-nodes-base.if", "position": [860, 300]}, {"parameters": {"url": "http://mcp-vector-search:8088/mcp/call", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "get_document_context"}, {"name": "parameters", "value": "={{ JSON.stringify({\n  topic: $json.requestData.topic,\n  category: $json.requestData.category,\n  token_limit: $json.requestData.tokenTier * 10000, // Convert tier to tokens\n  include_manifesto: false\n}) }}"}]}, "options": {"timeout": 30000}}, "id": "vector-search-context", "name": "Vector Search Context", "type": "n8n-nodes-base.httpRequest", "position": [1060, 200]}, {"parameters": {"url": "http://mcp-vector-search:8088/mcp/call", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "search_similar_content"}, {"name": "parameters", "value": "={{ JSON.stringify({\n  query: $json.requestData.topic,\n  category: $json.requestData.category,\n  limit: 5,\n  minimum_similarity: 0.7\n}) }}"}]}, "options": {"timeout": 30000}}, "id": "similar-documents", "name": "Find Similar Documents", "type": "n8n-nodes-base.httpRequest", "position": [1060, 100]}, {"parameters": {"server": "research-integration", "tool": "research_topic", "arguments": {"topic": "={{ $json.requestData.topic }}", "research_depth": "comprehensive", "focus_areas": ["policy", "implementation", "international_examples"]}}, "id": "research-topic", "name": "Research Topic", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1280, 200]}, {"parameters": {"server": "manifesto-context", "tool": "get_context_by_tier", "arguments": {"tier": "={{ $json.requestData.tokenTier }}", "category": "={{ $json.requestData.category }}", "include_voice_guide": true}}, "id": "get-manifesto-context", "name": "Get Manifesto Context", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1280, 100]}, {"parameters": {"functionCode": "// Combine context from vector search, manifesto, and research\nconst vectorContext = $node[\"Vector Search Context\"].json;\nconst similarDocs = $node[\"Find Similar Documents\"].json;\nconst researchData = $node[\"Research Topic\"].json;\nconst manifestoContext = $node[\"Get Manifesto Context\"].json;\nconst requestData = $node[\"Generate Job ID\"].json.requestData;\n\nreturn {\n  jobId: $node[\"Generate Job ID\"].json.jobId,\n  requestData,\n  enhancedContext: {\n    vectorSearch: vectorContext,\n    similarDocuments: similarDocs,\n    research: researchData,\n    manifesto: manifestoContext\n  }\n};"}, "id": "combine-context", "name": "Combine Context", "type": "n8n-nodes-base.code", "position": [1480, 200]}, {"parameters": {"server": "political-content", "tool": "generate_white_paper", "arguments": {"topic": "={{ $json.requestData.topic }}", "category": "={{ $json.requestData.category }}", "token_tier": "={{ $json.requestData.tokenTier }}", "research_level": "={{ $json.requestData.researchLevel || 'comprehensive' }}", "output_format": "={{ $json.requestData.outputFormat || 'markdown' }}"}, "options": {"timeout": 120000}}, "id": "generate-whitepaper", "name": "Generate White Paper", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1680, 200]}, {"parameters": {"server": "quality-control", "tool": "review_document", "arguments": {"document_id": "={{ $node[\"Generate White Paper\"].json.jobId }}", "review_type": "full", "strict_mode": true}}, "id": "quality-review", "name": "Quality Review", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [1880, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "approve-condition", "leftValue": "={{ $json.approved }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equals"}}]}, "options": {"looseTypeValidation": true}}, "id": "quality-gate", "name": "Quality Gate", "type": "n8n-nodes-base.if", "position": [2080, 200]}, {"parameters": {"server": "document-processing", "tool": "convert_document_cloudconvert", "arguments": {"input_file_path": "={{ $node[\"Generate White Paper\"].json.file_path }}", "output_format": "={{ $json.requestData.outputFormat || 'pdf' }}"}}, "id": "convert-document", "name": "Convert Document", "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "position": [2280, 120]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE document_processing_jobs SET processing_status = 'completed', processing_end = CURRENT_TIMESTAMP, output_file_path = $1 WHERE job_id = $2", "additionalFields": {"queryParameters": "={{ [$node[\"Convert Document\"].json.output_file_path, $json.jobId] }}"}}, "id": "update-job-completed", "name": "Update Job Completed", "type": "n8n-nodes-base.postgres", "position": [2480, 120], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE document_processing_jobs SET processing_status = 'failed', processing_end = CURRENT_TIMESTAMP, error_message = $1 WHERE job_id = $2", "additionalFields": {"queryParameters": "={{ [$json.error_message, $json.jobId] }}"}}, "id": "update-job-failed", "name": "Update Job Failed", "type": "n8n-nodes-base.postgres", "position": [2280, 280], "credentials": {"postgres": {"id": "political-postgres-creds", "name": "Political Conversations DB"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: true,\n  jobId: $json.jobId,\n  status: 'completed',\n  documentPath: $node[\"Convert Document\"].json.output_file_path,\n  qualityScores: $node[\"Quality Review\"].json.scores,\n  metadata: {\n    vectorSearchUsed: true,\n    documentsAnalyzed: $node[\"Vector Search Context\"].json.metadata?.documents_included || 0,\n    researchSources: $node[\"Research Topic\"].json.source_count || 0,\n    processingTime: new Date().toISOString()\n  }\n} }}"}, "id": "success-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "position": [2680, 120]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  success: false,\n  jobId: $json.jobId,\n  status: 'failed',\n  error: $json.error_message,\n  qualityScores: $node[\"Quality Review\"].json.scores,\n  metadata: {\n    vectorSearchUsed: true,\n    failureReason: 'quality_gate_failed',\n    processingTime: new Date().toISOString()\n  }\n} }}"}, "id": "failure-response", "name": "Failure Response", "type": "n8n-nodes-base.respondToWebhook", "position": [2680, 280]}], "connections": {"Document Processing Webhook": {"main": [[{"node": "Generate Job ID", "type": "main", "index": 0}]]}, "Generate Job ID": {"main": [[{"node": "Create Job Record", "type": "main", "index": 0}]]}, "Create Job Record": {"main": [[{"node": "Task Router", "type": "main", "index": 0}]]}, "Task Router": {"main": [[{"node": "Vector Search Context", "type": "main", "index": 0}, {"node": "Find Similar Documents", "type": "main", "index": 0}, {"node": "Research Topic", "type": "main", "index": 0}, {"node": "Get Manifesto Context", "type": "main", "index": 0}]]}, "Vector Search Context": {"main": [[{"node": "Combine Context", "type": "main", "index": 0}]]}, "Find Similar Documents": {"main": [[{"node": "Combine Context", "type": "main", "index": 0}]]}, "Research Topic": {"main": [[{"node": "Combine Context", "type": "main", "index": 0}]]}, "Get Manifesto Context": {"main": [[{"node": "Combine Context", "type": "main", "index": 0}]]}, "Combine Context": {"main": [[{"node": "Generate White Paper", "type": "main", "index": 0}]]}, "Generate White Paper": {"main": [[{"node": "Quality Review", "type": "main", "index": 0}]]}, "Quality Review": {"main": [[{"node": "Quality Gate", "type": "main", "index": 0}]]}, "Quality Gate": {"main": [[{"node": "Convert Document", "type": "main", "index": 0}], [{"node": "Update Job Failed", "type": "main", "index": 0}]]}, "Convert Document": {"main": [[{"node": "Update Job Completed", "type": "main", "index": 0}]]}, "Update Job Completed": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Update Job Failed": {"main": [[{"node": "Failure Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [{"createdAt": "2025-07-17T00:00:00.000Z", "updatedAt": "2025-07-17T00:00:00.000Z", "id": "political-enhanced", "name": "Political Enhanced"}], "triggerCount": 0, "updatedAt": "2025-07-17T00:00:00.000Z", "versionId": "enhanced-v1"}