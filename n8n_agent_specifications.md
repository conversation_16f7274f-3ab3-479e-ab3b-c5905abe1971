# n8n Agent Specifications - Technical Implementation Guide

## Document Overview

This document provides detailed technical specifications for implementing each specialized agent as individual n8n workflows. Each agent will be exposed as an MCP tool and called by the Lead Coordinator Agent.

## **Lead Coordinator Agent Implementation**

### **Workflow Name**: `lead_coordinator_agent`
### **MCP Tool Name**: `lead_coordinator`

#### **Node Structure**
```
MCP Server Trigger → Read PROMPT → Read Manifesto → Analyze Batch → Create Task Plan → Distribute Tasks → Monitor Progress → Respond to MCP
```

#### **Node 1: MCP Server Trigger**
- **Tool Name**: `lead_coordinator`
- **Description**: "Coordinates batch document processing by analyzing requirements and delegating to specialized agents"
- **Parameters Schema**:
```json
{
  "type": "object",
  "properties": {
    "documents": {
      "type": "array",
      "items": {"type": "string"},
      "description": "Array of document contents to process"
    },
    "prompt_content": {
      "type": "string",
      "description": "Content of PROMPT.md with batch instructions"
    },
    "manifesto_content": {
      "type": "string",
      "description": "Content of manifesto.md for context"
    },
    "batch_id": {
      "type": "string",
      "description": "Unique identifier for this batch"
    }
  },
  "required": ["documents", "prompt_content", "manifesto_content", "batch_id"]
}
```

#### **Node 2: Code Node (Analyze Batch)**
```javascript
const { documents, prompt_content, manifesto_content, batch_id } = $input.item.json;

// Analyze each document and create processing plan
const analysisPrompt = `
You are a Lead Coordinator Agent responsible for intelligent document processing coordination.

MANIFESTO (Your guiding principles):
${manifesto_content}

BATCH PROCESSING INSTRUCTIONS:
${prompt_content}

DOCUMENTS TO PROCESS:
${documents.map((doc, index) => `Document ${index + 1}:\n${doc}`).join('\n\n---\n\n')}

Your task is to analyze these documents and create a comprehensive processing plan. For each document, determine:
1. Primary processing type (summary, analysis, whitepaper, research, editorial, legislative)
2. Secondary processing needed (fact-checking, web research, formatting)
3. Output requirements (length, style, audience)
4. Dependencies between documents
5. Quality control requirements

Respond with a structured JSON plan:
{
  "batch_summary": "Brief overview of the batch",
  "processing_plan": [
    {
      "document_index": 0,
      "document_title": "extracted or generated title",
      "primary_agent": "agent_name",
      "secondary_agents": ["agent_name1", "agent_name2"],
      "processing_type": "summary|analysis|whitepaper|research|editorial|legislative",
      "output_requirements": {
        "length": "brief|medium|comprehensive",
        "style": "formal|academic|executive|policy",
        "audience": "general|technical|executive|legislative"
      },
      "special_instructions": "any specific requirements",
      "dependencies": [other_document_indices],
      "priority": "high|medium|low"
    }
  ],
  "quality_control_requirements": {
    "fact_checking": true/false,
    "web_research": true/false,
    "cross_reference": true/false,
    "formatting_level": "basic|professional|executive"
  },
  "estimated_completion_time": "time estimate",
  "processing_order": [document_indices_in_order]
}
`;

return [{
  json: {
    analysis_prompt: analysisPrompt,
    batch_id: batch_id,
    document_count: documents.length
  }
}];
```

#### **Node 3: HTTP Request (Call AI for Analysis)**
- **Method**: POST
- **URL**: OpenAI o1 API endpoint
- **Headers**: Authorization with API key
- **Body**: Structured prompt from previous node

#### **Node 4: Code Node (Parse and Distribute)**
```javascript
const analysis = JSON.parse($input.item.json.choices[0].message.content);
const { batch_id } = $input.item.json;

// Create task distribution plan
const tasks = analysis.processing_plan.map(plan => ({
  task_id: `${batch_id}_${plan.document_index}`,
  agent: plan.primary_agent,
  document_content: $node["MCP Server Trigger"].json.documents[plan.document_index],
  processing_requirements: plan,
  manifesto_content: $node["MCP Server Trigger"].json.manifesto_content,
  batch_context: analysis.batch_summary
}));

return [{
  json: {
    processing_plan: analysis,
    tasks: tasks,
    batch_id: batch_id
  }
}];
```

## **Research Agent Implementation**

### **Workflow Name**: `research_agent`
### **MCP Tool Name**: `research_agent`

#### **Node Structure**
```
MCP Server Trigger → Parse Requirements → Web Research → Fact Check → Document Analysis → Format Output → Respond to MCP
```

#### **Node 1: MCP Server Trigger**
- **Tool Name**: `research_agent`
- **Description**: "Conducts web research and fact-checking for document analysis"
- **Parameters Schema**:
```json
{
  "type": "object",
  "properties": {
    "document_content": {"type": "string"},
    "research_requirements": {
      "type": "object",
      "properties": {
        "fact_check": {"type": "boolean"},
        "current_events": {"type": "boolean"},
        "background_research": {"type": "boolean"},
        "verification_needed": {"type": "array", "items": {"type": "string"}}
      }
    },
    "manifesto_content": {"type": "string"},
    "task_id": {"type": "string"}
  },
  "required": ["document_content", "research_requirements", "task_id"]
}
```

#### **Node 2: Code Node (Extract Research Topics)**
```javascript
const { document_content, research_requirements, manifesto_content } = $input.item.json;

// Extract key claims and topics that need research
const extractionPrompt = `
Analyze this document and identify:
1. Factual claims that need verification
2. Statistics or data that should be fact-checked
3. Current events references that need updating
4. Background topics that need additional research

Document:
${document_content}

Return a JSON array of research topics:
[
  {
    "topic": "specific topic or claim",
    "type": "fact_check|current_events|background|verification",
    "priority": "high|medium|low",
    "search_terms": ["term1", "term2", "term3"]
  }
]
`;

return [{
  json: {
    extraction_prompt: extractionPrompt,
    original_document: document_content,
    manifesto_content: manifesto_content
  }
}];
```

#### **Node 3: HTTP Request (Extract Research Topics)**
- **Method**: POST
- **URL**: Gemini 2.5 Pro API endpoint
- **Body**: Research topic extraction prompt

#### **Node 4: Split In Batches (Research Topics)**
- **Batch Size**: 3 (process 3 research topics at a time)

#### **Node 5: Playwright Navigate (Web Research)**
- **URL**: `https://www.google.com/search?q={{ $json.search_terms.join('+') }}`

#### **Node 6: Code Node (Collect Research Results)**
```javascript
// Collect and structure research findings
const researchFindings = $input.all().map(item => ({
  topic: item.json.topic,
  type: item.json.type,
  findings: item.json.research_results,
  sources: item.json.sources,
  reliability_score: item.json.reliability_score
}));

return [{
  json: {
    research_findings: researchFindings,
    research_complete: true
  }
}];
```

## **Policy Agent Implementation**

### **Workflow Name**: `policy_agent`
### **MCP Tool Name**: `policy_agent`

#### **Node Structure**
```
MCP Server Trigger → Analyze Policy Context → Regulatory Review → Impact Assessment → Generate Analysis → Respond to MCP
```

#### **Node 1: MCP Server Trigger**
- **Tool Name**: `policy_agent`
- **Description**: "Provides deep policy analysis and regulatory compliance review"
- **Parameters Schema**:
```json
{
  "type": "object",
  "properties": {
    "document_content": {"type": "string"},
    "analysis_type": {
      "type": "string", 
      "enum": ["policy_analysis", "regulatory_compliance", "impact_assessment", "strategic_recommendations"]
    },
    "manifesto_content": {"type": "string"},
    "research_context": {"type": "string"},
    "task_id": {"type": "string"}
  },
  "required": ["document_content", "analysis_type", "task_id"]
}
```

#### **Node 2: Code Node (Policy Analysis Prompt)**
```javascript
const { document_content, analysis_type, manifesto_content, research_context } = $input.item.json;

const policyPrompt = `
You are a senior policy analyst with expertise in regulatory compliance, strategic planning, and systemic policy analysis.

GUIDING PRINCIPLES:
${manifesto_content}

RESEARCH CONTEXT:
${research_context || 'No additional research context provided'}

DOCUMENT TO ANALYZE:
${document_content}

ANALYSIS TYPE: ${analysis_type}

Provide a comprehensive policy analysis that includes:

1. POLICY CONTEXT ANALYSIS
   - Current regulatory landscape
   - Relevant legislation and precedents
   - Stakeholder implications

2. STRATEGIC IMPLICATIONS
   - Short-term impacts (6-12 months)
   - Medium-term impacts (1-3 years)
   - Long-term strategic considerations (3+ years)

3. REGULATORY COMPLIANCE
   - Compliance requirements
   - Potential regulatory hurdles
   - Mitigation strategies

4. IMPLEMENTATION PATHWAY
   - Recommended implementation sequence
   - Resource requirements
   - Timeline considerations

5. RISK ASSESSMENT
   - Political risks
   - Legal risks
   - Implementation risks
   - Mitigation strategies

6. STRATEGIC RECOMMENDATIONS
   - Priority actions
   - Success metrics
   - Monitoring requirements

Format your response as a structured policy analysis document with clear sections and actionable recommendations.
`;

return [{
  json: {
    policy_prompt: policyPrompt,
    analysis_type: analysis_type
  }
}];
```

#### **Node 3: HTTP Request (Policy Analysis)**
- **Method**: POST
- **URL**: OpenAI o1 API endpoint (for complex reasoning)
- **Body**: Policy analysis prompt

## **Summary Agent Implementation**

### **Workflow Name**: `summary_agent`
### **MCP Tool Name**: `summary_agent`

#### **Node Structure**
```
MCP Server Trigger → Determine Summary Type → Generate Summary → Format Output → Respond to MCP
```

#### **Node 1: MCP Server Trigger**
- **Tool Name**: `summary_agent`
- **Description**: "Creates executive summaries and key point extractions"
- **Parameters Schema**:
```json
{
  "type": "object",
  "properties": {
    "document_content": {"type": "string"},
    "summary_type": {
      "type": "string",
      "enum": ["executive", "bullet_points", "key_findings", "brief_overview"]
    },
    "length_target": {
      "type": "string",
      "enum": ["brief", "medium", "comprehensive"]
    },
    "audience": {
      "type": "string",
      "enum": ["general", "technical", "executive", "legislative"]
    },
    "manifesto_content": {"type": "string"},
    "task_id": {"type": "string"}
  },
  "required": ["document_content", "summary_type", "task_id"]
}
```

#### **Node 2: Code Node (Summary Generation)**
```javascript
const { document_content, summary_type, length_target, audience, manifesto_content } = $input.item.json;

const lengthGuidelines = {
  brief: "150-300 words",
  medium: "300-600 words", 
  comprehensive: "600-1000 words"
};

const summaryPrompt = `
You are an expert summarization specialist skilled at extracting key insights and presenting them clearly to ${audience} audiences.

GUIDING PRINCIPLES:
${manifesto_content}

DOCUMENT TO SUMMARIZE:
${document_content}

SUMMARY REQUIREMENTS:
- Type: ${summary_type}
- Length: ${lengthGuidelines[length_target]}
- Audience: ${audience}

${summary_type === 'executive' ? `
Create an executive summary that includes:
1. Key Findings (3-5 main points)
2. Strategic Implications
3. Recommended Actions
4. Resource Requirements
5. Timeline Considerations
` : ''}

${summary_type === 'bullet_points' ? `
Create a bullet-point summary with:
- Main arguments (3-7 points)
- Supporting evidence for each point
- Action items or next steps
- Key metrics or data points
` : ''}

${summary_type === 'key_findings' ? `
Focus on:
- Primary discoveries or insights
- Data-driven conclusions
- Implications for policy/strategy
- Critical success factors
` : ''}

${summary_type === 'brief_overview' ? `
Provide a concise overview covering:
- Purpose and scope
- Main themes
- Conclusions
- Relevance to current priorities
` : ''}

Ensure the summary is:
- Clear and professional
- Actionable where appropriate
- Aligned with the guiding principles
- Appropriate for the target audience
`;

return [{
  json: {
    summary_prompt: summaryPrompt,
    summary_type: summary_type,
    length_target: length_target
  }
}];
```

#### **Node 3: HTTP Request (Generate Summary)**
- **Method**: POST
- **URL**: Gemini 2.5 Flash API endpoint (fast, cost-effective)
- **Body**: Summary generation prompt

## **Quality Control Agent Implementation**

### **Workflow Name**: `quality_control_agent`
### **MCP Tool Name**: `quality_control_agent`

#### **Node Structure**
```
MCP Server Trigger → Review All Outputs → Fact Check → Cross Reference → Format Check → Final Polish → Generate Filename → Respond to MCP
```

#### **Node 1: MCP Server Trigger**
- **Tool Name**: `quality_control_agent`
- **Description**: "Performs final quality control, fact-checking, and professional formatting"
- **Parameters Schema**:
```json
{
  "type": "object",
  "properties": {
    "agent_outputs": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "agent_name": {"type": "string"},
          "content": {"type": "string"},
          "document_title": {"type": "string"},
          "processing_type": {"type": "string"}
        }
      }
    },
    "original_prompt": {"type": "string"},
    "manifesto_content": {"type": "string"},
    "quality_requirements": {
      "type": "object",
      "properties": {
        "fact_checking": {"type": "boolean"},
        "web_research": {"type": "boolean"},
        "formatting_level": {"type": "string"}
      }
    },
    "batch_id": {"type": "string"}
  },
  "required": ["agent_outputs", "original_prompt", "batch_id"]
}
```

#### **Node 2: Code Node (Quality Review)**
```javascript
const { agent_outputs, original_prompt, manifesto_content, quality_requirements } = $input.item.json;

const qualityPrompt = `
You are a Senior Quality Control Agent responsible for final review and polishing of all agent outputs.

ORIGINAL REQUIREMENTS:
${original_prompt}

GUIDING PRINCIPLES:
${manifesto_content}

AGENT OUTPUTS TO REVIEW:
${agent_outputs.map(output => `
Agent: ${output.agent_name}
Document: ${output.document_title}
Type: ${output.processing_type}
Content:
${output.content}
---
`).join('\n')}

QUALITY REQUIREMENTS:
- Fact Checking: ${quality_requirements.fact_checking ? 'Required' : 'Not Required'}
- Web Research: ${quality_requirements.web_research ? 'Required' : 'Not Required'}
- Formatting Level: ${quality_requirements.formatting_level || 'Professional'}

Your responsibilities:
1. CONTENT REVIEW
   - Verify accuracy and consistency
   - Check alignment with original requirements
   - Ensure adherence to guiding principles
   - Identify any factual errors or inconsistencies

2. QUALITY ENHANCEMENT
   - Improve clarity and readability
   - Enhance professional presentation
   - Optimize structure and flow
   - Ensure appropriate tone and style

3. FINAL FORMATTING
   - Apply professional document formatting
   - Add proper headers, sections, and structure
   - Include table of contents if needed
   - Ensure consistent styling throughout

4. FILENAME GENERATION
   - Create descriptive, professional filenames
   - Use format: {Type}_{Topic}_{Date}_{Version}.docx
   - Ensure uniqueness and clarity

For each document, provide:
{
  "document_title": "final document title",
  "final_content": "polished and formatted content",
  "suggested_filename": "descriptive filename",
  "quality_notes": "any issues found and corrections made",
  "fact_check_status": "verified/needs_review/not_applicable",
  "formatting_level": "applied formatting level"
}
`;

return [{
  json: {
    quality_prompt: qualityPrompt,
    requires_fact_check: quality_requirements.fact_checking,
    requires_web_research: quality_requirements.web_research
  }
}];
```

#### **Node 3: HTTP Request (Quality Review)**
- **Method**: POST
- **URL**: OpenAI o1 API endpoint
- **Body**: Quality review prompt

#### **Node 4: IF Node (Fact Check Required)**
- **Condition**: `{{ $json.requires_fact_check === true }}`

#### **Node 5: Playwright Navigate (Additional Fact Checking)**
- **URL**: Dynamic based on claims to verify
- **Only runs if fact-checking is required**

#### **Node 6: Code Node (Final Output Preparation)**
```javascript
const qualityReview = JSON.parse($input.item.json.choices[0].message.content);
const batch_id = $input.item.json.batch_id;

// Prepare final outputs with metadata
const finalOutputs = qualityReview.map(doc => ({
  title: doc.document_title,
  content: doc.final_content,
  filename: doc.suggested_filename,
  quality_notes: doc.quality_notes,
  fact_check_status: doc.fact_check_status,
  formatting_level: doc.formatting_level,
  batch_id: batch_id,
  processing_timestamp: new Date().toISOString(),
  ready_for_conversion: true
}));

return [{
  json: {
    final_outputs: finalOutputs,
    quality_control_complete: true,
    batch_id: batch_id
  }
}];
```

## **Integration Workflow: Main Orchestrator**

### **Workflow Name**: `main_document_processor`

#### **Node Structure**
```
Google Drive Trigger → Batch Detection → Load Context → Lead Coordinator → Process Results → CloudConvert → Upload → Notify → Cleanup
```

#### **Node 1: Google Drive Trigger**
- **Watch Folder**: political_in folder ID
- **Trigger**: New File
- **Binary Data**: Enabled

#### **Node 2: Code Node (Batch Detection)**
```javascript
// Detect if this is part of a batch (multiple files added recently)
const currentFile = $input.item.json;
const batchWindow = 5 * 60 * 1000; // 5 minutes

// Check for other recent files in the same folder
// This would typically query Google Drive API for recent files
// For now, we'll process individually and let the Lead Coordinator handle batching

return [{
  json: {
    file_info: currentFile,
    batch_id: `batch_${Date.now()}`,
    processing_mode: 'single_file' // or 'batch' if multiple files detected
  }
}];
```

#### **Node 3: Google Drive List Files (Check for Batch)**
- **Folder ID**: political_in folder
- **Modified Time**: Within last 5 minutes
- **This helps identify if multiple files were added as a batch**

#### **Node 4: MCP Client (Call Lead Coordinator)**
- **Tool**: `lead_coordinator`
- **Parameters**: Document contents, PROMPT.md, manifesto.md, batch_id

#### **Node 5: Code Node (Process Coordinator Response)**
```javascript
const coordinatorResponse = $input.item.json;
const processingPlan = coordinatorResponse.processing_plan;

// Distribute tasks to appropriate agents
const agentTasks = processingPlan.processing_plan.map(task => ({
  agent: task.primary_agent,
  task_data: task,
  call_order: task.priority === 'high' ? 1 : (task.priority === 'medium' ? 2 : 3)
}));

return [{
  json: {
    agent_tasks: agentTasks,
    processing_plan: processingPlan,
    batch_id: coordinatorResponse.batch_id
  }
}];
```

#### **Node 6: Split In Batches (Agent Tasks)**
- **Batch Size**: 1 (process one task at a time)

#### **Node 7: Switch Node (Route to Appropriate Agent)**
- **Route by**: `{{ $json.agent }}`
- **Branches**: research_agent, policy_agent, summary_agent, etc.

#### **Node 8: MCP Client (Quality Control)**
- **Tool**: `quality_control_agent`
- **Parameters**: All agent outputs, original requirements, quality standards

#### **Node 9: CloudConvert (Markdown to DOCX)**
- **Input**: Final polished content from QC Agent
- **Output Format**: DOCX with professional formatting

#### **Node 10: Google Drive Upload**
- **Folder**: political_out folder ID
- **Filename**: From QC Agent's suggested filename

#### **Node 11: Email Notification**
- **To**: User email
- **Subject**: Batch Processing Complete
- **Body**: Summary of processed documents with links

## **Error Handling Strategy**

### **Global Error Workflow**
```
Error Trigger → Categorize Error → Notification → Retry Logic → Human Escalation
```

### **Agent-Level Error Handling**
- **Graceful Degradation**: Fallback to simpler models
- **Retry Logic**: Exponential backoff for API failures
- **Human Escalation**: Automatic notification for unresolvable errors
- **Partial Success**: Continue processing even if some agents fail

## **Testing Strategy**

### **Unit Testing**
- Test each agent workflow independently
- Verify prompt engineering effectiveness
- Validate output format consistency

### **Integration Testing**
- Test full end-to-end workflow
- Verify agent coordination
- Test error handling scenarios

### **Performance Testing**
- Measure processing times
- Monitor API costs
- Test with various document sizes and types

---

*This technical specification provides the detailed implementation guide for building each agent workflow in n8n. Each workflow is designed to be modular, testable, and scalable.* 