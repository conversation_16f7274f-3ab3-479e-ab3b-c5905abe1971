FROM node:18-alpine

# Create app directory
WORKDIR /app

# Create logs directory
RUN mkdir -p /app/logs

# Install security updates and required packages
RUN apk update && apk upgrade && apk add --no-cache \
    dumb-init \
    curl \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcpuser -u 1001 -G nodejs

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && \
    npm cache clean --force

# Copy application code
COPY . .

# Create scripts directory and data refresh scripts
RUN mkdir -p scripts && \
    echo '#!/usr/bin/env node\nconsole.log("International data refresh script placeholder");' > scripts/refresh-international-data.js && \
    echo '#!/usr/bin/env node\nconsole.log("Countries seeding script placeholder");' > scripts/seed-countries.js && \
    chmod +x scripts/refresh-international-data.js && \
    chmod +x scripts/seed-countries.js

# Set proper permissions
RUN chown -R mcpuser:nodejs /app && \
    chmod +x server.js

# Switch to non-root user
USER mcpuser

# Security: Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8092/health || exit 1

# Expose port
EXPOSE 8092

# Environment variables documentation
ENV MCP_SERVER_PORT=8092 \
    LOG_LEVEL=info \
    NODE_ENV=production \
    CACHE_TTL=7200 \
    MAX_RESULTS=500

# Labels for metadata
LABEL maintainer="Beau Lewis" \
      description="International Research MCP Server" \
      version="1.0.0" \
      org.opencontainers.image.title="International Research MCP Server" \
      org.opencontainers.image.description="Comprehensive international research tools for global policy analysis, diplomatic relations, and geopolitical research" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.authors="Beau Lewis" \
      org.opencontainers.image.source="https://github.com/beau-lewis/political-document-system" \
      org.opencontainers.image.licenses="MIT"

# Start the application
CMD ["node", "server.js"]