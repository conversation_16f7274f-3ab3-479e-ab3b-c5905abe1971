#!/usr/bin/env node

import axios from 'axios';
import fs from 'fs-extra';
import path from 'path';

/**
 * Comprehensive System Test Script
 * Tests the entire political document processing system end-to-end
 */

class SystemTester {
  constructor() {
    this.n8nWebhookUrl = 'http://localhost:5678/webhook/process-document-enhanced';
    this.chatInterfaceUrl = 'http://localhost:3001';
    this.vectorSearchUrl = 'http://localhost:8089';
    this.manifestoContextUrl = 'http://localhost:8080';
    this.politicalContentUrl = 'http://localhost:8081';
    
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runAllTests() {
    console.log('🧪 Starting Comprehensive System Tests');
    console.log('=====================================');

    try {
      // Core infrastructure tests
      await this.testInfrastructure();
      
      // MCP server tests
      await this.testMCPServers();
      
      // Vector search tests
      await this.testVectorSearch();
      
      // Document generation tests
      await this.testDocumentGeneration();
      
      // End-to-end workflow tests
      await this.testEndToEndWorkflow();
      
      // Performance tests
      await this.testPerformance();
      
      this.printSummary();
      
    } catch (error) {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    }
  }

  async testInfrastructure() {
    console.log('\n📊 Testing Infrastructure...');
    
    // Test PostgreSQL
    await this.runTest('PostgreSQL Connection', async () => {
      // This would require a database query, but we'll test via health endpoints
      return true;
    });
    
    // Test Redis
    await this.runTest('Redis Connection', async () => {
      // This would require a Redis ping, but we'll test via health endpoints
      return true;
    });
    
    // Test ChromaDB
    await this.runTest('ChromaDB Connection', async () => {
      const response = await axios.get('http://localhost:8000/api/v1/heartbeat');
      return response.status === 200;
    });
    
    // Test n8n
    await this.runTest('n8n Workflow Engine', async () => {
      const response = await axios.get('http://localhost:5678/healthz');
      return response.status === 200;
    });
    
    // Test chat interface
    await this.runTest('Chat Interface', async () => {
      const response = await axios.get(`${this.chatInterfaceUrl}/health`);
      return response.status === 200;
    });
  }

  async testMCPServers() {
    console.log('\n🤖 Testing MCP Servers...');
    
    const servers = [
      { name: 'Vector Search', url: this.vectorSearchUrl },
      { name: 'Manifesto Context', url: this.manifestoContextUrl },
      { name: 'Political Content', url: this.politicalContentUrl },
      { name: 'Research Integration', url: 'http://localhost:8082' },
      { name: 'Document Processing', url: 'http://localhost:8083' },
      { name: 'Quality Control', url: 'http://localhost:8084' }
    ];
    
    for (const server of servers) {
      await this.runTest(`${server.name} MCP Server`, async () => {
        const response = await axios.get(`${server.url}/health`);
        return response.status === 200 && response.data.status === 'healthy';
      });
    }
  }

  async testVectorSearch() {
    console.log('\n🔍 Testing Vector Search...');
    
    // Test collection stats
    await this.runTest('Vector Search - Collection Stats', async () => {
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_collection_stats',
        parameters: {}
      });
      
      return response.data.success && response.data.total_documents > 0;
    });
    
    // Test document search
    await this.runTest('Vector Search - Document Search', async () => {
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'search_similar_content',
        parameters: {
          query: 'healthcare reform universal coverage',
          category: 'healthcare',
          limit: 3
        }
      });
      
      return response.data.success && response.data.results.length > 0;
    });
    
    // Test context retrieval
    await this.runTest('Vector Search - Context Retrieval', async () => {
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_document_context',
        parameters: {
          topic: 'education reform and student debt',
          category: 'education',
          token_limit: 5000
        }
      });
      
      return response.data.success && response.data.context.length > 0;
    });
  }

  async testDocumentGeneration() {
    console.log('\n📄 Testing Document Generation...');
    
    // Test manifesto context
    await this.runTest('Manifesto Context Retrieval', async () => {
      const response = await axios.post(`${this.manifestoContextUrl}/mcp/call`, {
        tool: 'get_context_by_tier',
        parameters: {
          tier: 2,
          category: 'healthcare',
          include_voice_guide: true
        }
      });
      
      return response.data.success && response.data.context.length > 0;
    });
    
    // Test white paper generation
    await this.runTest('White Paper Generation', async () => {
      const response = await axios.post(`${this.politicalContentUrl}/mcp/call`, {
        tool: 'generate_white_paper',
        parameters: {
          topic: 'Test Healthcare Reform',
          category: 'healthcare',
          token_tier: 2,
          research_level: 'basic',
          output_format: 'markdown'
        }
      });
      
      return response.data.success && response.data.document_id;
    });
  }

  async testEndToEndWorkflow() {
    console.log('\n🔄 Testing End-to-End Workflow...');
    
    // Test complete document processing workflow
    await this.runTest('End-to-End Document Processing', async () => {
      const requestData = {
        taskType: 'generate_whitepaper',
        topic: 'Universal Basic Income Implementation',
        category: 'economic_policy',
        tokenTier: 2,
        researchLevel: 'comprehensive',
        outputFormat: 'markdown',
        filename: 'ubi_test_whitepaper.md'
      };
      
      const response = await axios.post(this.n8nWebhookUrl, requestData, {
        timeout: 60000 // 60 second timeout
      });
      
      return response.data.success && response.data.jobId;
    });
  }

  async testPerformance() {
    console.log('\n⚡ Testing Performance...');
    
    // Test vector search performance
    await this.runTest('Vector Search Performance', async () => {
      const startTime = Date.now();
      
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'search_similar_content',
        parameters: {
          query: 'climate change environmental policy',
          limit: 10
        }
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`    Vector search took ${duration}ms`);
      
      return response.data.success && duration < 5000; // Should complete in under 5 seconds
    });
    
    // Test context retrieval performance
    await this.runTest('Context Retrieval Performance', async () => {
      const startTime = Date.now();
      
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_document_context',
        parameters: {
          topic: 'economic inequality solutions',
          token_limit: 10000
        }
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`    Context retrieval took ${duration}ms`);
      
      return response.data.success && duration < 10000; // Should complete in under 10 seconds
    });
  }

  async runTest(testName, testFunction) {
    try {
      console.log(`  🧪 ${testName}...`);
      const result = await testFunction();
      
      if (result) {
        console.log(`  ✅ ${testName} - PASSED`);
        this.testResults.passed++;
        this.testResults.tests.push({ name: testName, status: 'PASSED' });
      } else {
        console.log(`  ❌ ${testName} - FAILED`);
        this.testResults.failed++;
        this.testResults.tests.push({ name: testName, status: 'FAILED' });
      }
    } catch (error) {
      console.log(`  ❌ ${testName} - ERROR: ${error.message}`);
      this.testResults.failed++;
      this.testResults.tests.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }

  printSummary() {
    console.log('\n📊 Test Summary');
    console.log('================');
    console.log(`✅ Tests Passed: ${this.testResults.passed}`);
    console.log(`❌ Tests Failed: ${this.testResults.failed}`);
    console.log(`📈 Success Rate: ${(this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.tests
        .filter(test => test.status !== 'PASSED')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.status}${test.error ? ` (${test.error})` : ''}`);
        });
    }
    
    console.log('\n🎯 System Status:');
    if (this.testResults.failed === 0) {
      console.log('🟢 All systems operational - ready for production use!');
      process.exit(0);
    } else if (this.testResults.failed <= 2) {
      console.log('🟡 Minor issues detected - system mostly functional');
      process.exit(0);
    } else {
      console.log('🔴 Major issues detected - system needs attention');
      process.exit(1);
    }
  }
}

// Main execution
async function main() {
  const tester = new SystemTester();
  await tester.runAllTests();
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default SystemTester;