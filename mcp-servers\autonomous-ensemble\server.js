const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
const { PythonShell } = require('python-shell');
const redis = require('redis');
const Database = require('sqlite3').Database;
const OpenAI = require('openai');
const Anthropic = require('anthropic');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const CircuitBreaker = require('circuit-breaker-js');
const NodeCache = require('node-cache');
const uuid = require('uuid').v4;
const client = require('prom-client');
const promMiddleware = require('express-prometheus-middleware');
const { CronJob } = require('cron');
const _ = require('lodash');
const moment = require('moment');

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Initialize AI clients
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
const googleAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);

// Initialize Redis and SQLite
const redisClient = redis.createClient({
  host: process.env.REDIS_HOST || 'redis',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD
});

const db = new Database('/app/data/autonomous_ensemble.db');
const memoryCache = new NodeCache({ stdTTL: 600, checkperiod: 120 });

// Prometheus metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });

const taskCounter = new client.Counter({
  name: 'autonomous_tasks_total',
  help: 'Total number of autonomous tasks processed',
  labelNames: ['type', 'status', 'agent']
});

const taskDuration = new client.Histogram({
  name: 'autonomous_task_duration_seconds',
  help: 'Duration of autonomous task processing',
  labelNames: ['type', 'agent'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60, 300]
});

const modelRoutingCounter = new client.Counter({
  name: 'model_routing_decisions_total',
  help: 'Total model routing decisions',
  labelNames: ['strategy', 'selected_model', 'fallback']
});

const agentPerformanceGauge = new client.Gauge({
  name: 'agent_performance_score',
  help: 'Performance scores of autonomous agents',
  labelNames: ['agent_type']
});

register.registerMetric(taskCounter);
register.registerMetric(taskDuration);
register.registerMetric(modelRoutingCounter);
register.registerMetric(agentPerformanceGauge);

// Circuit breakers for AI models
const circuitBreakers = {
  openai: new CircuitBreaker({ timeout: 30000, errorThreshold: 50, resetTimeout: 60000 }),
  anthropic: new CircuitBreaker({ timeout: 30000, errorThreshold: 50, resetTimeout: 60000 }),
  google: new CircuitBreaker({ timeout: 30000, errorThreshold: 50, resetTimeout: 60000 })
};

// Model performance tracking
const modelPerformance = {
  'gpt-4': { successRate: 0.95, avgLatency: 2.3, cost: 0.03, lastUpdated: Date.now() },
  'claude-3-sonnet': { successRate: 0.94, avgLatency: 1.8, cost: 0.015, lastUpdated: Date.now() },
  'gemini-pro': { successRate: 0.92, avgLatency: 1.5, cost: 0.0025, lastUpdated: Date.now() }
};

// Autonomous agents configuration
const agents = {
  researcher: {
    name: 'Political Research Agent',
    models: ['gpt-4', 'claude-3-sonnet'],
    capabilities: ['web_research', 'fact_checking', 'source_analysis'],
    systemPrompt: "You are a political research specialist focused on accurate fact-finding and source verification."
  },
  analyzer: {
    name: 'Content Analysis Agent',
    models: ['claude-3-sonnet', 'gpt-4'],
    capabilities: ['sentiment_analysis', 'bias_detection', 'content_classification'],
    systemPrompt: "You are a political content analyst specializing in bias detection and sentiment analysis."
  },
  synthesizer: {
    name: 'Information Synthesis Agent',
    models: ['gpt-4', 'gemini-pro'],
    capabilities: ['summarization', 'synthesis', 'report_generation'],
    systemPrompt: "You are an expert at synthesizing complex political information into clear, comprehensive reports."
  },
  factChecker: {
    name: 'Fact Verification Agent',
    models: ['claude-3-sonnet', 'gpt-4'],
    capabilities: ['claim_verification', 'source_validation', 'credibility_assessment'],
    systemPrompt: "You are a fact-checking specialist focused on verifying political claims and assessing source credibility."
  },
  qualityController: {
    name: 'Quality Control Agent',
    models: ['gpt-4'],
    capabilities: ['quality_assessment', 'accuracy_verification', 'completeness_check'],
    systemPrompt: "You are a quality control specialist ensuring accuracy and completeness of political analysis."
  }
};

// Express app setup
const app = express();
const port = process.env.MCP_SERVER_PORT || 8093;

app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5678'],
  credentials: true
}));

app.use(rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 500,
  message: { error: 'Too many requests to autonomous ensemble' }
}));

app.use(express.json({ limit: '10mb' }));
app.use(winston.expressLogger);

app.use(promMiddleware({
  metricsPath: '/metrics',
  collectDefaultMetrics: true
}));

// Core orchestration functions
class AutonomousOrchestrator {
  constructor() {
    this.activeTasks = new Map();
    this.taskQueue = [];
    this.routingStrategies = ['capability_based', 'cost_optimized', 'performance_based', 'load_balanced'];
    this.currentStrategy = 'capability_based';
  }

  async submitTask(taskDefinition) {
    const taskId = uuid();
    const task = {
      id: taskId,
      type: taskDefinition.type,
      content: taskDefinition.content,
      requirements: taskDefinition.requirements || {},
      priority: taskDefinition.priority || 'medium',
      status: 'queued',
      createdAt: new Date(),
      dependencies: taskDefinition.dependencies || [],
      metadata: taskDefinition.metadata || {}
    };

    this.activeTasks.set(taskId, task);
    this.taskQueue.push(task);

    logger.info(`Task ${taskId} submitted with type: ${task.type}`);
    
    // Start processing asynchronously
    this.processTask(task).catch(error => {
      logger.error(`Task ${taskId} failed:`, error);
      task.status = 'failed';
      task.error = error.message;
    });

    return { taskId, status: 'submitted', estimatedCompletion: this.estimateCompletion(task) };
  }

  async processTask(task) {
    const timer = taskDuration.startTimer({ type: task.type, agent: 'orchestrator' });
    
    try {
      task.status = 'processing';
      task.startedAt = new Date();

      // Select appropriate agent based on task type
      const selectedAgent = this.selectAgent(task);
      task.assignedAgent = selectedAgent;

      // Route to appropriate model
      const selectedModel = await this.routeToModel(task, selectedAgent);
      task.selectedModel = selectedModel;

      // Execute task with selected agent and model
      const result = await this.executeTask(task, selectedAgent, selectedModel);
      
      task.result = result;
      task.status = 'completed';
      task.completedAt = new Date();

      taskCounter.inc({ type: task.type, status: 'success', agent: selectedAgent });
      timer();

      logger.info(`Task ${task.id} completed successfully`);
      return result;

    } catch (error) {
      task.status = 'failed';
      task.error = error.message;
      task.failedAt = new Date();

      taskCounter.inc({ type: task.type, status: 'error', agent: task.assignedAgent || 'unknown' });
      timer();

      logger.error(`Task ${task.id} failed:`, error);
      throw error;
    }
  }

  selectAgent(task) {
    const taskType = task.type;
    const capabilities = task.requirements.capabilities || [];

    // Rule-based agent selection
    if (taskType.includes('research') || capabilities.includes('research')) {
      return 'researcher';
    } else if (taskType.includes('analysis') || capabilities.includes('analysis')) {
      return 'analyzer';
    } else if (taskType.includes('synthesis') || capabilities.includes('synthesis')) {
      return 'synthesizer';
    } else if (taskType.includes('fact_check') || capabilities.includes('fact_checking')) {
      return 'factChecker';
    } else if (taskType.includes('quality') || capabilities.includes('quality_control')) {
      return 'qualityController';
    }

    // Default to analyzer for general tasks
    return 'analyzer';
  }

  async routeToModel(task, agentType) {
    const agent = agents[agentType];
    const availableModels = agent.models;

    let selectedModel;

    switch (this.currentStrategy) {
      case 'capability_based':
        selectedModel = this.selectByCapability(availableModels, task);
        break;
      case 'cost_optimized':
        selectedModel = this.selectByCost(availableModels);
        break;
      case 'performance_based':
        selectedModel = this.selectByPerformance(availableModels);
        break;
      case 'load_balanced':
        selectedModel = this.selectByLoad(availableModels);
        break;
      default:
        selectedModel = availableModels[0];
    }

    modelRoutingCounter.inc({ 
      strategy: this.currentStrategy, 
      selected_model: selectedModel, 
      fallback: 'false' 
    });

    return selectedModel;
  }

  selectByCapability(models, task) {
    // For political analysis, prefer Claude for nuanced understanding
    if (task.type.includes('political') || task.type.includes('bias')) {
      return models.includes('claude-3-sonnet') ? 'claude-3-sonnet' : models[0];
    }
    // For research and factual tasks, prefer GPT-4
    if (task.type.includes('research') || task.type.includes('fact')) {
      return models.includes('gpt-4') ? 'gpt-4' : models[0];
    }
    // For synthesis and summarization, prefer GPT-4
    if (task.type.includes('synthesis') || task.type.includes('summary')) {
      return models.includes('gpt-4') ? 'gpt-4' : models[0];
    }
    return models[0];
  }

  selectByCost(models) {
    const costs = models.map(model => ({ model, cost: modelPerformance[model]?.cost || 1 }));
    return costs.sort((a, b) => a.cost - b.cost)[0].model;
  }

  selectByPerformance(models) {
    const performances = models.map(model => ({
      model,
      score: (modelPerformance[model]?.successRate || 0.5) / (modelPerformance[model]?.avgLatency || 10)
    }));
    return performances.sort((a, b) => b.score - a.score)[0].model;
  }

  selectByLoad(models) {
    // Simple round-robin for load balancing
    const modelIndex = this.taskQueue.length % models.length;
    return models[modelIndex];
  }

  async executeTask(task, agentType, modelName) {
    const agent = agents[agentType];
    const systemPrompt = agent.systemPrompt;

    const userPrompt = this.constructUserPrompt(task);

    try {
      let response;

      if (modelName.startsWith('gpt')) {
        response = await circuitBreakers.openai.call(async () => {
          const completion = await openai.chat.completions.create({
            model: modelName,
            messages: [
              { role: 'system', content: systemPrompt },
              { role: 'user', content: userPrompt }
            ],
            max_tokens: 2000,
            temperature: 0.3
          });
          return completion.choices[0].message.content;
        });
      } else if (modelName.startsWith('claude')) {
        response = await circuitBreakers.anthropic.call(async () => {
          const completion = await anthropic.messages.create({
            model: 'claude-3-sonnet-20240229',
            max_tokens: 2000,
            messages: [
              { role: 'user', content: `${systemPrompt}\n\n${userPrompt}` }
            ]
          });
          return completion.content[0].text;
        });
      } else if (modelName.startsWith('gemini')) {
        response = await circuitBreakers.google.call(async () => {
          const model = googleAI.getGenerativeModel({ model: 'gemini-pro' });
          const result = await model.generateContent(`${systemPrompt}\n\n${userPrompt}`);
          return result.response.text();
        });
      }

      // Store result in cache for potential reuse
      const cacheKey = `result_${task.type}_${Buffer.from(userPrompt).toString('base64').slice(0, 20)}`;
      memoryCache.set(cacheKey, response, 3600);

      return {
        response,
        agent: agentType,
        model: modelName,
        timestamp: new Date(),
        confidence: this.calculateConfidence(response, task),
        metadata: {
          processingTime: Date.now() - task.startedAt?.getTime(),
          tokensUsed: this.estimateTokens(userPrompt + response)
        }
      };

    } catch (error) {
      // Attempt fallback to next available model
      const availableModels = agent.models.filter(m => m !== modelName);
      if (availableModels.length > 0) {
        logger.warn(`Model ${modelName} failed, trying fallback: ${availableModels[0]}`);
        modelRoutingCounter.inc({ 
          strategy: this.currentStrategy, 
          selected_model: availableModels[0], 
          fallback: 'true' 
        });
        return await this.executeTask(task, agentType, availableModels[0]);
      }
      throw error;
    }
  }

  constructUserPrompt(task) {
    return `
Task Type: ${task.type}
Content: ${task.content}
Requirements: ${JSON.stringify(task.requirements)}
Priority: ${task.priority}

Please analyze this political content according to the task type and provide a comprehensive response.
If this is a research task, include sources and methodology.
If this is analysis, include detailed findings and confidence levels.
If this is synthesis, provide a clear, structured summary.
If this is fact-checking, include verification status and sources.
    `.trim();
  }

  calculateConfidence(response, task) {
    // Simple confidence calculation based on response length and task complexity
    const baseConfidence = 0.7;
    const lengthBonus = Math.min(response.length / 1000, 0.2);
    const complexityAdjustment = task.priority === 'high' ? 0.1 : 0;
    return Math.min(baseConfidence + lengthBonus + complexityAdjustment, 1.0);
  }

  estimateTokens(text) {
    // Rough estimation: ~4 characters per token
    return Math.ceil(text.length / 4);
  }

  estimateCompletion(task) {
    const baseTime = 30; // seconds
    const complexityMultiplier = task.priority === 'high' ? 2 : task.priority === 'low' ? 0.5 : 1;
    const estimatedSeconds = baseTime * complexityMultiplier;
    return moment().add(estimatedSeconds, 'seconds').toISOString();
  }

  getTaskStatus(taskId) {
    const task = this.activeTasks.get(taskId);
    if (!task) {
      return { error: 'Task not found' };
    }

    return {
      id: task.id,
      type: task.type,
      status: task.status,
      assignedAgent: task.assignedAgent,
      selectedModel: task.selectedModel,
      progress: this.calculateProgress(task),
      createdAt: task.createdAt,
      startedAt: task.startedAt,
      completedAt: task.completedAt,
      result: task.result,
      error: task.error
    };
  }

  calculateProgress(task) {
    if (task.status === 'completed') return 100;
    if (task.status === 'failed') return 0;
    if (task.status === 'processing') {
      const elapsed = Date.now() - (task.startedAt?.getTime() || Date.now());
      const estimated = 30000; // 30 seconds default
      return Math.min(Math.floor((elapsed / estimated) * 100), 95);
    }
    return 0;
  }

  getSystemAnalytics() {
    const totalTasks = this.activeTasks.size;
    const completedTasks = Array.from(this.activeTasks.values()).filter(t => t.status === 'completed').length;
    const failedTasks = Array.from(this.activeTasks.values()).filter(t => t.status === 'failed').length;
    const activeTasks = Array.from(this.activeTasks.values()).filter(t => t.status === 'processing').length;

    // Update agent performance metrics
    Object.keys(agents).forEach(agentType => {
      const agentTasks = Array.from(this.activeTasks.values()).filter(t => t.assignedAgent === agentType);
      const successRate = agentTasks.length > 0 ? agentTasks.filter(t => t.status === 'completed').length / agentTasks.length : 0;
      agentPerformanceGauge.set({ agent_type: agentType }, successRate);
    });

    return {
      totalTasks,
      completedTasks,
      failedTasks,
      activeTasks,
      queueSize: this.taskQueue.length,
      successRate: totalTasks > 0 ? completedTasks / totalTasks : 0,
      currentRoutingStrategy: this.currentStrategy,
      modelPerformance,
      systemHealth: this.calculateSystemHealth(),
      uptime: process.uptime()
    };
  }

  calculateSystemHealth() {
    const analytics = this.getSystemAnalytics();
    let healthScore = 100;

    // Reduce score based on failure rate
    if (analytics.totalTasks > 0) {
      const failureRate = analytics.failedTasks / analytics.totalTasks;
      healthScore -= failureRate * 50;
    }

    // Reduce score if queue is backing up
    if (analytics.queueSize > 10) {
      healthScore -= Math.min((analytics.queueSize - 10) * 2, 20);
    }

    // Reduce score based on circuit breaker states
    Object.entries(circuitBreakers).forEach(([name, breaker]) => {
      if (breaker.state === 'OPEN') {
        healthScore -= 15;
      } else if (breaker.state === 'HALF_OPEN') {
        healthScore -= 5;
      }
    });

    return Math.max(Math.floor(healthScore), 0);
  }
}

// Initialize orchestrator
const orchestrator = new AutonomousOrchestrator();

// MCP Tools
const tools = [
  {
    name: "submit_autonomous_task",
    description: "Submit a task for autonomous processing with intelligent agent assignment and model routing",
    inputSchema: {
      type: "object",
      properties: {
        type: {
          type: "string",
          description: "Type of task (e.g., 'political_analysis', 'fact_check', 'research', 'synthesis')"
        },
        content: {
          type: "string",
          description: "Content to be processed"
        },
        requirements: {
          type: "object",
          description: "Task requirements and constraints"
        },
        priority: {
          type: "string",
          enum: ["low", "medium", "high"],
          description: "Task priority level"
        },
        metadata: {
          type: "object",
          description: "Additional metadata for the task"
        }
      },
      required: ["type", "content"]
    }
  },
  {
    name: "get_task_status",
    description: "Get the current status and progress of a submitted task",
    inputSchema: {
      type: "object",
      properties: {
        taskId: {
          type: "string",
          description: "ID of the task to check"
        }
      },
      required: ["taskId"]
    }
  },
  {
    name: "get_system_analytics",
    description: "Get comprehensive analytics about the autonomous ensemble system",
    inputSchema: {
      type: "object",
      properties: {
        includeDetailedMetrics: {
          type: "boolean",
          description: "Include detailed performance metrics",
          default: false
        }
      }
    }
  },
  {
    name: "configure_routing_strategy",
    description: "Configure the model routing strategy for optimal performance",
    inputSchema: {
      type: "object",
      properties: {
        strategy: {
          type: "string",
          enum: ["capability_based", "cost_optimized", "performance_based", "load_balanced"],
          description: "Routing strategy to use"
        }
      },
      required: ["strategy"]
    }
  },
  {
    name: "get_agent_capabilities",
    description: "Get information about available agents and their capabilities",
    inputSchema: {
      type: "object",
      properties: {
        agentType: {
          type: "string",
          description: "Specific agent to get info about (optional)"
        }
      }
    }
  }
];

// Routes
app.get('/health', (req, res) => {
  const systemHealth = orchestrator.calculateSystemHealth();
  res.json({ 
    status: systemHealth > 70 ? 'healthy' : systemHealth > 40 ? 'degraded' : 'unhealthy',
    healthScore: systemHealth,
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    service: 'autonomous-ensemble-mcp'
  });
});

app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
});

// MCP tool endpoint
app.post('/mcp/call', async (req, res) => {
  try {
    const { tool, arguments: args } = req.body;
    
    if (!tools.find(t => t.name === tool)) {
      return res.status(400).json({ error: 'Unknown tool' });
    }
    
    let result;
    
    switch (tool) {
      case 'submit_autonomous_task':
        result = await orchestrator.submitTask(args);
        break;
      case 'get_task_status':
        result = orchestrator.getTaskStatus(args.taskId);
        break;
      case 'get_system_analytics':
        result = orchestrator.getSystemAnalytics();
        break;
      case 'configure_routing_strategy':
        orchestrator.currentStrategy = args.strategy;
        result = { strategy: args.strategy, message: 'Routing strategy updated' };
        break;
      case 'get_agent_capabilities':
        result = args.agentType ? agents[args.agentType] : agents;
        break;
      default:
        return res.status(400).json({ error: 'Tool not implemented' });
    }
    
    res.json({ result });
    
  } catch (error) {
    logger.error('MCP tool error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Periodic health checks and optimization
const healthCheckJob = new CronJob('*/5 * * * *', () => {
  const analytics = orchestrator.getSystemAnalytics();
  logger.info('System health check:', analytics);
  
  // Auto-adjust routing strategy based on performance
  if (analytics.successRate < 0.8 && orchestrator.currentStrategy !== 'performance_based') {
    orchestrator.currentStrategy = 'performance_based';
    logger.info('Auto-switched to performance_based routing due to low success rate');
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Initialize database schema
function initializeDatabase() {
  db.serialize(() => {
    db.run(`
      CREATE TABLE IF NOT EXISTS task_history (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        agent TEXT,
        model TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        duration_ms INTEGER,
        success BOOLEAN
      )
    `);
    
    db.run(`
      CREATE TABLE IF NOT EXISTS model_performance (
        model TEXT PRIMARY KEY,
        success_rate REAL,
        avg_latency REAL,
        cost REAL,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
  });
}

// Start server
async function startServer() {
  try {
    await redisClient.connect();
    initializeDatabase();
    healthCheckJob.start();
    
    app.listen(port, '0.0.0.0', () => {
      logger.info(`Autonomous Ensemble MCP Server running on port ${port}`);
      logger.info('Features: Multi-agent orchestration, Intelligent routing, Performance optimization');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();