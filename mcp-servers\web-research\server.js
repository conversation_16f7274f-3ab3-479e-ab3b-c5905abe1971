#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import { ChromaClient } from 'chromadb';
import { OpenAI } from 'openai';
import axios from 'axios';
import * as cheerio from 'cheerio';
import puppeteer from 'puppeteer';
import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { encoding_for_model } from 'tiktoken';
import winston from 'winston';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { Client } from 'pg';
import { createClient } from 'redis';
import { parse as parseHtml } from 'node-html-parser';
import urlParse from 'url-parse';
import robotsParser from 'robots-parser';

/**
 * Web Research MCP Server
 * Comprehensive web research functionality with scraping, search, and RAG integration
 */

class WebResearchServer {
  constructor() {
    this.server = new MCPServer({
      name: 'web-research',
      version: '1.0.0'
    });

    // Initialize clients
    this.chromaClient = new ChromaClient({
      url: process.env.CHROMADB_URL || 'http://chromadb:8000'
    });
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    this.tokenizer = encoding_for_model('gpt-4');
    this.collection = null;
    this.pgClient = null;
    this.redisClient = null;
    this.browser = null;

    // Configuration
    this.embeddingModel = 'text-embedding-3-small';
    this.maxTokens = 50000;
    this.requestDelay = 1000; // Rate limiting
    this.userAgent = 'Mozilla/5.0 (compatible; WebResearchBot/1.0; +http://example.com/bot)';

    // Setup logging
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: '/app/logs/web-research.log' })
      ]
    });

    this.setupTools();
    this.setupHealthEndpoint();
  }

  async initialize() {
    try {
      // Initialize database connections
      this.pgClient = new Client({
        host: process.env.POSTGRES_HOST || 'postgresql',
        port: process.env.POSTGRES_PORT || 5432,
        database: process.env.POSTGRES_DB || 'political_conversations',
        user: process.env.POSTGRES_USER || 'n8n_user',
        password: process.env.POSTGRES_PASSWORD || 'secure_password'
      });

      this.redisClient = createClient({
        host: process.env.REDIS_HOST || 'redis',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD
      });

      await this.pgClient.connect();
      await this.redisClient.connect();

      // Initialize ChromaDB collection
      await this.initializeChromaCollection();

      // Initialize Puppeteer browser
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      this.logger.info('Web Research MCP Server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Web Research MCP Server:', error);
      throw error;
    }
  }

  async initializeChromaCollection() {
    try {
      // Create or get the web research collection
      this.collection = await this.chromaClient.getOrCreateCollection({
        name: 'web_research_documents',
        metadata: {
          'description': 'Web scraped content and research documents for RAG retrieval',
          'hnsw:space': 'cosine'
        }
      });

      this.logger.info('ChromaDB collection initialized: web_research_documents');
    } catch (error) {
      this.logger.error('Failed to initialize ChromaDB collection:', error);
      throw error;
    }
  }

  setupTools() {
    // Tool 1: Web Search
    this.server.addTool({
      name: 'web_search',
      description: 'Search the web for information using multiple search engines',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query'
          },
          search_engine: {
            type: 'string',
            default: 'duckduckgo',
            enum: ['duckduckgo', 'google', 'bing'],
            description: 'Search engine to use'
          },
          max_results: {
            type: 'number',
            default: 10,
            minimum: 1,
            maximum: 50,
            description: 'Maximum number of search results to return'
          },
          language: {
            type: 'string',
            default: 'en',
            description: 'Language code for search results'
          },
          region: {
            type: 'string',
            default: 'us',
            description: 'Region code for search results'
          },
          safe_search: {
            type: 'boolean',
            default: true,
            description: 'Enable safe search filtering'
          }
        },
        required: ['query']
      }
    }, this.webSearch.bind(this));

    // Tool 2: Website Scraper
    this.server.addTool({
      name: 'website_scraper',
      description: 'Scrape and extract content from websites with advanced parsing',
      inputSchema: {
        type: 'object',
        properties: {
          url: {
            type: 'string',
            description: 'URL to scrape'
          },
          scraping_method: {
            type: 'string',
            default: 'cheerio',
            enum: ['cheerio', 'puppeteer', 'readability'],
            description: 'Method to use for scraping'
          },
          extract_links: {
            type: 'boolean',
            default: false,
            description: 'Extract all links from the page'
          },
          extract_images: {
            type: 'boolean',
            default: false,
            description: 'Extract image URLs from the page'
          },
          wait_for_selector: {
            type: 'string',
            description: 'CSS selector to wait for (Puppeteer only)'
          },
          custom_headers: {
            type: 'object',
            description: 'Custom HTTP headers to send with the request'
          },
          respect_robots: {
            type: 'boolean',
            default: true,
            description: 'Respect robots.txt directives'
          }
        },
        required: ['url']
      }
    }, this.websiteScraper.bind(this));

    // Tool 3: Content Summarizer
    this.server.addTool({
      name: 'content_summarizer',
      description: 'Summarize scraped web content using AI',
      inputSchema: {
        type: 'object',
        properties: {
          content: {
            type: 'string',
            description: 'Content to summarize'
          },
          summary_type: {
            type: 'string',
            default: 'extractive',
            enum: ['extractive', 'abstractive', 'bullet_points', 'key_facts'],
            description: 'Type of summary to generate'
          },
          max_length: {
            type: 'number',
            default: 500,
            minimum: 100,
            maximum: 2000,
            description: 'Maximum length of summary in words'
          },
          focus_area: {
            type: 'string',
            description: 'Specific area or topic to focus on in the summary'
          },
          include_metadata: {
            type: 'boolean',
            default: true,
            description: 'Include metadata like word count, key topics, etc.'
          }
        },
        required: ['content']
      }
    }, this.contentSummarizer.bind(this));

    // Tool 4: Research Aggregator
    this.server.addTool({
      name: 'research_aggregator',
      description: 'Aggregate research from multiple sources and generate comprehensive reports',
      inputSchema: {
        type: 'object',
        properties: {
          topic: {
            type: 'string',
            description: 'Research topic or question'
          },
          sources: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                url: { type: 'string' },
                title: { type: 'string' },
                content: { type: 'string' },
                credibility_score: { type: 'number' }
              },
              required: ['url', 'content']
            },
            description: 'Array of source materials to aggregate'
          },
          report_type: {
            type: 'string',
            default: 'comprehensive',
            enum: ['comprehensive', 'comparative', 'factual', 'analytical'],
            description: 'Type of research report to generate'
          },
          include_citations: {
            type: 'boolean',
            default: true,
            description: 'Include citations and source references'
          },
          fact_check: {
            type: 'boolean',
            default: false,
            description: 'Perform fact-checking on the aggregated content'
          }
        },
        required: ['topic', 'sources']
      }
    }, this.researchAggregator.bind(this));

    // Tool 5: Retrieve Relevant Documents (RAG)
    this.server.addTool({
      name: 'retrieve_relevant_documents',
      description: 'RAG vector search integration for document retrieval from web research cache',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query for document retrieval'
          },
          document_type: {
            type: 'string',
            enum: ['web_content', 'research_report', 'scraped_article', 'summary'],
            description: 'Type of documents to search for'
          },
          limit: {
            type: 'number',
            default: 5,
            minimum: 1,
            maximum: 20,
            description: 'Maximum number of documents to retrieve'
          },
          similarity_threshold: {
            type: 'number',
            default: 0.7,
            minimum: 0.0,
            maximum: 1.0,
            description: 'Minimum similarity score for results'
          },
          date_range: {
            type: 'object',
            properties: {
              start_date: { type: 'string', format: 'date' },
              end_date: { type: 'string', format: 'date' }
            },
            description: 'Filter documents by date range'
          },
          domain_filter: {
            type: 'array',
            items: { type: 'string' },
            description: 'Filter results by domain names'
          }
        },
        required: ['query']
      }
    }, this.retrieveRelevantDocuments.bind(this));

    // Additional management tools
    this.server.addTool({
      name: 'index_web_content',
      description: 'Index scraped web content for future RAG retrieval',
      inputSchema: {
        type: 'object',
        properties: {
          url: { type: 'string', description: 'Source URL' },
          title: { type: 'string', description: 'Content title' },
          content: { type: 'string', description: 'Full text content' },
          metadata: {
            type: 'object',
            properties: {
              author: { type: 'string' },
              publish_date: { type: 'string' },
              domain: { type: 'string' },
              content_type: { type: 'string' },
              language: { type: 'string' },
              credibility_score: { type: 'number' }
            }
          }
        },
        required: ['url', 'content']
      }
    }, this.indexWebContent.bind(this));

    this.server.addTool({
      name: 'bulk_web_research',
      description: 'Perform bulk web research on multiple queries or URLs',
      inputSchema: {
        type: 'object',
        properties: {
          queries: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of search queries'
          },
          urls: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of URLs to scrape'
          },
          auto_summarize: {
            type: 'boolean',
            default: true,
            description: 'Automatically summarize scraped content'
          },
          auto_index: {
            type: 'boolean',
            default: true,
            description: 'Automatically index content for RAG'
          }
        }
      }
    }, this.bulkWebResearch.bind(this));
  }

  async webSearch(params) {
    try {
      const { query, search_engine = 'duckduckgo', max_results = 10, language = 'en', region = 'us', safe_search = true } = params;

      this.logger.info(`Performing web search: ${query} using ${search_engine}`);

      let results = [];

      switch (search_engine) {
        case 'duckduckgo':
          results = await this.searchDuckDuckGo(query, max_results, safe_search);
          break;
        case 'google':
          results = await this.searchGoogle(query, max_results, language, region, safe_search);
          break;
        case 'bing':
          results = await this.searchBing(query, max_results, language, region, safe_search);
          break;
        default:
          throw new Error(`Unsupported search engine: ${search_engine}`);
      }

      // Cache results
      const cacheKey = `search:${Buffer.from(query).toString('base64')}:${search_engine}`;
      await this.redisClient.setex(cacheKey, 3600, JSON.stringify(results));

      this.logger.info(`Found ${results.length} search results for query: ${query}`);

      return {
        success: true,
        query,
        search_engine,
        total_results: results.length,
        results: results.slice(0, max_results)
      };
    } catch (error) {
      this.logger.error('Error in web search:', error);
      throw error;
    }
  }

  async websiteScraper(params) {
    try {
      const { 
        url, 
        scraping_method = 'cheerio', 
        extract_links = false, 
        extract_images = false, 
        wait_for_selector, 
        custom_headers = {}, 
        respect_robots = true 
      } = params;

      this.logger.info(`Scraping website: ${url} using ${scraping_method}`);

      // Check robots.txt if required
      if (respect_robots && !(await this.checkRobotsTxt(url))) {
        throw new Error('Scraping not allowed by robots.txt');
      }

      // Check cache first
      const cacheKey = `scrape:${Buffer.from(url).toString('base64')}`;
      const cached = await this.redisClient.get(cacheKey);
      if (cached) {
        this.logger.info(`Returning cached content for ${url}`);
        return JSON.parse(cached);
      }

      let result;

      switch (scraping_method) {
        case 'cheerio':
          result = await this.scrapeWithCheerio(url, extract_links, extract_images, custom_headers);
          break;
        case 'puppeteer':
          result = await this.scrapeWithPuppeteer(url, extract_links, extract_images, wait_for_selector);
          break;
        case 'readability':
          result = await this.scrapeWithReadability(url, custom_headers);
          break;
        default:
          throw new Error(`Unsupported scraping method: ${scraping_method}`);
      }

      // Cache result
      await this.redisClient.setex(cacheKey, 1800, JSON.stringify(result)); // 30 min cache

      this.logger.info(`Successfully scraped ${url}: ${result.content.length} characters`);

      return result;
    } catch (error) {
      this.logger.error('Error in website scraping:', error);
      throw error;
    }
  }

  async contentSummarizer(params) {
    try {
      const { 
        content, 
        summary_type = 'extractive', 
        max_length = 500, 
        focus_area, 
        include_metadata = true 
      } = params;

      this.logger.info(`Summarizing content (${content.length} chars) as ${summary_type}`);

      const tokenCount = this.tokenizer.encode(content).length;
      
      let prompt;
      switch (summary_type) {
        case 'extractive':
          prompt = `Extract the most important sentences from the following text to create a summary of approximately ${max_length} words${focus_area ? ` focusing on ${focus_area}` : ''}:\n\n${content}`;
          break;
        case 'abstractive':
          prompt = `Create an abstractive summary of approximately ${max_length} words from the following text${focus_area ? ` focusing on ${focus_area}` : ''}:\n\n${content}`;
          break;
        case 'bullet_points':
          prompt = `Create a bullet-point summary of the key points from the following text${focus_area ? ` focusing on ${focus_area}` : ''}:\n\n${content}`;
          break;
        case 'key_facts':
          prompt = `Extract the key facts and important information from the following text${focus_area ? ` focusing on ${focus_area}` : ''}:\n\n${content}`;
          break;
      }

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at creating clear, concise, and informative summaries. Focus on accuracy and relevance.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: Math.min(max_length * 2, 2000),
        temperature: 0.3
      });

      const summary = response.choices[0].message.content;
      const summaryTokens = this.tokenizer.encode(summary).length;

      const result = {
        success: true,
        summary,
        summary_type,
        original_length: content.length,
        summary_length: summary.length,
        compression_ratio: (content.length / summary.length).toFixed(2)
      };

      if (include_metadata) {
        result.metadata = {
          original_tokens: tokenCount,
          summary_tokens: summaryTokens,
          focus_area,
          generated_at: new Date().toISOString()
        };
      }

      this.logger.info(`Generated ${summary_type} summary: ${summary.length} characters`);

      return result;
    } catch (error) {
      this.logger.error('Error in content summarization:', error);
      throw error;
    }
  }

  async researchAggregator(params) {
    try {
      const { 
        topic, 
        sources, 
        report_type = 'comprehensive', 
        include_citations = true, 
        fact_check = false 
      } = params;

      this.logger.info(`Aggregating research on topic: ${topic} from ${sources.length} sources`);

      // Prepare sources content
      const sourceContents = sources.map((source, index) => ({
        id: index + 1,
        url: source.url,
        title: source.title || `Source ${index + 1}`,
        content: source.content,
        credibility_score: source.credibility_score || 0.7
      }));

      // Generate research report
      let prompt;
      switch (report_type) {
        case 'comprehensive':
          prompt = `Create a comprehensive research report on "${topic}" using the following sources. Include an introduction, main findings, analysis, and conclusion.`;
          break;
        case 'comparative':
          prompt = `Create a comparative analysis report on "${topic}" highlighting similarities, differences, and conflicting viewpoints from the sources.`;
          break;
        case 'factual':
          prompt = `Create a factual report on "${topic}" focusing on verifiable facts, statistics, and concrete information from the sources.`;
          break;
        case 'analytical':
          prompt = `Create an analytical report on "${topic}" that examines causes, effects, implications, and provides insights based on the sources.`;
          break;
      }

      const sourcesText = sourceContents.map(source => 
        `Source ${source.id} (${source.url}):\nTitle: ${source.title}\nCredibility: ${source.credibility_score}\nContent: ${source.content}\n\n`
      ).join('');

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: `You are an expert research analyst. Create well-structured, objective research reports with proper analysis and insights. ${include_citations ? 'Include proper citations using [Source X] format.' : 'Do not include citations.'}`
          },
          {
            role: 'user',
            content: `${prompt}\n\nSources:\n${sourcesText}`
          }
        ],
        max_tokens: 4000,
        temperature: 0.2
      });

      const report = response.choices[0].message.content;

      // Fact-checking if requested
      let factCheckResults = null;
      if (fact_check) {
        factCheckResults = await this.performFactCheck(report, sourceContents);
      }

      const result = {
        success: true,
        topic,
        report_type,
        report,
        source_count: sources.length,
        generated_at: new Date().toISOString()
      };

      if (include_citations) {
        result.sources = sourceContents.map(source => ({
          id: source.id,
          url: source.url,
          title: source.title,
          credibility_score: source.credibility_score
        }));
      }

      if (fact_check) {
        result.fact_check = factCheckResults;
      }

      // Index the research report
      const reportId = uuidv4();
      await this.indexWebContent({
        url: `research://report/${reportId}`,
        title: `Research Report: ${topic}`,
        content: report,
        metadata: {
          document_type: 'research_report',
          topic,
          report_type,
          source_count: sources.length,
          credibility_score: sourceContents.reduce((avg, source) => avg + source.credibility_score, 0) / sourceContents.length
        }
      });

      this.logger.info(`Generated ${report_type} research report: ${report.length} characters`);

      return result;
    } catch (error) {
      this.logger.error('Error in research aggregation:', error);
      throw error;
    }
  }

  async retrieveRelevantDocuments(params) {
    try {
      const { 
        query, 
        document_type, 
        limit = 5, 
        similarity_threshold = 0.7, 
        date_range, 
        domain_filter 
      } = params;

      this.logger.info(`Retrieving relevant documents for query: ${query}`);

      // Generate embedding for the query
      const queryEmbedding = await this.generateQueryEmbedding(query);

      // Build where clause for filtering
      const whereClause = {};
      if (document_type) {
        whereClause.document_type = document_type;
      }

      // Search ChromaDB
      const results = await this.collection.query({
        queryEmbeddings: [queryEmbedding],
        nResults: limit * 2, // Get more results for filtering
        where: whereClause
      });

      // Process and filter results
      let processedResults = results.ids[0].map((id, index) => ({
        id,
        document_id: results.metadatas[0][index].document_id,
        url: results.metadatas[0][index].url,
        title: results.metadatas[0][index].title,
        content: results.documents[0][index],
        similarity: 1 - results.distances[0][index],
        metadata: results.metadatas[0][index]
      }))
      .filter(result => result.similarity >= similarity_threshold);

      // Apply date range filter
      if (date_range) {
        processedResults = processedResults.filter(result => {
          const docDate = new Date(result.metadata.indexed_at || result.metadata.publish_date);
          const startDate = new Date(date_range.start_date);
          const endDate = new Date(date_range.end_date);
          return docDate >= startDate && docDate <= endDate;
        });
      }

      // Apply domain filter
      if (domain_filter && domain_filter.length > 0) {
        processedResults = processedResults.filter(result => {
          const domain = result.metadata.domain || new URL(result.url).hostname;
          return domain_filter.includes(domain);
        });
      }

      // Sort by similarity and limit results
      processedResults = processedResults
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);

      this.logger.info(`Retrieved ${processedResults.length} relevant documents`);

      return {
        success: true,
        query,
        total_results: processedResults.length,
        results: processedResults,
        filters_applied: {
          document_type,
          similarity_threshold,
          date_range,
          domain_filter
        }
      };
    } catch (error) {
      this.logger.error('Error retrieving relevant documents:', error);
      throw error;
    }
  }

  async indexWebContent(params) {
    try {
      const { url, title, content, metadata = {} } = params;

      const documentId = uuidv4();
      const parsedUrl = urlParse(url);
      
      // Enhanced metadata
      const enhancedMetadata = {
        ...metadata,
        document_id: documentId,
        url,
        title: title || 'Untitled',
        domain: parsedUrl.hostname,
        document_type: metadata.document_type || 'web_content',
        indexed_at: new Date().toISOString(),
        content_length: content.length,
        token_count: this.tokenizer.encode(content).length
      };

      // Chunk the content
      const chunks = this.chunkDocument(content, 1000, 200);
      
      // Generate embeddings
      const embeddings = await this.generateEmbeddings(chunks);
      
      // Prepare data for ChromaDB
      const ids = chunks.map((_, index) => `${documentId}_chunk_${index}`);
      const metadatas = chunks.map((chunk, index) => ({
        ...enhancedMetadata,
        chunk_index: index,
        chunk_text: chunk.substring(0, 200) + '...'
      }));

      // Add to ChromaDB
      await this.collection.add({
        ids,
        embeddings,
        documents: chunks,
        metadatas
      });

      // Store in PostgreSQL
      await this.pgClient.query(
        'INSERT INTO web_research_documents (document_id, url, title, domain, document_type, content_length, chunk_count, indexed_at) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)',
        [documentId, url, title, parsedUrl.hostname, enhancedMetadata.document_type, content.length, chunks.length, new Date()]
      );

      this.logger.info(`Indexed web content: ${url} (${chunks.length} chunks)`);

      return {
        success: true,
        document_id: documentId,
        url,
        chunk_count: chunks.length,
        total_tokens: enhancedMetadata.token_count
      };
    } catch (error) {
      this.logger.error('Error indexing web content:', error);
      throw error;
    }
  }

  async bulkWebResearch(params) {
    try {
      const { queries = [], urls = [], auto_summarize = true, auto_index = true } = params;

      this.logger.info(`Starting bulk web research: ${queries.length} queries, ${urls.length} URLs`);

      const results = {
        search_results: [],
        scraped_content: [],
        summaries: [],
        indexed_documents: []
      };

      // Process search queries
      for (const query of queries) {
        try {
          const searchResult = await this.webSearch({ query, max_results: 5 });
          results.search_results.push(searchResult);

          // Auto-scrape top results
          for (const result of searchResult.results.slice(0, 3)) {
            try {
              const scrapedContent = await this.websiteScraper({ url: result.url });
              results.scraped_content.push(scrapedContent);

              if (auto_summarize) {
                const summary = await this.contentSummarizer({ 
                  content: scrapedContent.content,
                  summary_type: 'abstractive',
                  max_length: 300
                });
                results.summaries.push({ url: result.url, summary });
              }

              if (auto_index) {
                const indexed = await this.indexWebContent({
                  url: result.url,
                  title: scrapedContent.title,
                  content: scrapedContent.content,
                  metadata: {
                    query_source: query,
                    scraped_at: new Date().toISOString()
                  }
                });
                results.indexed_documents.push(indexed);
              }
            } catch (error) {
              this.logger.warn(`Failed to process search result ${result.url}:`, error);
            }
          }

          // Rate limiting
          await this.delay(this.requestDelay);
        } catch (error) {
          this.logger.error(`Failed to process query "${query}":`, error);
        }
      }

      // Process direct URLs
      for (const url of urls) {
        try {
          const scrapedContent = await this.websiteScraper({ url });
          results.scraped_content.push(scrapedContent);

          if (auto_summarize) {
            const summary = await this.contentSummarizer({ 
              content: scrapedContent.content,
              summary_type: 'abstractive',
              max_length: 300
            });
            results.summaries.push({ url, summary });
          }

          if (auto_index) {
            const indexed = await this.indexWebContent({
              url,
              title: scrapedContent.title,
              content: scrapedContent.content,
              metadata: {
                direct_url: true,
                scraped_at: new Date().toISOString()
              }
            });
            results.indexed_documents.push(indexed);
          }

          // Rate limiting
          await this.delay(this.requestDelay);
        } catch (error) {
          this.logger.error(`Failed to process URL ${url}:`, error);
        }
      }

      this.logger.info(`Bulk web research completed: ${results.scraped_content.length} pages scraped, ${results.indexed_documents.length} documents indexed`);

      return {
        success: true,
        processed_queries: queries.length,
        processed_urls: urls.length,
        results
      };
    } catch (error) {
      this.logger.error('Error in bulk web research:', error);
      throw error;
    }
  }

  // Helper methods

  async searchDuckDuckGo(query, maxResults, safeSearch) {
    try {
      // Use DuckDuckGo instant answer API and HTML scraping approach
      const searchUrl = `https://duckduckgo.com/html/?q=${encodeURIComponent(query)}&kl=us-en`;
      
      const response = await axios.get(searchUrl, {
        headers: { 
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data);
      const results = [];

      $('.result__body').each((i, elem) => {
        if (results.length >= maxResults) return false;
        
        const titleElem = $(elem).find('.result__title a');
        const snippetElem = $(elem).find('.result__snippet');
        
        const title = titleElem.text().trim();
        const url = titleElem.attr('href');
        const snippet = snippetElem.text().trim();

        if (title && url) {
          results.push({
            title,
            url: url.startsWith('http') ? url : `https://duckduckgo.com${url}`,
            snippet,
            source: 'duckduckgo'
          });
        }
      });

      return results;
    } catch (error) {
      this.logger.error('DuckDuckGo search error:', error);
      return [];
    }
  }

  async searchGoogle(query, maxResults, language, region, safeSearch) {
    // Note: This requires Google Custom Search API key
    // For now, return empty array - can be implemented with proper API key
    this.logger.warn('Google search not implemented - requires API key');
    return [];
  }

  async searchBing(query, maxResults, language, region, safeSearch) {
    // Note: This requires Bing Search API key
    // For now, return empty array - can be implemented with proper API key
    this.logger.warn('Bing search not implemented - requires API key');
    return [];
  }

  async scrapeWithCheerio(url, extractLinks, extractImages, customHeaders) {
    const headers = {
      'User-Agent': this.userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'Connection': 'keep-alive',
      ...customHeaders
    };

    const response = await axios.get(url, { headers, timeout: 30000 });
    const $ = cheerio.load(response.data);

    // Remove script and style elements
    $('script').remove();
    $('style').remove();
    $('nav').remove();
    $('.advertisement').remove();

    const result = {
      success: true,
      url,
      title: $('title').text().trim() || $('h1').first().text().trim() || 'Untitled',
      content: $('body').text().replace(/\s+/g, ' ').trim(),
      html: $.html(),
      metadata: {
        scraped_at: new Date().toISOString(),
        method: 'cheerio',
        response_status: response.status,
        content_type: response.headers['content-type'],
        content_length: response.data.length
      }
    };

    if (extractLinks) {
      result.links = [];
      $('a[href]').each((i, elem) => {
        const href = $(elem).attr('href');
        const text = $(elem).text().trim();
        if (href && text) {
          result.links.push({ url: new URL(href, url).href, text });
        }
      });
    }

    if (extractImages) {
      result.images = [];
      $('img[src]').each((i, elem) => {
        const src = $(elem).attr('src');
        const alt = $(elem).attr('alt') || '';
        if (src) {
          result.images.push({ url: new URL(src, url).href, alt });
        }
      });
    }

    return result;
  }

  async scrapeWithPuppeteer(url, extractLinks, extractImages, waitForSelector) {
    const page = await this.browser.newPage();
    
    try {
      await page.setUserAgent(this.userAgent);
      await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });

      if (waitForSelector) {
        await page.waitForSelector(waitForSelector, { timeout: 10000 });
      }

      const result = await page.evaluate((extractLinks, extractImages) => {
        // Remove unwanted elements
        const scripts = document.querySelectorAll('script, style, nav, .advertisement');
        scripts.forEach(el => el.remove());

        const data = {
          title: document.title || document.querySelector('h1')?.textContent?.trim() || 'Untitled',
          content: document.body.innerText.replace(/\s+/g, ' ').trim(),
          html: document.documentElement.outerHTML
        };

        if (extractLinks) {
          data.links = Array.from(document.querySelectorAll('a[href]')).map(a => ({
            url: a.href,
            text: a.textContent.trim()
          })).filter(link => link.url && link.text);
        }

        if (extractImages) {
          data.images = Array.from(document.querySelectorAll('img[src]')).map(img => ({
            url: img.src,
            alt: img.alt || ''
          })).filter(img => img.url);
        }

        return data;
      }, extractLinks, extractImages);

      result.success = true;
      result.url = url;
      result.metadata = {
        scraped_at: new Date().toISOString(),
        method: 'puppeteer',
        viewport: await page.viewport()
      };

      return result;
    } finally {
      await page.close();
    }
  }

  async scrapeWithReadability(url, customHeaders) {
    try {
      const headers = {
        'User-Agent': this.userAgent,
        ...customHeaders
      };

      const response = await axios.get(url, { headers, timeout: 30000 });
      const $ = cheerio.load(response.data);

      // Remove unwanted elements
      $('script, style, nav, .advertisement, .sidebar, .comments').remove();

      // Extract main content using simple heuristics
      let content = '';
      const contentSelectors = [
        'article',
        '.content',
        '.post-content', 
        '.entry-content',
        'main',
        '.main-content',
        '#content'
      ];

      for (const selector of contentSelectors) {
        const elem = $(selector);
        if (elem.length && elem.text().trim().length > content.length) {
          content = elem.text().trim();
        }
      }

      // Fallback to body if no main content found
      if (!content) {
        content = $('body').text().replace(/\s+/g, ' ').trim();
      }

      return {
        success: true,
        url,
        title: $('title').text().trim() || $('h1').first().text().trim() || 'Untitled',
        content: content,
        metadata: {
          scraped_at: new Date().toISOString(),
          method: 'readability',
          response_status: response.status,
          content_type: response.headers['content-type']
        }
      };
    } catch (error) {
      this.logger.error('Readability scraping error:', error);
      throw error;
    }
  }

  async performFactCheck(content, sources) {
    try {
      const prompt = `Analyze the following content for factual accuracy. Cross-reference with the provided sources and identify any potential inaccuracies, unsupported claims, or contradictions:

Content to fact-check:
${content}

Sources for verification:
${sources.map(source => `Source ${source.id}: ${source.title} - ${source.content.substring(0, 500)}...`).join('\n\n')}

Provide a fact-check analysis with:
1. Overall credibility score (0-1)
2. Specific claims that may be inaccurate
3. Claims that are well-supported
4. Contradictions between sources
5. Recommendations for further verification`;

      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert fact-checker. Analyze content objectively and identify potential inaccuracies or unsupported claims.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.1
      });

      const analysis = response.choices[0].message.content;

      return {
        analysis,
        performed_at: new Date().toISOString(),
        sources_checked: sources.length
      };
    } catch (error) {
      this.logger.error('Fact-check error:', error);
      return {
        analysis: 'Fact-check could not be performed due to an error',
        error: error.message,
        performed_at: new Date().toISOString()
      };
    }
  }

  async checkRobotsTxt(url) {
    try {
      const parsedUrl = urlParse(url);
      const robotsUrl = `${parsedUrl.protocol}//${parsedUrl.host}/robots.txt`;
      
      const response = await axios.get(robotsUrl, { timeout: 5000 });
      const robots = robotsParser(robotsUrl, response.data);
      
      return robots.isAllowed(url, this.userAgent);
    } catch (error) {
      // If robots.txt doesn't exist or can't be fetched, assume allowed
      return true;
    }
  }

  chunkDocument(content, chunkSize, overlap) {
    const chunks = [];
    const words = content.split(' ');
    
    for (let i = 0; i < words.length; i += chunkSize - overlap) {
      const chunk = words.slice(i, i + chunkSize).join(' ');
      if (chunk.trim()) {
        chunks.push(chunk);
      }
    }
    
    return chunks;
  }

  async generateEmbeddings(texts) {
    const embeddings = [];
    
    for (const text of texts) {
      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: text
      });
      embeddings.push(response.data[0].embedding);
    }
    
    return embeddings;
  }

  async generateQueryEmbedding(query) {
    const response = await this.openai.embeddings.create({
      model: this.embeddingModel,
      input: query
    });
    return response.data[0].embedding;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  setupHealthEndpoint() {
    const app = express();
    app.use(helmet());
    app.use(cors());

    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'web-research-mcp',
        timestamp: new Date().toISOString(),
        chromadb: this.collection ? 'connected' : 'not connected',
        browser: this.browser ? 'ready' : 'not ready'
      });
    });

    const port = process.env.MCP_SERVER_PORT || 8089;
    app.listen(port, () => {
      this.logger.info(`Web Research MCP Server health endpoint running on port ${port}`);
    });
  }
}

// Initialize and start the server
const server = new WebResearchServer();

async function start() {
  try {
    await server.initialize();
    await server.server.start();
    console.log('Web Research MCP Server started successfully');
  } catch (error) {
    console.error('Failed to start Web Research MCP Server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down Web Research MCP Server...');
  
  if (server.browser) {
    await server.browser.close();
  }
  
  if (server.pgClient) {
    await server.pgClient.end();
  }
  
  if (server.redisClient) {
    await server.redisClient.disconnect();
  }
  
  process.exit(0);
});

start();