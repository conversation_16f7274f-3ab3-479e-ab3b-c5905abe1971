#!/usr/bin/env python3
"""
Model Ensemble Router for Political Document Processing
Intelligent routing and load balancing across multiple AI models
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import random
import statistics
from abc import ABC, abstractmethod

class ModelType(Enum):
    CLAUDE = "claude"
    GPT4 = "gpt4"
    GEMINI = "gemini"
    PERPLEXITY = "perplexity"
    SPECIALIZED = "specialized"

class TaskComplexity(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ModelCapability:
    """Model capability profile"""
    model_id: str
    model_type: ModelType
    strengths: List[str]
    weaknesses: List[str]
    max_tokens: int
    cost_per_token: float
    latency_ms: int
    accuracy_score: float
    specializations: List[str] = field(default_factory=list)

@dataclass
class ModelInstance:
    """Runtime model instance"""
    model_id: str
    capability: ModelCapability
    status: str  # 'available', 'busy', 'error', 'maintenance'
    current_load: int = 0
    max_concurrent: int = 5
    last_used: datetime = field(default_factory=datetime.now)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    error_count: int = 0
    success_rate: float = 1.0

@dataclass
class RoutingRequest:
    """Request for model routing"""
    request_id: str
    task_type: str
    content: str
    complexity: TaskComplexity
    priority: int
    context: Dict[str, Any] = field(default_factory=dict)
    requirements: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class RoutingResponse:
    """Response from model routing"""
    request_id: str
    selected_model: str
    routing_reason: str
    estimated_cost: float
    estimated_latency: int
    confidence: float
    fallback_models: List[str] = field(default_factory=list)

class RoutingStrategy(ABC):
    """Abstract base class for routing strategies"""
    
    @abstractmethod
    async def select_model(self, request: RoutingRequest, 
                          available_models: List[ModelInstance]) -> Optional[ModelInstance]:
        pass

class LoadBalancingStrategy(RoutingStrategy):
    """Load balancing routing strategy"""
    
    async def select_model(self, request: RoutingRequest, 
                          available_models: List[ModelInstance]) -> Optional[ModelInstance]:
        # Filter available models
        suitable_models = [
            model for model in available_models
            if model.status == 'available' and model.current_load < model.max_concurrent
        ]
        
        if not suitable_models:
            return None
        
        # Select model with lowest load
        return min(suitable_models, key=lambda m: m.current_load / m.max_concurrent)

class CapabilityBasedStrategy(RoutingStrategy):
    """Capability-based routing strategy"""
    
    async def select_model(self, request: RoutingRequest, 
                          available_models: List[ModelInstance]) -> Optional[ModelInstance]:
        # Filter suitable models based on task type and specialization
        suitable_models = []
        
        for model in available_models:
            if model.status != 'available' or model.current_load >= model.max_concurrent:
                continue
            
            # Check specialization match
            if request.task_type in model.capability.specializations:
                suitable_models.append((model, 1.0))  # Perfect match
            elif any(spec in request.task_type for spec in model.capability.specializations):
                suitable_models.append((model, 0.7))  # Partial match
            elif model.capability.model_type == ModelType.SPECIALIZED:
                continue  # Skip specialized models without match
            else:
                suitable_models.append((model, 0.5))  # General purpose
        
        if not suitable_models:
            return None
        
        # Select best match considering capability score and performance
        best_model = max(suitable_models, key=lambda x: (
            x[1] * x[0].capability.accuracy_score * x[0].success_rate
        ))
        
        return best_model[0]

class CostOptimizedStrategy(RoutingStrategy):
    """Cost-optimized routing strategy"""
    
    async def select_model(self, request: RoutingRequest, 
                          available_models: List[ModelInstance]) -> Optional[ModelInstance]:
        # Filter available models
        suitable_models = [
            model for model in available_models
            if model.status == 'available' and model.current_load < model.max_concurrent
        ]
        
        if not suitable_models:
            return None
        
        # Calculate cost-effectiveness score
        def cost_effectiveness(model: ModelInstance) -> float:
            estimated_tokens = len(request.content.split()) * 1.3  # Rough estimate
            cost = estimated_tokens * model.capability.cost_per_token
            accuracy = model.capability.accuracy_score
            success_rate = model.success_rate
            
            return (accuracy * success_rate) / (cost + 0.001)  # Avoid division by zero
        
        # Select most cost-effective model
        return max(suitable_models, key=cost_effectiveness)

class PerformanceBasedStrategy(RoutingStrategy):
    """Performance-based routing strategy"""
    
    async def select_model(self, request: RoutingRequest, 
                          available_models: List[ModelInstance]) -> Optional[ModelInstance]:
        # Filter available models
        suitable_models = [
            model for model in available_models
            if model.status == 'available' and model.current_load < model.max_concurrent
        ]
        
        if not suitable_models:
            return None
        
        # Calculate performance score
        def performance_score(model: ModelInstance) -> float:
            accuracy = model.capability.accuracy_score
            latency_score = 1.0 / (model.capability.latency_ms / 1000.0 + 0.1)
            success_rate = model.success_rate
            load_factor = 1.0 - (model.current_load / model.max_concurrent)
            
            return accuracy * success_rate * latency_score * load_factor
        
        # Select highest performing model
        return max(suitable_models, key=performance_score)

class ModelEnsembleRouter:
    """
    Intelligent router for political document processing model ensemble
    Adapted from tool-integration-hub patterns
    """
    
    def __init__(self):
        self.models: Dict[str, ModelInstance] = {}
        self.routing_strategies: Dict[str, RoutingStrategy] = {}
        self.request_history: List[RoutingRequest] = []
        self.performance_history: List[Dict[str, Any]] = []
        self.logger = logging.getLogger(__name__)
        
        # Initialize strategies
        self._initialize_strategies()
        
        # Initialize model instances
        self._initialize_models()
        
        # Performance monitoring
        self.monitoring_enabled = True
        self.performance_window = timedelta(hours=1)
    
    def _initialize_strategies(self):
        """Initialize routing strategies"""
        self.routing_strategies = {
            "load_balancing": LoadBalancingStrategy(),
            "capability_based": CapabilityBasedStrategy(),
            "cost_optimized": CostOptimizedStrategy(),
            "performance_based": PerformanceBasedStrategy()
        }
    
    def _initialize_models(self):
        """Initialize model instances"""
        # Define model capabilities
        model_configs = [
            {
                "model_id": "claude-3-sonnet",
                "model_type": ModelType.CLAUDE,
                "strengths": ["reasoning", "analysis", "writing"],
                "weaknesses": ["real-time_data", "mathematics"],
                "max_tokens": 200000,
                "cost_per_token": 0.003,
                "latency_ms": 800,
                "accuracy_score": 0.92,
                "specializations": ["document_analysis", "synthesis", "fact_check"],
                "max_concurrent": 10
            },
            {
                "model_id": "gpt-4-turbo",
                "model_type": ModelType.GPT4,
                "strengths": ["versatility", "coding", "analysis"],
                "weaknesses": ["cost", "latency"],
                "max_tokens": 128000,
                "cost_per_token": 0.01,
                "latency_ms": 1200,
                "accuracy_score": 0.88,
                "specializations": ["document_analysis", "research"],
                "max_concurrent": 8
            },
            {
                "model_id": "gemini-pro",
                "model_type": ModelType.GEMINI,
                "strengths": ["speed", "multimodal", "reasoning"],
                "weaknesses": ["consistency", "specialized_tasks"],
                "max_tokens": 1000000,
                "cost_per_token": 0.001,
                "latency_ms": 500,
                "accuracy_score": 0.85,
                "specializations": ["research", "analysis"],
                "max_concurrent": 15
            },
            {
                "model_id": "perplexity-sonar",
                "model_type": ModelType.PERPLEXITY,
                "strengths": ["real-time_data", "research", "citations"],
                "weaknesses": ["creative_tasks", "analysis_depth"],
                "max_tokens": 4000,
                "cost_per_token": 0.005,
                "latency_ms": 600,
                "accuracy_score": 0.90,
                "specializations": ["research", "fact_check"],
                "max_concurrent": 12
            },
            {
                "model_id": "political-specialist",
                "model_type": ModelType.SPECIALIZED,
                "strengths": ["political_analysis", "policy_understanding"],
                "weaknesses": ["general_tasks", "creative_writing"],
                "max_tokens": 32000,
                "cost_per_token": 0.008,
                "latency_ms": 900,
                "accuracy_score": 0.95,
                "specializations": ["document_analysis", "political_content"],
                "max_concurrent": 5
            }
        ]
        
        # Create model instances
        for config in model_configs:
            capability = ModelCapability(
                model_id=config["model_id"],
                model_type=config["model_type"],
                strengths=config["strengths"],
                weaknesses=config["weaknesses"],
                max_tokens=config["max_tokens"],
                cost_per_token=config["cost_per_token"],
                latency_ms=config["latency_ms"],
                accuracy_score=config["accuracy_score"],
                specializations=config["specializations"]
            )
            
            instance = ModelInstance(
                model_id=config["model_id"],
                capability=capability,
                status="available",
                max_concurrent=config["max_concurrent"]
            )
            
            self.models[config["model_id"]] = instance
    
    async def route_request(self, request: RoutingRequest, 
                           strategy: str = "capability_based") -> RoutingResponse:
        """Route a request to the best available model"""
        
        # Get available models
        available_models = [
            model for model in self.models.values()
            if model.status == 'available'
        ]
        
        if not available_models:
            raise Exception("No available models")
        
        # Select routing strategy
        routing_strategy = self.routing_strategies.get(strategy)
        if not routing_strategy:
            routing_strategy = self.routing_strategies["capability_based"]
        
        # Apply routing strategy
        selected_model = await routing_strategy.select_model(request, available_models)
        
        if not selected_model:
            # Fallback to any available model
            selected_model = min(available_models, key=lambda m: m.current_load)
        
        # Prepare fallback models
        fallback_models = [
            model.model_id for model in available_models
            if model.model_id != selected_model.model_id
        ]
        fallback_models.sort(key=lambda mid: self.models[mid].capability.accuracy_score, reverse=True)
        
        # Calculate estimates
        estimated_cost = self._calculate_cost(request, selected_model)
        estimated_latency = self._calculate_latency(request, selected_model)
        confidence = self._calculate_confidence(request, selected_model)
        
        # Create response
        response = RoutingResponse(
            request_id=request.request_id,
            selected_model=selected_model.model_id,
            routing_reason=f"Selected by {strategy} strategy",
            estimated_cost=estimated_cost,
            estimated_latency=estimated_latency,
            confidence=confidence,
            fallback_models=fallback_models[:3]  # Top 3 fallbacks
        )
        
        # Update model load
        selected_model.current_load += 1
        selected_model.last_used = datetime.now()
        
        # Record routing decision
        self.request_history.append(request)
        self._record_routing_decision(request, response)
        
        self.logger.info(f"Routed request {request.request_id} to {selected_model.model_id}")
        return response
    
    def _calculate_cost(self, request: RoutingRequest, model: ModelInstance) -> float:
        """Calculate estimated cost for the request"""
        # Estimate token count
        input_tokens = len(request.content.split()) * 1.3
        output_tokens = input_tokens * 0.5  # Rough estimate
        total_tokens = input_tokens + output_tokens
        
        return total_tokens * model.capability.cost_per_token
    
    def _calculate_latency(self, request: RoutingRequest, model: ModelInstance) -> int:
        """Calculate estimated latency for the request"""
        base_latency = model.capability.latency_ms
        
        # Adjust for complexity
        complexity_multiplier = {
            TaskComplexity.LOW: 0.8,
            TaskComplexity.MEDIUM: 1.0,
            TaskComplexity.HIGH: 1.5,
            TaskComplexity.CRITICAL: 2.0
        }
        
        # Adjust for current load
        load_factor = 1.0 + (model.current_load / model.max_concurrent) * 0.5
        
        return int(base_latency * complexity_multiplier[request.complexity] * load_factor)
    
    def _calculate_confidence(self, request: RoutingRequest, model: ModelInstance) -> float:
        """Calculate confidence in the routing decision"""
        # Base confidence from model accuracy
        base_confidence = model.capability.accuracy_score
        
        # Adjust for specialization match
        specialization_bonus = 0.0
        if request.task_type in model.capability.specializations:
            specialization_bonus = 0.1
        
        # Adjust for success rate
        success_rate_factor = model.success_rate
        
        # Adjust for load
        load_penalty = (model.current_load / model.max_concurrent) * 0.1
        
        confidence = base_confidence + specialization_bonus
        confidence *= success_rate_factor
        confidence -= load_penalty
        
        return max(0.0, min(1.0, confidence))
    
    def _record_routing_decision(self, request: RoutingRequest, response: RoutingResponse):
        """Record routing decision for analysis"""
        record = {
            "timestamp": datetime.now().isoformat(),
            "request_id": request.request_id,
            "task_type": request.task_type,
            "complexity": request.complexity.value,
            "selected_model": response.selected_model,
            "estimated_cost": response.estimated_cost,
            "estimated_latency": response.estimated_latency,
            "confidence": response.confidence
        }
        
        self.performance_history.append(record)
        
        # Keep only recent history
        cutoff_time = datetime.now() - self.performance_window
        self.performance_history = [
            r for r in self.performance_history
            if datetime.fromisoformat(r["timestamp"]) > cutoff_time
        ]
    
    async def release_model(self, model_id: str, success: bool = True, 
                           actual_cost: float = 0.0, actual_latency: int = 0):
        """Release a model after task completion"""
        if model_id not in self.models:
            return
        
        model = self.models[model_id]
        
        # Update load
        model.current_load = max(0, model.current_load - 1)
        
        # Update performance metrics
        await self._update_performance_metrics(model, success, actual_cost, actual_latency)
        
        self.logger.info(f"Released model {model_id}, load: {model.current_load}")
    
    async def _update_performance_metrics(self, model: ModelInstance, success: bool, 
                                         actual_cost: float, actual_latency: int):
        """Update model performance metrics"""
        # Update success rate
        if "total_requests" not in model.performance_metrics:
            model.performance_metrics["total_requests"] = 0
            model.performance_metrics["successful_requests"] = 0
        
        model.performance_metrics["total_requests"] += 1
        if success:
            model.performance_metrics["successful_requests"] += 1
        
        # Update success rate
        total = model.performance_metrics["total_requests"]
        successful = model.performance_metrics["successful_requests"]
        model.success_rate = successful / total
        
        # Update error count
        if not success:
            model.error_count += 1
        
        # Update latency metrics
        if actual_latency > 0:
            if "latency_history" not in model.performance_metrics:
                model.performance_metrics["latency_history"] = []
            
            latency_history = model.performance_metrics["latency_history"]
            latency_history.append(actual_latency)
            
            # Keep only recent history
            if len(latency_history) > 100:
                latency_history = latency_history[-100:]
            
            model.performance_metrics["latency_history"] = latency_history
            model.performance_metrics["avg_latency"] = statistics.mean(latency_history)
        
        # Update cost metrics
        if actual_cost > 0:
            if "cost_history" not in model.performance_metrics:
                model.performance_metrics["cost_history"] = []
            
            cost_history = model.performance_metrics["cost_history"]
            cost_history.append(actual_cost)
            
            # Keep only recent history
            if len(cost_history) > 100:
                cost_history = cost_history[-100:]
            
            model.performance_metrics["cost_history"] = cost_history
            model.performance_metrics["avg_cost"] = statistics.mean(cost_history)
    
    async def get_model_status(self) -> Dict[str, Any]:
        """Get status of all models"""
        status = {}
        
        for model_id, model in self.models.items():
            status[model_id] = {
                "status": model.status,
                "current_load": model.current_load,
                "max_concurrent": model.max_concurrent,
                "utilization": model.current_load / model.max_concurrent,
                "success_rate": model.success_rate,
                "error_count": model.error_count,
                "last_used": model.last_used.isoformat(),
                "performance_metrics": model.performance_metrics,
                "capability": {
                    "model_type": model.capability.model_type.value,
                    "specializations": model.capability.specializations,
                    "accuracy_score": model.capability.accuracy_score,
                    "cost_per_token": model.capability.cost_per_token
                }
            }
        
        return status
    
    async def get_routing_analytics(self) -> Dict[str, Any]:
        """Get routing analytics and performance data"""
        if not self.performance_history:
            return {"message": "No routing history available"}
        
        # Calculate analytics
        total_requests = len(self.performance_history)
        model_usage = {}
        task_type_distribution = {}
        complexity_distribution = {}
        
        for record in self.performance_history:
            # Model usage
            model = record["selected_model"]
            model_usage[model] = model_usage.get(model, 0) + 1
            
            # Task type distribution
            task_type = record["task_type"]
            task_type_distribution[task_type] = task_type_distribution.get(task_type, 0) + 1
            
            # Complexity distribution
            complexity = record["complexity"]
            complexity_distribution[complexity] = complexity_distribution.get(complexity, 0) + 1
        
        # Calculate percentages
        for model in model_usage:
            model_usage[model] = {
                "count": model_usage[model],
                "percentage": (model_usage[model] / total_requests) * 100
            }
        
        # Calculate average metrics
        avg_cost = statistics.mean([r["estimated_cost"] for r in self.performance_history])
        avg_latency = statistics.mean([r["estimated_latency"] for r in self.performance_history])
        avg_confidence = statistics.mean([r["confidence"] for r in self.performance_history])
        
        return {
            "total_requests": total_requests,
            "model_usage": model_usage,
            "task_type_distribution": task_type_distribution,
            "complexity_distribution": complexity_distribution,
            "average_metrics": {
                "cost": avg_cost,
                "latency": avg_latency,
                "confidence": avg_confidence
            },
            "time_window": self.performance_window.total_seconds() / 3600  # hours
        }
    
    async def optimize_routing(self) -> Dict[str, Any]:
        """Analyze performance and suggest routing optimizations"""
        analytics = await self.get_routing_analytics()
        model_status = await self.get_model_status()
        
        recommendations = []
        
        # Check for underutilized models
        for model_id, status in model_status.items():
            if status["utilization"] < 0.3 and status["success_rate"] > 0.8:
                recommendations.append({
                    "type": "underutilized_model",
                    "model": model_id,
                    "message": f"Model {model_id} is underutilized but performs well",
                    "suggestion": "Consider routing more tasks to this model"
                })
        
        # Check for overutilized models
        for model_id, status in model_status.items():
            if status["utilization"] > 0.8:
                recommendations.append({
                    "type": "overutilized_model",
                    "model": model_id,
                    "message": f"Model {model_id} is heavily utilized",
                    "suggestion": "Consider load balancing or scaling"
                })
        
        # Check for poor performing models
        for model_id, status in model_status.items():
            if status["success_rate"] < 0.7:
                recommendations.append({
                    "type": "poor_performance",
                    "model": model_id,
                    "message": f"Model {model_id} has low success rate: {status['success_rate']:.2f}",
                    "suggestion": "Review model configuration or reduce routing"
                })
        
        return {
            "analytics": analytics,
            "model_status": model_status,
            "recommendations": recommendations,
            "optimization_timestamp": datetime.now().isoformat()
        }
    
    async def set_model_status(self, model_id: str, status: str):
        """Set model status (available, busy, error, maintenance)"""
        if model_id in self.models:
            self.models[model_id].status = status
            self.logger.info(f"Set model {model_id} status to {status}")
    
    async def add_model(self, model_config: Dict[str, Any]):
        """Add a new model to the ensemble"""
        capability = ModelCapability(
            model_id=model_config["model_id"],
            model_type=ModelType(model_config["model_type"]),
            strengths=model_config["strengths"],
            weaknesses=model_config["weaknesses"],
            max_tokens=model_config["max_tokens"],
            cost_per_token=model_config["cost_per_token"],
            latency_ms=model_config["latency_ms"],
            accuracy_score=model_config["accuracy_score"],
            specializations=model_config.get("specializations", [])
        )
        
        instance = ModelInstance(
            model_id=model_config["model_id"],
            capability=capability,
            status="available",
            max_concurrent=model_config.get("max_concurrent", 5)
        )
        
        self.models[model_config["model_id"]] = instance
        self.logger.info(f"Added model {model_config['model_id']} to ensemble")
    
    async def remove_model(self, model_id: str):
        """Remove a model from the ensemble"""
        if model_id in self.models:
            del self.models[model_id]
            self.logger.info(f"Removed model {model_id} from ensemble")


# Example usage
async def main():
    router = ModelEnsembleRouter()
    
    # Create a routing request
    request = RoutingRequest(
        request_id="req_001",
        task_type="document_analysis",
        content="This is a political document about healthcare policy...",
        complexity=TaskComplexity.HIGH,
        priority=1
    )
    
    # Route the request
    response = await router.route_request(request, strategy="capability_based")
    print(f"Routing Response: {response}")
    
    # Simulate task completion
    await asyncio.sleep(2)
    await router.release_model(response.selected_model, success=True, 
                              actual_cost=0.15, actual_latency=1200)
    
    # Get analytics
    analytics = await router.get_routing_analytics()
    print(f"Analytics: {analytics}")
    
    # Get optimization recommendations
    optimization = await router.optimize_routing()
    print(f"Optimization: {optimization}")

if __name__ == "__main__":
    asyncio.run(main())