#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import OpenAI from 'openai';
import winston from 'winston';
import natural from 'natural';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import compression from 'compression';
import Database from 'sqlite3';
import cron from 'node-cron';
import createGraph from 'graph-data-structure';
import { euclidean } from 'ml-distance';
import pino from 'pino';

/**
 * Memory Context MCP Server
 * Advanced memory and context management across n8n workflow executions
 */

class MemoryContextServer {
  constructor() {
    this.server = new MCPServer({
      name: 'memory-context',
      version: '1.0.0'
    });
    
    // Initialize tokenizer and logger
    this.tokenizer = encoding_for_model('gpt-4');
    this.logger = pino({
      level: process.env.LOG_LEVEL || 'info',
      transport: {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard'
        }
      }
    });
    
    // Memory storage
    this.conversationMemory = new Map();
    this.documentContexts = new Map();
    this.userPreferences = new Map();
    this.sessionContexts = new Map();
    this.knowledgeGraph = createGraph();
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    this.sqliteDb = null;
    this.openai = null;
    
    // Express server for health checks
    this.app = express();
    this.port = process.env.MCP_SERVER_PORT || process.env.PORT || 8095;
    
    this.setupExpress();
    this.setupTools();
    this.setupResources();
    this.scheduleMaintenance();
  }

  setupExpress() {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));

    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'memory-context-mcp-server',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        connections: {
          postgres: this.pgClient ? 'connected' : 'disconnected',
          redis: this.redisClient ? 'connected' : 'disconnected',
          sqlite: this.sqliteDb ? 'connected' : 'disconnected'
        }
      });
    });

    this.app.listen(this.port, () => {
      this.logger.info(`Memory Context MCP Server health endpoint listening on port ${this.port}`);
    });
  }

  async initialize() {
    try {
      // Initialize PostgreSQL connection
      this.pgClient = new Client({
        host: process.env.POSTGRES_HOST || 'postgres',
        port: process.env.POSTGRES_PORT || 5432,
        database: process.env.POSTGRES_DB || 'memory_context_db',
        user: process.env.POSTGRES_USER || 'n8n_user',
        password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
      });

      await this.pgClient.connect();
      await this.initializeDatabase();

      // Initialize Redis connection
      this.redisClient = createClient({
        host: process.env.REDIS_HOST || 'redis',
        port: process.env.REDIS_PORT || 6379
      });

      await this.redisClient.connect();

      // Initialize SQLite for local storage
      this.sqliteDb = new Database.Database(':memory:');
      await this.initializeSQLite();

      // Initialize OpenAI
      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY
      });

      this.logger.info('Memory Context MCP Server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Memory Context MCP Server:', error);
      throw error;
    }
  }

  async initializeDatabase() {
    const queries = [
      `CREATE TABLE IF NOT EXISTS conversation_memory (
        id SERIAL PRIMARY KEY,
        conversation_id VARCHAR(255) NOT NULL,
        user_id VARCHAR(255),
        message_content TEXT NOT NULL,
        context_type VARCHAR(100),
        emotional_tone VARCHAR(50),
        key_entities JSONB,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        embedding VECTOR(1536)
      )`,
      `CREATE TABLE IF NOT EXISTS document_contexts (
        id SERIAL PRIMARY KEY,
        document_id VARCHAR(255) NOT NULL,
        context_summary TEXT,
        key_topics JSONB,
        sentiment_analysis JSONB,
        complexity_score FLOAT,
        token_count INTEGER,
        last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS user_preferences (
        id SERIAL PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL UNIQUE,
        communication_style VARCHAR(100),
        preferred_topics JSONB,
        response_length VARCHAR(50),
        technical_level VARCHAR(50),
        preferences_data JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS session_contexts (
        id SERIAL PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL UNIQUE,
        user_id VARCHAR(255),
        workflow_context JSONB,
        active_documents JSONB,
        session_goals TEXT,
        progress_state JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS knowledge_graph_nodes (
        id SERIAL PRIMARY KEY,
        node_id VARCHAR(255) NOT NULL UNIQUE,
        node_type VARCHAR(100),
        properties JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS knowledge_graph_edges (
        id SERIAL PRIMARY KEY,
        from_node VARCHAR(255) NOT NULL,
        to_node VARCHAR(255) NOT NULL,
        relationship_type VARCHAR(100),
        weight FLOAT DEFAULT 1.0,
        properties JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    for (const query of queries) {
      await this.pgClient.query(query);
    }
  }

  async initializeSQLite() {
    return new Promise((resolve, reject) => {
      this.sqliteDb.serialize(() => {
        this.sqliteDb.run(`CREATE TABLE IF NOT EXISTS temp_memory (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          key TEXT NOT NULL,
          value TEXT NOT NULL,
          expiry INTEGER,
          created_at INTEGER DEFAULT (strftime('%s', 'now'))
        )`, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    });
  }

  setupTools() {
    // Tool 1: Conversation Memory Store
    this.server.addTool({
      name: 'conversation_memory_store',
      description: 'Store and retrieve conversation memory with context analysis',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['store', 'retrieve', 'search', 'analyze'],
            description: 'Action to perform on conversation memory'
          },
          conversation_id: {
            type: 'string',
            description: 'Unique conversation identifier'
          },
          user_id: {
            type: 'string',
            description: 'User identifier'
          },
          message_content: {
            type: 'string',
            description: 'Message content to store or search query'
          },
          context_type: {
            type: 'string',
            description: 'Type of context (question, answer, document_request, etc.)'
          },
          limit: {
            type: 'number',
            default: 10,
            description: 'Number of results to return'
          }
        },
        required: ['action']
      }
    }, async (params) => {
      return await this.handleConversationMemory(params);
    });

    // Tool 2: Document Context Tracker
    this.server.addTool({
      name: 'document_context_tracker',
      description: 'Track and analyze document contexts across workflow executions',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['track', 'retrieve', 'analyze', 'update', 'search'],
            description: 'Action to perform on document context'
          },
          document_id: {
            type: 'string',
            description: 'Unique document identifier'
          },
          document_content: {
            type: 'string',
            description: 'Document content for analysis'
          },
          context_summary: {
            type: 'string',
            description: 'Manual context summary'
          },
          search_query: {
            type: 'string',
            description: 'Search query for finding related documents'
          }
        },
        required: ['action']
      }
    }, async (params) => {
      return await this.handleDocumentContext(params);
    });

    // Tool 3: User Preference Manager
    this.server.addTool({
      name: 'user_preference_manager',
      description: 'Manage user preferences and personalization settings',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['get', 'set', 'update', 'delete', 'analyze'],
            description: 'Action to perform on user preferences'
          },
          user_id: {
            type: 'string',
            description: 'User identifier'
          },
          preferences: {
            type: 'object',
            description: 'User preferences object'
          },
          preference_key: {
            type: 'string',
            description: 'Specific preference key to get/set'
          },
          preference_value: {
            type: 'string',
            description: 'Preference value to set'
          }
        },
        required: ['action', 'user_id']
      }
    }, async (params) => {
      return await this.handleUserPreferences(params);
    });

    // Tool 4: Session Context Manager
    this.server.addTool({
      name: 'session_context_manager',
      description: 'Manage session contexts and workflow state tracking',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['create', 'get', 'update', 'delete', 'list', 'cleanup'],
            description: 'Action to perform on session context'
          },
          session_id: {
            type: 'string',
            description: 'Session identifier'
          },
          user_id: {
            type: 'string',
            description: 'User identifier'
          },
          workflow_context: {
            type: 'object',
            description: 'Workflow context data'
          },
          session_goals: {
            type: 'string',
            description: 'Session goals description'
          },
          progress_state: {
            type: 'object',
            description: 'Current progress state'
          }
        },
        required: ['action']
      }
    }, async (params) => {
      return await this.handleSessionContext(params);
    });

    // Tool 5: Knowledge Graph Builder
    this.server.addTool({
      name: 'knowledge_graph_builder',
      description: 'Build and query knowledge graphs from conversation and document data',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['add_node', 'add_edge', 'query', 'visualize', 'analyze', 'export'],
            description: 'Action to perform on knowledge graph'
          },
          node_id: {
            type: 'string',
            description: 'Node identifier'
          },
          node_type: {
            type: 'string',
            description: 'Type of node (person, concept, document, etc.)'
          },
          properties: {
            type: 'object',
            description: 'Node or edge properties'
          },
          from_node: {
            type: 'string',
            description: 'Source node for relationship'
          },
          to_node: {
            type: 'string',
            description: 'Target node for relationship'
          },
          relationship_type: {
            type: 'string',
            description: 'Type of relationship'
          },
          query: {
            type: 'string',
            description: 'Graph query or search term'
          },
          depth: {
            type: 'number',
            default: 2,
            description: 'Query depth for traversal'
          }
        },
        required: ['action']
      }
    }, async (params) => {
      return await this.handleKnowledgeGraph(params);
    });
  }

  setupResources() {
    this.server.addResource({
      uri: 'memory://conversations',
      name: 'Conversation Memory',
      description: 'Access to stored conversation memories',
      mimeType: 'application/json'
    }, async () => {
      const conversations = Array.from(this.conversationMemory.entries()).map(([id, data]) => ({
        id,
        ...data
      }));
      return JSON.stringify(conversations, null, 2);
    });

    this.server.addResource({
      uri: 'memory://knowledge-graph',
      name: 'Knowledge Graph',
      description: 'Visual representation of knowledge relationships',
      mimeType: 'application/json'
    }, async () => {
      return JSON.stringify({
        nodes: this.knowledgeGraph.nodes(),
        edges: this.knowledgeGraph.edges(),
        statistics: {
          nodeCount: this.knowledgeGraph.nodes().length,
          edgeCount: this.knowledgeGraph.edges().length
        }
      }, null, 2);
    });
  }

  async handleConversationMemory(params) {
    const { action, conversation_id, user_id, message_content, context_type, limit = 10 } = params;

    try {
      switch (action) {
        case 'store':
          if (!conversation_id || !message_content) {
            throw new Error('conversation_id and message_content are required for store action');
          }

          // Analyze message with NLP
          const sentiment = natural.SentimentAnalyzer.analyze(
            natural.WordTokenizer.tokenize(message_content)
          );
          const entities = this.extractEntities(message_content);

          // Get embedding
          const embedding = await this.getEmbedding(message_content);

          // Store in PostgreSQL
          const result = await this.pgClient.query(
            `INSERT INTO conversation_memory 
             (conversation_id, user_id, message_content, context_type, emotional_tone, key_entities, embedding) 
             VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`,
            [conversation_id, user_id, message_content, context_type, 
             sentiment > 0 ? 'positive' : sentiment < 0 ? 'negative' : 'neutral',
             JSON.stringify(entities), JSON.stringify(embedding)]
          );

          // Cache in memory
          this.conversationMemory.set(conversation_id, {
            messages: this.conversationMemory.get(conversation_id)?.messages || [],
            lastActivity: new Date()
          });

          return {
            success: true,
            message_id: result.rows[0].id,
            conversation_id,
            sentiment: sentiment > 0 ? 'positive' : sentiment < 0 ? 'negative' : 'neutral',
            entities
          };

        case 'retrieve':
          if (!conversation_id) {
            throw new Error('conversation_id is required for retrieve action');
          }

          const messages = await this.pgClient.query(
            `SELECT * FROM conversation_memory 
             WHERE conversation_id = $1 
             ORDER BY timestamp DESC LIMIT $2`,
            [conversation_id, limit]
          );

          return {
            success: true,
            conversation_id,
            messages: messages.rows,
            total: messages.rowCount
          };

        case 'search':
          if (!message_content) {
            throw new Error('message_content (search query) is required for search action');
          }

          const searchEmbedding = await this.getEmbedding(message_content);
          const searchResults = await this.pgClient.query(
            `SELECT *, (embedding <-> $1) as distance 
             FROM conversation_memory 
             WHERE user_id = $2 OR user_id IS NULL
             ORDER BY distance 
             LIMIT $3`,
            [JSON.stringify(searchEmbedding), user_id, limit]
          );

          return {
            success: true,
            query: message_content,
            results: searchResults.rows,
            total: searchResults.rowCount
          };

        case 'analyze':
          const stats = await this.pgClient.query(
            `SELECT 
               COUNT(*) as total_messages,
               COUNT(DISTINCT conversation_id) as total_conversations,
               AVG(CASE WHEN emotional_tone = 'positive' THEN 1 ELSE 0 END) as positive_ratio,
               COUNT(DISTINCT user_id) as unique_users
             FROM conversation_memory`
          );

          return {
            success: true,
            analysis: stats.rows[0]
          };

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error('Error in conversation memory handler:', error);
      return { success: false, error: error.message };
    }
  }

  async handleDocumentContext(params) {
    const { action, document_id, document_content, context_summary, search_query } = params;

    try {
      switch (action) {
        case 'track':
          if (!document_id || !document_content) {
            throw new Error('document_id and document_content are required for track action');
          }

          // Analyze document
          const analysis = await this.analyzeDocument(document_content);
          
          const result = await this.pgClient.query(
            `INSERT INTO document_contexts 
             (document_id, context_summary, key_topics, sentiment_analysis, complexity_score, token_count) 
             VALUES ($1, $2, $3, $4, $5, $6) 
             ON CONFLICT (document_id) DO UPDATE SET
               context_summary = EXCLUDED.context_summary,
               key_topics = EXCLUDED.key_topics,
               sentiment_analysis = EXCLUDED.sentiment_analysis,
               complexity_score = EXCLUDED.complexity_score,
               token_count = EXCLUDED.token_count,
               last_accessed = CURRENT_TIMESTAMP
             RETURNING id`,
            [document_id, analysis.summary, JSON.stringify(analysis.topics), 
             JSON.stringify(analysis.sentiment), analysis.complexity, analysis.tokenCount]
          );

          return {
            success: true,
            document_id,
            analysis
          };

        case 'retrieve':
          if (!document_id) {
            throw new Error('document_id is required for retrieve action');
          }

          const doc = await this.pgClient.query(
            'SELECT * FROM document_contexts WHERE document_id = $1',
            [document_id]
          );

          return {
            success: true,
            document: doc.rows[0] || null
          };

        case 'search':
          if (!search_query) {
            throw new Error('search_query is required for search action');
          }

          const searchResults = await this.pgClient.query(
            `SELECT * FROM document_contexts 
             WHERE context_summary ILIKE $1 OR key_topics::text ILIKE $1
             ORDER BY last_accessed DESC`,
            [`%${search_query}%`]
          );

          return {
            success: true,
            query: search_query,
            documents: searchResults.rows
          };

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error('Error in document context handler:', error);
      return { success: false, error: error.message };
    }
  }

  async handleUserPreferences(params) {
    const { action, user_id, preferences, preference_key, preference_value } = params;

    try {
      switch (action) {
        case 'get':
          const userPref = await this.pgClient.query(
            'SELECT * FROM user_preferences WHERE user_id = $1',
            [user_id]
          );

          return {
            success: true,
            user_id,
            preferences: userPref.rows[0] || null
          };

        case 'set':
          if (!preferences) {
            throw new Error('preferences object is required for set action');
          }

          const result = await this.pgClient.query(
            `INSERT INTO user_preferences 
             (user_id, communication_style, preferred_topics, response_length, technical_level, preferences_data) 
             VALUES ($1, $2, $3, $4, $5, $6)
             ON CONFLICT (user_id) DO UPDATE SET
               communication_style = EXCLUDED.communication_style,
               preferred_topics = EXCLUDED.preferred_topics,
               response_length = EXCLUDED.response_length,
               technical_level = EXCLUDED.technical_level,
               preferences_data = EXCLUDED.preferences_data,
               updated_at = CURRENT_TIMESTAMP
             RETURNING id`,
            [user_id, preferences.communication_style, JSON.stringify(preferences.preferred_topics),
             preferences.response_length, preferences.technical_level, JSON.stringify(preferences)]
          );

          return {
            success: true,
            user_id,
            preferences
          };

        case 'update':
          if (!preference_key || !preference_value) {
            throw new Error('preference_key and preference_value are required for update action');
          }

          await this.pgClient.query(
            `UPDATE user_preferences 
             SET preferences_data = jsonb_set(preferences_data, $2, $3, true),
                 updated_at = CURRENT_TIMESTAMP
             WHERE user_id = $1`,
            [user_id, `{${preference_key}}`, JSON.stringify(preference_value)]
          );

          return {
            success: true,
            user_id,
            updated_key: preference_key,
            new_value: preference_value
          };

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error('Error in user preferences handler:', error);
      return { success: false, error: error.message };
    }
  }

  async handleSessionContext(params) {
    const { action, session_id, user_id, workflow_context, session_goals, progress_state } = params;

    try {
      switch (action) {
        case 'create':
          if (!session_id) {
            throw new Error('session_id is required for create action');
          }

          const result = await this.pgClient.query(
            `INSERT INTO session_contexts 
             (session_id, user_id, workflow_context, session_goals, progress_state) 
             VALUES ($1, $2, $3, $4, $5) RETURNING id`,
            [session_id, user_id, JSON.stringify(workflow_context), 
             session_goals, JSON.stringify(progress_state)]
          );

          return {
            success: true,
            session_id,
            created: true
          };

        case 'get':
          if (!session_id) {
            throw new Error('session_id is required for get action');
          }

          const session = await this.pgClient.query(
            'SELECT * FROM session_contexts WHERE session_id = $1',
            [session_id]
          );

          return {
            success: true,
            session: session.rows[0] || null
          };

        case 'update':
          if (!session_id) {
            throw new Error('session_id is required for update action');
          }

          await this.pgClient.query(
            `UPDATE session_contexts 
             SET workflow_context = $2, session_goals = $3, progress_state = $4, last_activity = CURRENT_TIMESTAMP
             WHERE session_id = $1`,
            [session_id, JSON.stringify(workflow_context), session_goals, JSON.stringify(progress_state)]
          );

          return {
            success: true,
            session_id,
            updated: true
          };

        case 'cleanup':
          // Clean up old sessions (older than 24 hours)
          const cleanupResult = await this.pgClient.query(
            `DELETE FROM session_contexts 
             WHERE last_activity < NOW() - INTERVAL '24 hours'`
          );

          return {
            success: true,
            cleaned_sessions: cleanupResult.rowCount
          };

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error('Error in session context handler:', error);
      return { success: false, error: error.message };
    }
  }

  async handleKnowledgeGraph(params) {
    const { action, node_id, node_type, properties, from_node, to_node, relationship_type, query, depth = 2 } = params;

    try {
      switch (action) {
        case 'add_node':
          if (!node_id || !node_type) {
            throw new Error('node_id and node_type are required for add_node action');
          }

          await this.pgClient.query(
            `INSERT INTO knowledge_graph_nodes (node_id, node_type, properties) 
             VALUES ($1, $2, $3)
             ON CONFLICT (node_id) DO UPDATE SET
               node_type = EXCLUDED.node_type,
               properties = EXCLUDED.properties`,
            [node_id, node_type, JSON.stringify(properties)]
          );

          this.knowledgeGraph.addNode(node_id);

          return {
            success: true,
            node_id,
            node_type,
            properties
          };

        case 'add_edge':
          if (!from_node || !to_node || !relationship_type) {
            throw new Error('from_node, to_node, and relationship_type are required for add_edge action');
          }

          await this.pgClient.query(
            `INSERT INTO knowledge_graph_edges (from_node, to_node, relationship_type, properties) 
             VALUES ($1, $2, $3, $4)`,
            [from_node, to_node, relationship_type, JSON.stringify(properties)]
          );

          this.knowledgeGraph.addEdge(from_node, to_node);

          return {
            success: true,
            from_node,
            to_node,
            relationship_type
          };

        case 'query':
          if (!query) {
            throw new Error('query is required for query action');
          }

          const queryResults = await this.pgClient.query(
            `SELECT n1.node_id as from_node, n1.node_type as from_type, n1.properties as from_props,
                    e.relationship_type,
                    n2.node_id as to_node, n2.node_type as to_type, n2.properties as to_props
             FROM knowledge_graph_nodes n1
             JOIN knowledge_graph_edges e ON n1.node_id = e.from_node
             JOIN knowledge_graph_nodes n2 ON e.to_node = n2.node_id
             WHERE n1.properties::text ILIKE $1 OR n2.properties::text ILIKE $1`,
            [`%${query}%`]
          );

          return {
            success: true,
            query,
            relationships: queryResults.rows
          };

        case 'visualize':
          const allNodes = await this.pgClient.query('SELECT * FROM knowledge_graph_nodes LIMIT 100');
          const allEdges = await this.pgClient.query('SELECT * FROM knowledge_graph_edges LIMIT 200');

          return {
            success: true,
            visualization: {
              nodes: allNodes.rows,
              edges: allEdges.rows,
              format: 'graph-data'
            }
          };

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error) {
      this.logger.error('Error in knowledge graph handler:', error);
      return { success: false, error: error.message };
    }
  }

  // Utility methods
  async getEmbedding(text) {
    if (!this.openai) return [];
    
    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text
      });
      return response.data[0].embedding;
    } catch (error) {
      this.logger.error('Error getting embedding:', error);
      return [];
    }
  }

  extractEntities(text) {
    // Simple entity extraction using natural language processing
    const tokens = natural.WordTokenizer.tokenize(text);
    const tagged = natural.BrillPOSTagger ? natural.BrillPOSTagger.tag(tokens) : [];
    
    const entities = tagged
      .filter(tag => ['NNP', 'NNPS'].includes(tag.tag))
      .map(tag => tag.token);
    
    return [...new Set(entities)];
  }

  async analyzeDocument(content) {
    const tokens = this.tokenizer.encode(content);
    const tokenCount = tokens.length;
    
    // Basic complexity scoring
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;
    const complexity = Math.min(avgWordsPerSentence / 20, 1); // Normalize to 0-1

    // Extract key topics using TF-IDF (simplified)
    const wordFreq = {};
    const words_lower = content.toLowerCase().split(/\s+/);
    words_lower.forEach(word => {
      wordFreq[word] = (wordFreq[word] || 0) + 1;
    });

    const topics = Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);

    // Sentiment analysis
    const sentiment = natural.SentimentAnalyzer.analyze(
      natural.WordTokenizer.tokenize(content)
    );

    return {
      summary: content.substring(0, 500) + (content.length > 500 ? '...' : ''),
      topics,
      sentiment: {
        score: sentiment,
        label: sentiment > 0.1 ? 'positive' : sentiment < -0.1 ? 'negative' : 'neutral'
      },
      complexity,
      tokenCount
    };
  }

  scheduleMaintenance() {
    // Run cleanup every hour
    cron.schedule('0 * * * *', async () => {
      try {
        // Clean up old conversation memories
        await this.pgClient.query(
          `DELETE FROM conversation_memory 
           WHERE timestamp < NOW() - INTERVAL '30 days'`
        );

        // Clean up old session contexts
        await this.pgClient.query(
          `DELETE FROM session_contexts 
           WHERE last_activity < NOW() - INTERVAL '7 days'`
        );

        this.logger.info('Memory maintenance completed');
      } catch (error) {
        this.logger.error('Memory maintenance failed:', error);
      }
    });
  }

  async start() {
    await this.initialize();
    await this.server.start();
    this.logger.info('Memory Context MCP Server started successfully');
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    if (this.sqliteDb) await this.sqliteDb.close();
    this.logger.info('Memory Context MCP Server stopped');
  }
}

// Start the server
const server = new MemoryContextServer();
server.start().catch(console.error);

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down Memory Context MCP Server...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Memory Context MCP Server...');
  await server.stop();
  process.exit(0);
});