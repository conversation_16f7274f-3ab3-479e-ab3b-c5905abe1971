#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import natural from 'natural';
import Sentiment from 'sentiment';
import { withCircuitBreaker, logStructuredError, getHealthCheck } from '../shared/error-handling.js';

/**
 * Quality Control MCP Server
 * CEO-level quality control, manifesto alignment, and document review
 */

class QualityControlServer {
  constructor() {
    this.server = new MCPServer({
      name: 'quality-control',
      version: '1.0.0'
    });
    
    this.tokenizer = encoding_for_model('gpt-4');
    this.sentiment = new Sentiment();
    
    // AI clients for quality analysis
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    });
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    this.reportsDir = process.env.REPORTS_DIR || '/app/reports';
    
    this.setupTools();
    this.setupResources();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'political_conversations',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      // Connect to PostgreSQL with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.pgClient.connect();
      });
      
      // Connect to Redis with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.redisClient.connect();
      });
      
      console.log('Quality Control Server: Connected to databases');
      
      // Ensure reports directory exists
      await fs.ensureDir(this.reportsDir);
    } catch (error) {
      logStructuredError(error, 'QualityControlServer.initialize', {
        component: 'database_connection'
      });
      throw error;
    }
  }

  setupTools() {
    // Tool: Review Document (Primary Quality Control)
    this.server.addTool({
      name: 'review_document',
      description: 'Comprehensive quality review of political documents with CEO-level standards',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Document ID or content to review'
          },
          document_content: {
            type: 'string',
            description: 'Document content if not using document_id'
          },
          review_type: {
            type: 'string',
            enum: ['full', 'manifesto_alignment', 'voice_consistency', 'factual_accuracy', 'political_impact'],
            default: 'full',
            description: 'Type of quality review to perform'
          },
          strict_mode: {
            type: 'boolean',
            default: true,
            description: 'Apply strict CEO-level quality standards'
          },
          target_audience: {
            type: 'string',
            enum: ['general_public', 'legislators', 'media', 'activists', 'academics'],
            description: 'Target audience for tailored review criteria'
          }
        }
      }
    }, this.reviewDocument.bind(this));

    // Tool: Manifesto Alignment Check
    this.server.addTool({
      name: 'check_manifesto_alignment',
      description: 'Deep analysis of document alignment with Beau Lewis political manifesto',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to analyze'
          },
          manifesto_areas: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['economic_justice', 'democratic_renewal', 'voice_consistency', 'policy_framework', 'moral_clarity']
            },
            description: 'Specific manifesto areas to focus on'
          },
          return_recommendations: {
            type: 'boolean',
            default: true,
            description: 'Include specific improvement recommendations'
          }
        },
        required: ['document_content']
      }
    }, this.checkManifestoAlignment.bind(this));

    // Tool: Voice Consistency Analysis
    this.server.addTool({
      name: 'analyze_voice_consistency',
      description: 'Analyze document for Beau Lewis voice and tone consistency',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to analyze'
          },
          reference_documents: {
            type: 'array',
            items: { type: 'string' },
            description: 'Reference document IDs for voice comparison'
          },
          detailed_feedback: {
            type: 'boolean',
            default: true,
            description: 'Provide detailed feedback on voice issues'
          }
        },
        required: ['document_content']
      }
    }, this.analyzeVoiceConsistency.bind(this));

    // Tool: Political Impact Assessment
    this.server.addTool({
      name: 'assess_political_impact',
      description: 'Assess potential political impact and strategic effectiveness',
      inputSchema: {
        type: 'object',
        properties: {
          document_content: {
            type: 'string',
            description: 'Document content to assess'
          },
          document_type: {
            type: 'string',
            enum: ['white_paper', 'policy_brief', 'speech', 'manifesto', 'press_release'],
            description: 'Type of political document'
          },
          risk_assessment: {
            type: 'boolean',
            default: true,
            description: 'Include political risk assessment'
          },
          coalition_building_potential: {
            type: 'boolean',
            default: true,
            description: 'Analyze coalition building potential'
          }
        },
        required: ['document_content', 'document_type']
      }
    }, this.assessPoliticalImpact.bind(this));

    // Tool: Generate Quality Report
    this.server.addTool({
      name: 'generate_quality_report',
      description: 'Generate comprehensive quality control report with recommendations',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Document ID to generate report for'
          },
          include_metrics: {
            type: 'boolean',
            default: true,
            description: 'Include detailed quality metrics'
          },
          include_comparisons: {
            type: 'boolean',
            default: true,
            description: 'Include comparisons to best-performing documents'
          },
          report_format: {
            type: 'string',
            enum: ['detailed', 'executive_summary', 'action_items'],
            default: 'detailed'
          }
        },
        required: ['document_id']
      }
    }, this.generateQualityReport.bind(this));

    // Tool: Batch Quality Audit
    this.server.addTool({
      name: 'batch_quality_audit',
      description: 'Perform quality audit across multiple documents',
      inputSchema: {
        type: 'object',
        properties: {
          document_ids: {
            type: 'array',
            items: { type: 'string' },
            description: 'List of document IDs to audit'
          },
          audit_criteria: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['manifesto_alignment', 'voice_consistency', 'factual_accuracy', 'political_effectiveness']
            },
            description: 'Criteria to audit across all documents'
          },
          comparative_analysis: {
            type: 'boolean',
            default: true,
            description: 'Perform comparative analysis between documents'
          }
        },
        required: ['document_ids']
      }
    }, this.batchQualityAudit.bind(this));

    // Tool: Set Quality Standards
    this.server.addTool({
      name: 'set_quality_standards',
      description: 'Configure quality control standards and thresholds',
      inputSchema: {
        type: 'object',
        properties: {
          standards_config: {
            type: 'object',
            properties: {
              minimum_manifesto_score: { type: 'number', minimum: 0, maximum: 10 },
              minimum_voice_score: { type: 'number', minimum: 0, maximum: 10 },
              minimum_factual_score: { type: 'number', minimum: 0, maximum: 10 },
              minimum_overall_score: { type: 'number', minimum: 0, maximum: 10 },
              auto_approve_threshold: { type: 'number', minimum: 0, maximum: 10 },
              require_manual_review_below: { type: 'number', minimum: 0, maximum: 10 }
            }
          },
          document_type_standards: {
            type: 'object',
            description: 'Different standards for different document types'
          }
        },
        required: ['standards_config']
      }
    }, this.setQualityStandards.bind(this));
  }

  setupResources() {
    this.server.addResource({
      uri: 'quality://review_reports',
      name: 'Quality Review Reports',
      description: 'Generated quality control reports and analyses',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'quality://quality_standards',
      name: 'Quality Control Standards',
      description: 'Current quality control standards and thresholds',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'quality://performance_metrics',
      name: 'Quality Performance Metrics',
      description: 'Historical quality performance metrics and trends',
      mimeType: 'application/json'
    });
  }

  async reviewDocument({
    document_id,
    document_content,
    review_type = 'full',
    strict_mode = true,
    target_audience
  }) {
    try {
      const reviewId = this.generateReviewId();
      
      // Get document content if using document_id
      let content = document_content;
      if (document_id && !content) {
        content = await this.getDocumentContent(document_id);
      }

      if (!content) {
        throw new Error('No document content provided');
      }

      console.log(`Starting ${review_type} review ${reviewId} (strict: ${strict_mode})`);

      const review = {
        review_id: reviewId,
        document_id,
        review_type,
        strict_mode,
        target_audience,
        timestamp: new Date().toISOString(),
        scores: {},
        feedback: {},
        recommendations: [],
        overall_assessment: '',
        approved: false
      };

      // Perform different types of analysis based on review_type
      switch (review_type) {
        case 'full':
          review.scores = await this.performFullReview(content, strict_mode, target_audience);
          break;
        case 'manifesto_alignment':
          review.scores.manifesto_alignment_score = await this.scoreManifestoAlignment(content);
          break;
        case 'voice_consistency':
          review.scores.voice_consistency_score = await this.scoreVoiceConsistency(content);
          break;
        case 'factual_accuracy':
          review.scores.factual_accuracy_score = await this.scoreFactualAccuracy(content);
          break;
        case 'political_impact':
          review.scores.political_impact_score = await this.scorePoliticalImpact(content);
          break;
      }

      // Generate detailed feedback
      review.feedback = await this.generateDetailedFeedback(content, review.scores, review_type);
      
      // Generate recommendations
      review.recommendations = await this.generateRecommendations(content, review.scores, strict_mode);
      
      // Calculate overall assessment
      review.overall_assessment = await this.generateOverallAssessment(review.scores, review.feedback);
      
      // Determine approval status
      review.approved = this.determineApproval(review.scores, strict_mode);

      // Store review in database
      await this.storeQualityReview(review);

      return review;

    } catch (error) {
      console.error('Error in document review:', error);
      throw error;
    }
  }

  async checkManifestoAlignment({
    document_content,
    manifesto_areas = ['economic_justice', 'democratic_renewal', 'voice_consistency'],
    return_recommendations = true
  }) {
    try {
      const alignmentId = this.generateAlignmentId();
      
      const alignment = {
        alignment_id: alignmentId,
        manifesto_areas,
        timestamp: new Date().toISOString(),
        area_scores: {},
        overall_alignment: 0,
        key_issues: [],
        strengths: [],
        recommendations: []
      };

      // Analyze each manifesto area
      for (const area of manifesto_areas) {
        alignment.area_scores[area] = await this.analyzeManifestoArea(document_content, area);
      }

      // Calculate overall alignment
      const scores = Object.values(alignment.area_scores);
      alignment.overall_alignment = scores.reduce((a, b) => a + b, 0) / scores.length;

      // Identify issues and strengths
      alignment.key_issues = await this.identifyAlignmentIssues(document_content, alignment.area_scores);
      alignment.strengths = await this.identifyAlignmentStrengths(document_content, alignment.area_scores);

      // Generate recommendations if requested
      if (return_recommendations) {
        alignment.recommendations = await this.generateAlignmentRecommendations(
          document_content, 
          alignment.area_scores,
          alignment.key_issues
        );
      }

      return alignment;

    } catch (error) {
      console.error('Error checking manifesto alignment:', error);
      throw error;
    }
  }

  async analyzeVoiceConsistency({
    document_content,
    reference_documents = [],
    detailed_feedback = true
  }) {
    try {
      const analysisId = this.generateAnalysisId();
      
      const analysis = {
        analysis_id: analysisId,
        timestamp: new Date().toISOString(),
        voice_score: 0,
        consistency_metrics: {},
        voice_elements: {},
        issues_found: [],
        recommendations: []
      };

      // Analyze voice elements
      analysis.voice_elements = await this.analyzeVoiceElements(document_content);
      
      // Compare with reference documents if provided
      if (reference_documents.length > 0) {
        analysis.consistency_metrics = await this.compareVoiceConsistency(
          document_content, 
          reference_documents
        );
      }

      // Calculate voice score
      analysis.voice_score = await this.calculateVoiceScore(analysis.voice_elements, analysis.consistency_metrics);

      // Identify voice issues
      analysis.issues_found = await this.identifyVoiceIssues(document_content, analysis.voice_elements);

      // Generate detailed feedback
      if (detailed_feedback) {
        analysis.recommendations = await this.generateVoiceRecommendations(
          document_content,
          analysis.voice_elements,
          analysis.issues_found
        );
      }

      return analysis;

    } catch (error) {
      console.error('Error analyzing voice consistency:', error);
      throw error;
    }
  }

  async assessPoliticalImpact({
    document_content,
    document_type,
    risk_assessment = true,
    coalition_building_potential = true
  }) {
    try {
      const assessmentId = this.generateAssessmentId();
      
      const assessment = {
        assessment_id: assessmentId,
        document_type,
        timestamp: new Date().toISOString(),
        impact_score: 0,
        strategic_effectiveness: {},
        audience_resonance: {},
        risks: [],
        opportunities: [],
        coalition_potential: {}
      };

      // Analyze strategic effectiveness
      assessment.strategic_effectiveness = await this.analyzeStrategicEffectiveness(
        document_content, 
        document_type
      );

      // Analyze audience resonance
      assessment.audience_resonance = await this.analyzeAudienceResonance(document_content);

      // Perform risk assessment
      if (risk_assessment) {
        assessment.risks = await this.identifyPoliticalRisks(document_content, document_type);
      }

      // Identify opportunities
      assessment.opportunities = await this.identifyPoliticalOpportunities(document_content, document_type);

      // Analyze coalition building potential
      if (coalition_building_potential) {
        assessment.coalition_potential = await this.analyzeCoalitionPotential(document_content);
      }

      // Calculate overall impact score
      assessment.impact_score = await this.calculateImpactScore(assessment);

      return assessment;

    } catch (error) {
      console.error('Error assessing political impact:', error);
      throw error;
    }
  }

  async generateQualityReport({
    document_id,
    include_metrics = true,
    include_comparisons = true,
    report_format = 'detailed'
  }) {
    try {
      const reportId = this.generateReportId();
      
      // Get all reviews for this document
      const reviews = await this.getDocumentReviews(document_id);
      
      if (reviews.length === 0) {
        throw new Error(`No quality reviews found for document ${document_id}`);
      }

      const report = {
        report_id: reportId,
        document_id,
        report_format,
        timestamp: new Date().toISOString(),
        summary: {},
        detailed_analysis: {},
        trends: {},
        recommendations: [],
        action_items: []
      };

      // Generate summary
      report.summary = await this.generateReportSummary(reviews, report_format);

      // Include detailed metrics if requested
      if (include_metrics && report_format !== 'action_items') {
        report.detailed_analysis = await this.generateDetailedMetrics(reviews);
      }

      // Include comparisons if requested
      if (include_comparisons && report_format === 'detailed') {
        report.trends = await this.generateTrendAnalysis(document_id, reviews);
      }

      // Generate recommendations and action items
      report.recommendations = await this.generateReportRecommendations(reviews);
      report.action_items = await this.generateActionItems(reviews);

      // Save report
      const reportPath = path.join(this.reportsDir, `quality_report_${reportId}.json`);
      await fs.writeJson(reportPath, report, { spaces: 2 });

      return {
        ...report,
        report_path: reportPath
      };

    } catch (error) {
      console.error('Error generating quality report:', error);
      throw error;
    }
  }

  async batchQualityAudit({
    document_ids,
    audit_criteria = ['manifesto_alignment', 'voice_consistency', 'factual_accuracy'],
    comparative_analysis = true
  }) {
    try {
      const auditId = this.generateAuditId();
      
      const audit = {
        audit_id: auditId,
        document_ids,
        audit_criteria,
        timestamp: new Date().toISOString(),
        document_results: [],
        summary_statistics: {},
        comparative_results: {},
        recommendations: []
      };

      console.log(`Starting batch audit ${auditId} for ${document_ids.length} documents`);

      // Audit each document
      for (const docId of document_ids) {
        try {
          const docContent = await this.getDocumentContent(docId);
          const docResult = {
            document_id: docId,
            scores: {},
            issues: [],
            status: 'completed'
          };

          // Score each audit criterion
          for (const criterion of audit_criteria) {
            docResult.scores[criterion] = await this.scoreByCriterion(docContent, criterion);
          }

          audit.document_results.push(docResult);

        } catch (error) {
          console.error(`Failed to audit document ${docId}:`, error);
          audit.document_results.push({
            document_id: docId,
            status: 'failed',
            error: error.message
          });
        }
      }

      // Generate summary statistics
      audit.summary_statistics = this.calculateAuditStatistics(audit.document_results);

      // Perform comparative analysis
      if (comparative_analysis) {
        audit.comparative_results = this.performComparativeAnalysis(audit.document_results);
      }

      // Generate audit recommendations
      audit.recommendations = await this.generateAuditRecommendations(audit);

      // Save audit results
      const auditPath = path.join(this.reportsDir, `batch_audit_${auditId}.json`);
      await fs.writeJson(auditPath, audit, { spaces: 2 });

      return {
        ...audit,
        audit_path: auditPath
      };

    } catch (error) {
      console.error('Error in batch quality audit:', error);
      throw error;
    }
  }

  async setQualityStandards({ standards_config, document_type_standards = {} }) {
    try {
      const standardsId = this.generateStandardsId();
      
      const standards = {
        standards_id: standardsId,
        timestamp: new Date().toISOString(),
        global_standards: standards_config,
        document_type_standards,
        previous_standards_id: await this.getCurrentStandardsId()
      };

      // Validate standards
      this.validateQualityStandards(standards);

      // Store new standards
      await this.storeQualityStandards(standards);

      // Update cache
      await this.updateStandardsCache(standards);

      return {
        standards_id: standardsId,
        message: 'Quality standards updated successfully',
        effective_date: standards.timestamp,
        global_standards: standards.global_standards,
        document_type_count: Object.keys(document_type_standards).length
      };

    } catch (error) {
      console.error('Error setting quality standards:', error);
      throw error;
    }
  }

  // Helper methods

  async performFullReview(content, strictMode, targetAudience) {
    const scores = {};
    
    // Perform all quality checks
    scores.manifesto_alignment_score = await this.scoreManifestoAlignment(content);
    scores.voice_consistency_score = await this.scoreVoiceConsistency(content);
    scores.factual_accuracy_score = await this.scoreFactualAccuracy(content);
    scores.political_impact_score = await this.scorePoliticalImpact(content);
    
    // Calculate overall score
    const allScores = Object.values(scores);
    scores.overall_quality_score = allScores.reduce((a, b) => a + b, 0) / allScores.length;
    
    // Apply strict mode adjustments
    if (strictMode) {
      Object.keys(scores).forEach(key => {
        scores[key] = Math.max(0, scores[key] - 0.5); // Slightly stricter scoring
      });
    }
    
    return scores;
  }

  async scoreManifestoAlignment(content) {
    try {
      const prompt = `Analyze this political document for alignment with Beau Lewis's manifesto principles:

${content}

Rate the manifesto alignment on a scale of 1-10 based on:
- Economic justice messaging
- Democratic renewal themes
- Voice consistency with progressive values
- Policy framework alignment

Provide only a numeric score (1-10):`;

      // Use Anthropic for primary analysis with circuit breaker protection
      const response = await withCircuitBreaker('anthropic', async () => {
        return await this.anthropic.messages.create({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 50,
          messages: [{ role: 'user', content: prompt }]
        });
      }, {
        enableFallback: true,
        fallbackFunction: async (error) => {
          logStructuredError(error, 'QualityControlServer.scoreManifestoAlignment.fallback', {
            component: 'manifesto_scoring_fallback',
            content_length: content.length
          });
          
          // Fallback to OpenAI if Anthropic fails
          try {
            const fallbackResponse = await withCircuitBreaker('openai', async () => {
              return await this.openai.chat.completions.create({
                model: 'gpt-4',
                max_tokens: 50,
                messages: [{ role: 'user', content: prompt }]
              });
            });
            
            return {
              content: [{ text: fallbackResponse.choices[0].message.content }]
            };
          } catch (fallbackError) {
            logStructuredError(fallbackError, 'QualityControlServer.scoreManifestoAlignment.fallback_failed', {
              component: 'manifesto_scoring_fallback_failed'
            });
            throw new Error('Both primary and fallback AI services are unavailable for manifesto alignment scoring');
          }
        }
      });

      const scoreText = response.content[0].text.trim();
      const score = parseFloat(scoreText.match(/\d+(\.\d+)?/)?.[0] || '5');
      return Math.min(10, Math.max(0, score));

    } catch (error) {
      logStructuredError(error, 'QualityControlServer.scoreManifestoAlignment', {
        component: 'manifesto_alignment_scoring',
        content_length: content.length
      });
      // Return middle score if AI analysis fails
      return 5.0;
    }
  }

  async scoreVoiceConsistency(content) {
    try {
      const prompt = `Analyze this political document for voice consistency with Beau Lewis's authentic speaking style:

${content}

Rate the voice consistency on a scale of 1-10 based on:
- Passionate but professional tone
- Clear, direct communication style
- Consistent political messaging
- Authentic progressive voice

Provide only a numeric score (1-10):`;

      // Use Claude for voice analysis with circuit breaker protection
      const response = await withCircuitBreaker('anthropic', async () => {
        return await this.anthropic.messages.create({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 50,
          messages: [{ role: 'user', content: prompt }]
        });
      }, {
        enableFallback: true,
        fallbackFunction: async (error) => {
          logStructuredError(error, 'QualityControlServer.scoreVoiceConsistency.fallback', {
            component: 'voice_scoring_fallback',
            content_length: content.length
          });
          
          // Fallback to OpenAI if Anthropic fails
          try {
            const fallbackResponse = await withCircuitBreaker('openai', async () => {
              return await this.openai.chat.completions.create({
                model: 'gpt-4',
                max_tokens: 50,
                messages: [{ role: 'user', content: prompt }]
              });
            });
            
            return {
              content: [{ text: fallbackResponse.choices[0].message.content }]
            };
          } catch (fallbackError) {
            logStructuredError(fallbackError, 'QualityControlServer.scoreVoiceConsistency.fallback_failed', {
              component: 'voice_scoring_fallback_failed'
            });
            throw new Error('Both primary and fallback AI services are unavailable for voice consistency scoring');
          }
        }
      });

      const scoreText = response.content[0].text.trim();
      const score = parseFloat(scoreText.match(/\d+(\.\d+)?/)?.[0] || '5');
      return Math.min(10, Math.max(0, score));

    } catch (error) {
      logStructuredError(error, 'QualityControlServer.scoreVoiceConsistency', {
        component: 'voice_consistency_scoring',
        content_length: content.length
      });
      // Return middle score if AI analysis fails
      return 5.0;
    }
  }

  async scoreFactualAccuracy(content) {
    try {
      const prompt = `Analyze this political document for factual accuracy and evidence-based claims:

${content}

Rate the factual accuracy on a scale of 1-10 based on:
- Accuracy of statistical claims
- Proper use of evidence and examples
- Realistic policy proposals
- Factual consistency throughout

Provide only a numeric score (1-10):`;

      // Use GPT-4 for factual analysis with circuit breaker protection
      const response = await withCircuitBreaker('openai', async () => {
        return await this.openai.chat.completions.create({
          model: 'gpt-4',
          max_tokens: 50,
          messages: [{ role: 'user', content: prompt }]
        });
      }, {
        enableFallback: true,
        fallbackFunction: async (error) => {
          logStructuredError(error, 'QualityControlServer.scoreFactualAccuracy.fallback', {
            component: 'factual_scoring_fallback',
            content_length: content.length
          });
          
          // Fallback to Anthropic if OpenAI fails
          try {
            const fallbackResponse = await withCircuitBreaker('anthropic', async () => {
              return await this.anthropic.messages.create({
                model: 'claude-3-sonnet-20240229',
                max_tokens: 50,
                messages: [{ role: 'user', content: prompt }]
              });
            });
            
            return {
              choices: [{ message: { content: fallbackResponse.content[0].text } }]
            };
          } catch (fallbackError) {
            logStructuredError(fallbackError, 'QualityControlServer.scoreFactualAccuracy.fallback_failed', {
              component: 'factual_scoring_fallback_failed'
            });
            throw new Error('Both primary and fallback AI services are unavailable for factual accuracy scoring');
          }
        }
      });

      const scoreText = response.choices[0].message.content.trim();
      const score = parseFloat(scoreText.match(/\d+(\.\d+)?/)?.[0] || '5');
      return Math.min(10, Math.max(0, score));

    } catch (error) {
      logStructuredError(error, 'QualityControlServer.scoreFactualAccuracy', {
        component: 'factual_accuracy_scoring',
        content_length: content.length
      });
      // Return middle score if AI analysis fails
      return 5.0;
    }
  }

  async scorePoliticalImpact(content) {
    try {
      const prompt = `Analyze this political document for potential political impact and strategic effectiveness:

${content}

Rate the political impact on a scale of 1-10 based on:
- Persuasive messaging and rhetoric
- Strategic positioning for policy goals
- Ability to mobilize supporters
- Potential for coalition building

Provide only a numeric score (1-10):`;

      // Use Claude for strategic political analysis with circuit breaker protection
      const response = await withCircuitBreaker('anthropic', async () => {
        return await this.anthropic.messages.create({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 50,
          messages: [{ role: 'user', content: prompt }]
        });
      }, {
        enableFallback: true,
        fallbackFunction: async (error) => {
          logStructuredError(error, 'QualityControlServer.scorePoliticalImpact.fallback', {
            component: 'political_impact_fallback',
            content_length: content.length
          });
          
          // Fallback to OpenAI if Anthropic fails
          try {
            const fallbackResponse = await withCircuitBreaker('openai', async () => {
              return await this.openai.chat.completions.create({
                model: 'gpt-4',
                max_tokens: 50,
                messages: [{ role: 'user', content: prompt }]
              });
            });
            
            return {
              content: [{ text: fallbackResponse.choices[0].message.content }]
            };
          } catch (fallbackError) {
            logStructuredError(fallbackError, 'QualityControlServer.scorePoliticalImpact.fallback_failed', {
              component: 'political_impact_fallback_failed'
            });
            throw new Error('Both primary and fallback AI services are unavailable for political impact scoring');
          }
        }
      });

      const scoreText = response.content[0].text.trim();
      const score = parseFloat(scoreText.match(/\d+(\.\d+)?/)?.[0] || '5');
      return Math.min(10, Math.max(0, score));

    } catch (error) {
      logStructuredError(error, 'QualityControlServer.scorePoliticalImpact', {
        component: 'political_impact_scoring',
        content_length: content.length
      });
      // Return middle score if AI analysis fails
      return 5.0;
    }
  }

  async generateDetailedFeedback(content, scores, reviewType) {
    // Implementation for generating detailed feedback
    return {
      strengths: ['Strong economic justice messaging', 'Clear policy framework'],
      weaknesses: ['Could strengthen international examples', 'Minor voice inconsistencies'],
      specific_issues: []
    };
  }

  async generateRecommendations(content, scores, strictMode) {
    // Implementation for generating improvement recommendations
    return [
      'Strengthen manifesto alignment in sections 2-3',
      'Add more specific policy implementation details',
      'Include additional international success examples'
    ];
  }

  async generateOverallAssessment(scores, feedback) {
    // Implementation for overall assessment generation
    const avgScore = Object.values(scores).reduce((a, b) => a + b, 0) / Object.values(scores).length;
    
    if (avgScore >= 8.5) return 'Excellent - meets all CEO standards';
    if (avgScore >= 7.0) return 'Good - minor improvements needed';
    if (avgScore >= 5.5) return 'Adequate - significant improvements required';
    return 'Below standards - major revision required';
  }

  determineApproval(scores, strictMode) {
    const avgScore = Object.values(scores).reduce((a, b) => a + b, 0) / Object.values(scores).length;
    const threshold = strictMode ? 7.5 : 6.5;
    return avgScore >= threshold;
  }

  async storeQualityReview(review) {
    try {
      await this.pgClient.query(`
        INSERT INTO quality_control_reviews (
          job_id, reviewer_agent, manifesto_alignment_score, voice_consistency_score,
          factual_accuracy_score, overall_quality_score, review_comments, approved
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        review.document_id,
        'quality-control-server',
        review.scores.manifesto_alignment_score || 0,
        review.scores.voice_consistency_score || 0,
        review.scores.factual_accuracy_score || 0,
        review.scores.overall_quality_score || 0,
        JSON.stringify(review.feedback),
        review.approved
      ]);
    } catch (error) {
      console.error('Error storing quality review:', error);
    }
  }

  async getDocumentContent(documentId) {
    // Implementation to retrieve document content from storage
    return null; // Placeholder
  }

  async getDocumentReviews(documentId) {
    // Implementation to get all reviews for a document
    return []; // Placeholder
  }

  generateReviewId() {
    return `review_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateAlignmentId() {
    return `alignment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateAnalysisId() {
    return `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateAssessmentId() {
    return `assessment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateReportId() {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateAuditId() {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateStandardsId() {
    return `standards_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Additional placeholder methods for comprehensive implementation
  async analyzeManifestoArea(content, area) { return Math.random() * 10; }
  async identifyAlignmentIssues(content, scores) { return []; }
  async identifyAlignmentStrengths(content, scores) { return []; }
  async generateAlignmentRecommendations(content, scores, issues) { return []; }
  async analyzeVoiceElements(content) { return {}; }
  async compareVoiceConsistency(content, refs) { return {}; }
  async calculateVoiceScore(elements, metrics) { return Math.random() * 10; }
  async identifyVoiceIssues(content, elements) { return []; }
  async generateVoiceRecommendations(content, elements, issues) { return []; }
  async analyzeStrategicEffectiveness(content, type) { return {}; }
  async analyzeAudienceResonance(content) { return {}; }
  async identifyPoliticalRisks(content, type) { return []; }
  async identifyPoliticalOpportunities(content, type) { return []; }
  async analyzeCoalitionPotential(content) { return {}; }
  async calculateImpactScore(assessment) { return Math.random() * 10; }
  async generateReportSummary(reviews, format) { return {}; }
  async generateDetailedMetrics(reviews) { return {}; }
  async generateTrendAnalysis(docId, reviews) { return {}; }
  async generateReportRecommendations(reviews) { return []; }
  async generateActionItems(reviews) { return []; }
  async scoreByCriterion(content, criterion) { return Math.random() * 10; }
  calculateAuditStatistics(results) { return {}; }
  performComparativeAnalysis(results) { return {}; }
  async generateAuditRecommendations(audit) { return []; }
  validateQualityStandards(standards) { return true; }
  async storeQualityStandards(standards) { return true; }
  async updateStandardsCache(standards) { return true; }
  async getCurrentStandardsId() { return null; }

  async start() {
    await this.initialize();
    await this.server.start();
    console.log('Quality Control MCP Server started');
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    await this.server.stop();
  }
}

// Start the server
const server = new QualityControlServer();

process.on('SIGINT', async () => {
  console.log('Shutting down Quality Control Server...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Quality Control Server...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);