# Multimodal ChromaDB MCP Server

A powerful multimodal content processing server that handles images, videos, and audio files with advanced AI analysis and vector storage in ChromaDB for the political document processing system.

## Overview

The Multimodal ChromaDB MCP Server provides:
- Image processing with OpenAI Vision analysis
- Video frame extraction and analysis
- Audio transcription with political content analysis
- Vector embeddings storage in ChromaDB
- Semantic search across multimodal content
- Real-time processing metrics and monitoring

## Features

### 🖼️ Image Processing
- **Automatic Optimization**: Resizes and optimizes images for processing
- **AI Vision Analysis**: Uses GPT-4 Vision to analyze political documents, charts, and symbols
- **Dimension Tracking**: Stores image metadata including size and dimensions
- **Vector Storage**: Generates embeddings for semantic image search

### 🎥 Video Processing
- **Frame Extraction**: Extracts key frames using FFmpeg
- **Multi-Frame Analysis**: Analyzes multiple frames for comprehensive understanding
- **Political Content Detection**: Identifies speakers, text, and political content
- **Efficient Storage**: Stores combined frame descriptions for searchability

### 🎙️ Audio Processing
- **Whisper Transcription**: High-accuracy transcription using OpenAI Whisper
- **Format Conversion**: Automatic conversion to optimal format for processing
- **Political Analysis**: Enhanced analysis of political content in transcriptions
- **Confidence Scoring**: Provides transcription confidence metrics

### 🔍 Search Capabilities
- **Semantic Search**: Find content based on meaning, not just keywords
- **Cross-Modal Search**: Search across images, videos, and audio with one query
- **Filtered Results**: Filter by modality, metadata, or custom parameters
- **Relevance Ranking**: Results sorted by semantic similarity

## Installation

### Prerequisites
- Node.js 16+
- ChromaDB instance
- FFmpeg (for video/audio processing)
- OpenAI API key
- Docker (optional)

### Environment Variables

```bash
# Server Configuration
MCP_SERVER_PORT=8091
LOG_LEVEL=info

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
WHISPER_MODEL=whisper-1

# ChromaDB Configuration
CHROMADB_URL=http://chromadb:8000

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5678

# File Upload Limits
MAX_FILE_SIZE=104857600  # 100MB
MAX_FILES=10
```

### Docker Installation

```bash
docker run -d \
  --name multimodal-chromadb-mcp \
  -p 8091:8091 \
  --env-file .env \
  --network n8n-network \
  -v /tmp:/tmp \
  multimodal-chromadb-mcp:latest
```

### Manual Installation

```bash
# Clone the repository
git clone <repository-url>
cd mcp-servers/multimodal-chromadb

# Install dependencies
npm install

# Install system dependencies
apt-get update && apt-get install -y ffmpeg

# Start the server
npm start
```

## MCP Tools

### 1. upload_multimodal_content
Upload and process images, videos, or audio files for political document analysis.

**Input Schema:**
```json
{
  "files": [/* File array - handled by multipart upload */],
  "metadata": {
    "category": "political_speech",
    "source": "congress_hearing",
    "date": "2024-01-15"
  }
}
```

**Supported File Types:**
- **Images**: JPEG, PNG, GIF, WebP
- **Videos**: MP4, AVI, MOV, WebM
- **Audio**: MP3, WAV, OGG, MP4A, FLAC, AAC

**Response Example:**
```json
{
  "message": "Successfully processed 3 files",
  "results": [
    {
      "filename": "congress_chart.png",
      "type": "image",
      "id": "uuid-1234",
      "description": "Chart showing budget allocation...",
      "dimensions": { "width": 1920, "height": 1080 },
      "size": 245678
    }
  ]
}
```

### 2. search_multimodal_content
Search across images, videos, and audio content using semantic similarity.

**Input Schema:**
```json
{
  "query": "federal budget discussions with charts",
  "modality": "all|image|video|audio",
  "limit": 10
}
```

**Response Example:**
```json
{
  "query": "federal budget discussions with charts",
  "totalResults": 25,
  "results": [
    {
      "id": "uuid-1234",
      "document": "Congressional budget hearing with pie chart showing...",
      "distance": 0.15,
      "metadata": {
        "filename": "budget_hearing.mp4",
        "type": "video",
        "duration": 1823.5
      },
      "collection": "political_videos"
    }
  ]
}
```

### 3. analyze_image_content
Deep analysis of political image content including text extraction and visual elements.

**Input Schema:**
```json
{
  "imageId": "uuid-1234",
  "analysisType": "text_extraction|visual_analysis|political_symbols|comprehensive"
}
```

### 4. get_multimodal_stats
Get statistics about stored multimodal content.

**Input Schema:**
```json
{
  "collection": "political_images|political_videos|political_audio|all"
}
```

**Response Example:**
```json
{
  "political_images": {
    "totalDocuments": 1523,
    "lastUpdated": "2024-01-20T15:30:00Z"
  },
  "political_videos": {
    "totalDocuments": 342,
    "lastUpdated": "2024-01-20T14:45:00Z"
  }
}
```

## API Endpoints

### Health & Monitoring
- `GET /health` - Service health check
- `GET /metrics` - Prometheus metrics

### MCP Tool Endpoint
- `POST /mcp/call` - Execute MCP tools (multipart/form-data for file uploads)

## ChromaDB Collections

### political_images
Stores political document images with CLIP embeddings:
- Document scans
- Charts and graphs
- Political symbols and logos
- Meeting photographs

### political_videos
Stores political video content with frame-based embeddings:
- Congressional hearings
- Political speeches
- News segments
- Campaign videos

### political_audio
Stores political audio/voice content with transcription embeddings:
- Speeches and addresses
- Interviews
- Podcasts
- Radio broadcasts

## Processing Pipeline

### Image Processing Flow
1. **Upload** → Validate file type and size
2. **Optimize** → Resize to max 1024x1024 maintaining aspect ratio
3. **Analyze** → GPT-4 Vision analysis for political content
4. **Embed** → Generate text embeddings from description
5. **Store** → Save to ChromaDB with metadata

### Video Processing Flow
1. **Upload** → Validate video format
2. **Extract** → Extract 10 frames at intervals
3. **Analyze** → Process first 5 frames with Vision API
4. **Combine** → Merge frame descriptions
5. **Store** → Save combined analysis to ChromaDB

### Audio Processing Flow
1. **Upload** → Validate audio format
2. **Convert** → Convert to 16kHz mono WAV
3. **Transcribe** → Process with Whisper API
4. **Analyze** → Political content analysis with GPT-4
5. **Store** → Save transcription and analysis to ChromaDB

## Performance Metrics

The server tracks:
- **Processing Time**: Duration for each file type
- **Success/Error Rates**: Processing completion statistics
- **Search Performance**: Query response times and hit rates
- **Resource Usage**: Memory and CPU utilization

Access metrics at `/metrics` endpoint for Prometheus integration.

## Best Practices

### File Size Optimization
- **Images**: Pre-resize large images to reduce processing time
- **Videos**: Keep videos under 5 minutes for optimal processing
- **Audio**: Use compressed formats (MP3, AAC) for faster uploads

### Batch Processing
- Upload multiple files in a single request for efficiency
- Maximum 10 files per request
- Total payload limit: 100MB

### Search Optimization
- Use specific, descriptive queries for better results
- Leverage modality filtering when you know the content type
- Limit results to improve response time

## Security Considerations

1. **File Validation**: All uploads are validated for type and content
2. **Size Limits**: Enforced to prevent DoS attacks
3. **Rate Limiting**: 1000 requests/15min general, 50 uploads/15min
4. **CORS**: Configured for specific origins only
5. **Content Security**: CSP headers prevent XSS attacks

## Troubleshooting

### Common Issues

1. **FFmpeg Not Found**
   ```bash
   # Install FFmpeg
   apt-get update && apt-get install -y ffmpeg
   ```

2. **ChromaDB Connection Failed**
   - Verify ChromaDB is running at configured URL
   - Check network connectivity
   - Ensure ChromaDB allows connections from server

3. **OpenAI API Errors**
   - Verify API key is valid
   - Check API rate limits
   - Monitor API usage and billing

4. **Large File Processing Timeout**
   - Consider splitting large videos into segments
   - Increase server timeout settings
   - Process files asynchronously

### Debug Mode
Enable detailed logging:
```bash
LOG_LEVEL=debug npm start
```

## Monitoring

### Key Metrics to Watch
- `multimodal_processing_total` - Total files processed by type
- `multimodal_processing_duration_seconds` - Processing time histograms
- `multimodal_vector_search_total` - Search query counts
- `nodejs_heap_size_used_bytes` - Memory usage

### Logging
Logs are written to:
- `/app/logs/error.log` - Error-level logs
- `/app/logs/combined.log` - All logs
- Console output - Simplified logs

## License

This MCP server is part of the political document processing system. See the main project license for details.