#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import { ChromaClient } from 'chromadb';
import { OpenAI } from 'openai';
import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { encoding_for_model } from 'tiktoken';
import winston from 'winston';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { Client } from 'pg';
import { createClient } from 'redis';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';

/**
 * Vector Search MCP Server
 * ChromaDB-powered vector search for intelligent political document context retrieval
 */

class VectorSearchServer {
  constructor() {
    this.server = new MCPServer({
      name: 'vector-search',
      version: '1.0.0'
    });

    // Initialize clients
    this.chromaClient = new ChromaClient({
      url: process.env.CHROMADB_URL || 'http://chromadb:8000'
    });
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });

    this.tokenizer = encoding_for_model('gpt-4');
    this.collection = null;
    this.pgClient = null;
    this.redisClient = null;

    // Configuration
    this.embeddingModel = 'text-embedding-3-small';
    this.chunkSize = 1000;
    this.chunkOverlap = 200;
    this.maxTokens = 50000;

    // Setup logging
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: '/app/logs/vector-search.log' })
      ]
    });

    this.setupTools();
    this.setupHealthEndpoint();
  }

  async initialize() {
    try {
      // Initialize database connections
      this.pgClient = new Client({
        host: process.env.POSTGRES_HOST || 'postgresql',
        port: process.env.POSTGRES_PORT || 5432,
        database: process.env.POSTGRES_DB || 'political_conversations',
        user: process.env.POSTGRES_USER || 'n8n_user',
        password: process.env.POSTGRES_PASSWORD || 'secure_password'
      });

      this.redisClient = createClient({
        host: process.env.REDIS_HOST || 'redis',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD
      });

      await this.pgClient.connect();
      await this.redisClient.connect();

      // Initialize ChromaDB collection
      await this.initializeChromaCollection();

      this.logger.info('Vector Search MCP Server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Vector Search MCP Server:', error);
      throw error;
    }
  }

  async initializeChromaCollection() {
    try {
      // Create or get the political documents collection
      this.collection = await this.chromaClient.getOrCreateCollection({
        name: 'political_documents',
        metadata: {
          'description': 'Political documents and manifesto content for RAG retrieval',
          'hnsw:space': 'cosine'
        }
      });

      this.logger.info('ChromaDB collection initialized: political_documents');
    } catch (error) {
      this.logger.error('Failed to initialize ChromaDB collection:', error);
      throw error;
    }
  }

  setupTools() {
    // Tool: Index Document
    this.server.addTool({
      name: 'index_document',
      description: 'Index a political document for vector search',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Unique identifier for the document'
          },
          content: {
            type: 'string',
            description: 'Full text content of the document'
          },
          metadata: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              category: { type: 'string' },
              author: { type: 'string', default: 'Beau Lewis' },
              date: { type: 'string' },
              document_type: { type: 'string' },
              file_path: { type: 'string' },
              token_count: { type: 'number' }
            }
          }
        },
        required: ['document_id', 'content']
      }
    }, this.indexDocument.bind(this));

    // Tool: Search Similar Content
    this.server.addTool({
      name: 'search_similar_content',
      description: 'Find similar political documents using vector search',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query for finding similar content'
          },
          category: {
            type: 'string',
            description: 'Filter by document category',
            enum: ['healthcare', 'education', 'economic_policy', 'housing', 'jobs_automation', 'constitutional_amendments', 'ethics_accountability', 'rights_repair_grow', 'funding_revenue']
          },
          limit: {
            type: 'number',
            default: 5,
            minimum: 1,
            maximum: 20,
            description: 'Maximum number of results to return'
          },
          minimum_similarity: {
            type: 'number',
            default: 0.7,
            minimum: 0.0,
            maximum: 1.0,
            description: 'Minimum similarity score threshold'
          }
        },
        required: ['query']
      }
    }, this.searchSimilarContent.bind(this));

    // Tool: Get Document Context
    this.server.addTool({
      name: 'get_document_context',
      description: 'Get relevant context for document generation using vector search',
      inputSchema: {
        type: 'object',
        properties: {
          topic: {
            type: 'string',
            description: 'Topic or theme for context retrieval'
          },
          category: {
            type: 'string',
            description: 'Document category for focused search',
            enum: ['healthcare', 'education', 'economic_policy', 'housing', 'jobs_automation', 'constitutional_amendments', 'ethics_accountability', 'rights_repair_grow', 'funding_revenue']
          },
          token_limit: {
            type: 'number',
            default: 10000,
            minimum: 1000,
            maximum: 50000,
            description: 'Maximum tokens for context (fits with 4-tier system)'
          },
          include_manifesto: {
            type: 'boolean',
            default: true,
            description: 'Include core manifesto content in context'
          }
        },
        required: ['topic']
      }
    }, this.getDocumentContext.bind(this));

    // Tool: Index Manifesto Documents
    this.server.addTool({
      name: 'index_manifesto_documents',
      description: 'Index all manifesto and political documents from the filesystem',
      inputSchema: {
        type: 'object',
        properties: {
          manifesto_path: {
            type: 'string',
            default: '/app/manifesto',
            description: 'Path to manifesto documents directory'
          },
          white_papers_path: {
            type: 'string',
            default: '/app/white_papers_markdown',
            description: 'Path to white papers directory'
          },
          force_reindex: {
            type: 'boolean',
            default: false,
            description: 'Force reindexing of all documents'
          }
        }
      }
    }, this.indexManifestoDocuments.bind(this));

    // Tool: Get Collection Stats
    this.server.addTool({
      name: 'get_collection_stats',
      description: 'Get statistics about the indexed document collection',
      inputSchema: {
        type: 'object',
        properties: {}
      }
    }, this.getCollectionStats.bind(this));

    // Tool: Update Document
    this.server.addTool({
      name: 'update_document',
      description: 'Update an existing document in the vector store',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Unique identifier for the document to update'
          },
          content: {
            type: 'string',
            description: 'Updated content of the document'
          },
          metadata: {
            type: 'object',
            description: 'Updated metadata for the document'
          }
        },
        required: ['document_id']
      }
    }, this.updateDocument.bind(this));

    // Tool: Delete Document
    this.server.addTool({
      name: 'delete_document',
      description: 'Remove a document from the vector store',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Unique identifier for the document to delete'
          }
        },
        required: ['document_id']
      }
    }, this.deleteDocument.bind(this));
  }

  async indexDocument(params) {
    try {
      const { document_id, content, metadata = {} } = params;
      
      // Validate input
      if (!document_id || !content) {
        throw new Error('Document ID and content are required');
      }

      // Check if document already exists
      const cacheKey = `vector_doc:${document_id}`;
      const existingDoc = await this.redisClient.get(cacheKey);
      
      if (existingDoc && !metadata.force_reindex) {
        this.logger.info(`Document ${document_id} already indexed, skipping`);
        return { success: true, message: 'Document already indexed', document_id };
      }

      // Chunk the document
      const chunks = this.chunkDocument(content, this.chunkSize, this.chunkOverlap);
      
      // Generate embeddings for each chunk
      const embeddings = await this.generateEmbeddings(chunks);
      
      // Prepare data for ChromaDB
      const ids = chunks.map((_, index) => `${document_id}_chunk_${index}`);
      const metadatas = chunks.map((chunk, index) => ({
        ...metadata,
        document_id,
        chunk_index: index,
        chunk_text: chunk.substring(0, 200) + '...',
        token_count: this.tokenizer.encode(chunk).length,
        indexed_at: new Date().toISOString()
      }));

      // Add to ChromaDB
      await this.collection.add({
        ids,
        embeddings,
        documents: chunks,
        metadatas
      });

      // Cache in Redis
      await this.redisClient.setex(cacheKey, 3600, JSON.stringify({
        document_id,
        chunk_count: chunks.length,
        total_tokens: chunks.reduce((sum, chunk) => sum + this.tokenizer.encode(chunk).length, 0),
        indexed_at: new Date().toISOString()
      }));

      // Store in PostgreSQL for tracking
      await this.pgClient.query(
        'INSERT INTO indexed_documents (document_id, title, category, chunk_count, total_tokens, indexed_at) VALUES ($1, $2, $3, $4, $5, $6) ON CONFLICT (document_id) DO UPDATE SET chunk_count = $4, total_tokens = $5, indexed_at = $6',
        [document_id, metadata.title || 'Untitled', metadata.category || 'general', chunks.length, metadatas.reduce((sum, meta) => sum + meta.token_count, 0), new Date()]
      );

      this.logger.info(`Successfully indexed document ${document_id} with ${chunks.length} chunks`);
      
      return {
        success: true,
        message: 'Document indexed successfully',
        document_id,
        chunk_count: chunks.length,
        total_tokens: chunks.reduce((sum, chunk) => sum + this.tokenizer.encode(chunk).length, 0)
      };
    } catch (error) {
      this.logger.error('Error indexing document:', error);
      throw error;
    }
  }

  async searchSimilarContent(params) {
    try {
      const { query, category, limit = 5, minimum_similarity = 0.7 } = params;

      // Generate embedding for the query
      const queryEmbedding = await this.generateQueryEmbedding(query);

      // Build where clause for category filtering
      const whereClause = category ? { category } : {};

      // Search ChromaDB
      const results = await this.collection.query({
        queryEmbeddings: [queryEmbedding],
        nResults: limit,
        where: whereClause
      });

      // Process results
      const processedResults = results.ids[0].map((id, index) => ({
        id,
        document_id: results.metadatas[0][index].document_id,
        content: results.documents[0][index],
        similarity: 1 - results.distances[0][index], // Convert distance to similarity
        metadata: results.metadatas[0][index]
      }))
      .filter(result => result.similarity >= minimum_similarity)
      .sort((a, b) => b.similarity - a.similarity);

      this.logger.info(`Found ${processedResults.length} similar documents for query: ${query}`);

      return {
        success: true,
        query,
        results: processedResults,
        total_results: processedResults.length
      };
    } catch (error) {
      this.logger.error('Error searching similar content:', error);
      throw error;
    }
  }

  async getDocumentContext(params) {
    try {
      const { topic, category, token_limit = 10000, include_manifesto = true } = params;

      // Search for relevant content
      const searchResults = await this.searchSimilarContent({
        query: topic,
        category,
        limit: 10,
        minimum_similarity: 0.6
      });

      // Build context from search results
      let context = '';
      let tokenCount = 0;

      // Add manifesto core content if requested
      if (include_manifesto) {
        const manifestoContext = await this.getManifestoContext(category);
        if (manifestoContext) {
          const manifestoTokens = this.tokenizer.encode(manifestoContext).length;
          if (tokenCount + manifestoTokens <= token_limit) {
            context += `## Core Manifesto Context\n\n${manifestoContext}\n\n`;
            tokenCount += manifestoTokens;
          }
        }
      }

      // Add search results
      context += `## Relevant Political Documents\n\n`;
      
      for (const result of searchResults.results) {
        const resultTokens = this.tokenizer.encode(result.content).length;
        if (tokenCount + resultTokens <= token_limit) {
          context += `### ${result.metadata.title || 'Document'} (Similarity: ${(result.similarity * 100).toFixed(1)}%)\n`;
          context += `${result.content}\n\n`;
          tokenCount += resultTokens;
        } else {
          break;
        }
      }

      // Add context metadata
      const contextMetadata = {
        topic,
        category,
        token_count: tokenCount,
        token_limit,
        documents_included: searchResults.results.length,
        includes_manifesto: include_manifesto,
        generated_at: new Date().toISOString()
      };

      this.logger.info(`Generated context for topic "${topic}" with ${tokenCount} tokens`);

      return {
        success: true,
        context,
        metadata: contextMetadata
      };
    } catch (error) {
      this.logger.error('Error getting document context:', error);
      throw error;
    }
  }

  async indexManifestoDocuments(params) {
    try {
      const { manifesto_path = '/app/manifesto', white_papers_path = '/app/white_papers_markdown', force_reindex = false } = params;
      
      let indexedCount = 0;
      const results = [];

      // Index manifesto documents
      if (await fs.pathExists(manifesto_path)) {
        const manifestoFiles = await this.findMarkdownFiles(manifesto_path);
        for (const filePath of manifestoFiles) {
          try {
            const content = await fs.readFile(filePath, 'utf8');
            const relativePath = path.relative(manifesto_path, filePath);
            const category = this.extractCategoryFromPath(relativePath);
            
            const result = await this.indexDocument({
              document_id: `manifesto_${path.basename(filePath, '.md')}`,
              content,
              metadata: {
                title: path.basename(filePath, '.md'),
                category,
                document_type: 'manifesto',
                file_path: relativePath,
                force_reindex
              }
            });
            
            results.push(result);
            indexedCount++;
          } catch (error) {
            this.logger.error(`Error indexing manifesto file ${filePath}:`, error);
          }
        }
      }

      // Index white papers
      if (await fs.pathExists(white_papers_path)) {
        const whitepaperFiles = await this.findMarkdownFiles(white_papers_path);
        for (const filePath of whitepaperFiles) {
          try {
            const content = await fs.readFile(filePath, 'utf8');
            const relativePath = path.relative(white_papers_path, filePath);
            const category = this.extractCategoryFromPath(relativePath);
            
            const result = await this.indexDocument({
              document_id: `whitepaper_${path.basename(filePath, '.md')}`,
              content,
              metadata: {
                title: path.basename(filePath, '.md'),
                category,
                document_type: 'whitepaper',
                file_path: relativePath,
                force_reindex
              }
            });
            
            results.push(result);
            indexedCount++;
          } catch (error) {
            this.logger.error(`Error indexing whitepaper file ${filePath}:`, error);
          }
        }
      }

      this.logger.info(`Indexed ${indexedCount} manifesto documents`);

      return {
        success: true,
        message: `Indexed ${indexedCount} documents`,
        indexed_count: indexedCount,
        results
      };
    } catch (error) {
      this.logger.error('Error indexing manifesto documents:', error);
      throw error;
    }
  }

  async getCollectionStats() {
    try {
      const collectionInfo = await this.collection.count();
      
      // Get category breakdown from PostgreSQL
      const categoryStats = await this.pgClient.query(
        'SELECT category, COUNT(*) as count, SUM(chunk_count) as total_chunks, SUM(total_tokens) as total_tokens FROM indexed_documents GROUP BY category ORDER BY count DESC'
      );

      return {
        success: true,
        total_documents: collectionInfo,
        categories: categoryStats.rows,
        collection_name: 'political_documents'
      };
    } catch (error) {
      this.logger.error('Error getting collection stats:', error);
      throw error;
    }
  }

  async updateDocument(params) {
    try {
      const { document_id, content, metadata } = params;
      
      // Delete existing document
      await this.deleteDocument({ document_id });
      
      // Re-index with new content
      const result = await this.indexDocument({
        document_id,
        content,
        metadata: { ...metadata, updated_at: new Date().toISOString() }
      });

      return result;
    } catch (error) {
      this.logger.error('Error updating document:', error);
      throw error;
    }
  }

  async deleteDocument(params) {
    try {
      const { document_id } = params;
      
      // Find all chunks for this document
      const results = await this.collection.query({
        queryTexts: [''],
        where: { document_id },
        nResults: 1000
      });

      if (results.ids[0].length > 0) {
        // Delete from ChromaDB
        await this.collection.delete({
          ids: results.ids[0]
        });
      }

      // Delete from Redis cache
      await this.redisClient.del(`vector_doc:${document_id}`);

      // Delete from PostgreSQL
      await this.pgClient.query(
        'DELETE FROM indexed_documents WHERE document_id = $1',
        [document_id]
      );

      this.logger.info(`Deleted document ${document_id}`);

      return {
        success: true,
        message: 'Document deleted successfully',
        document_id
      };
    } catch (error) {
      this.logger.error('Error deleting document:', error);
      throw error;
    }
  }

  // Helper methods
  chunkDocument(content, chunkSize, overlap) {
    const chunks = [];
    const words = content.split(' ');
    
    for (let i = 0; i < words.length; i += chunkSize - overlap) {
      const chunk = words.slice(i, i + chunkSize).join(' ');
      if (chunk.trim()) {
        chunks.push(chunk);
      }
    }
    
    return chunks;
  }

  async generateEmbeddings(texts) {
    const embeddings = [];
    
    for (const text of texts) {
      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: text
      });
      embeddings.push(response.data[0].embedding);
    }
    
    return embeddings;
  }

  async generateQueryEmbedding(query) {
    const response = await this.openai.embeddings.create({
      model: this.embeddingModel,
      input: query
    });
    return response.data[0].embedding;
  }

  async getManifestoContext(category) {
    // Get core manifesto content for the category
    const manifestoResults = await this.collection.query({
      queryTexts: [category || 'core manifesto'],
      where: { document_type: 'manifesto' },
      nResults: 3
    });

    if (manifestoResults.documents[0].length > 0) {
      return manifestoResults.documents[0].join('\n\n');
    }
    
    return null;
  }

  async findMarkdownFiles(dirPath) {
    const files = [];
    
    const items = await fs.readdir(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        const subFiles = await this.findMarkdownFiles(itemPath);
        files.push(...subFiles);
      } else if (item.endsWith('.md')) {
        files.push(itemPath);
      }
    }
    
    return files;
  }

  extractCategoryFromPath(filePath) {
    const parts = filePath.split(path.sep);
    
    // Category mapping
    const categoryMap = {
      'healthcare': 'healthcare',
      'education': 'education',
      'economic': 'economic_policy',
      'housing': 'housing',
      'jobs': 'jobs_automation',
      'constitution': 'constitutional_amendments',
      'ethics': 'ethics_accountability',
      'repair': 'rights_repair_grow',
      'grow': 'rights_repair_grow',
      'funding': 'funding_revenue'
    };

    for (const part of parts) {
      for (const [key, value] of Object.entries(categoryMap)) {
        if (part.toLowerCase().includes(key)) {
          return value;
        }
      }
    }

    return 'general';
  }

  setupHealthEndpoint() {
    const app = express();
    app.use(helmet());
    app.use(cors());

    app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'vector-search-mcp',
        timestamp: new Date().toISOString(),
        collection: this.collection ? 'connected' : 'not connected'
      });
    });

    const port = process.env.MCP_SERVER_PORT || 8088;
    app.listen(port, () => {
      this.logger.info(`Vector Search MCP Server health endpoint running on port ${port}`);
    });
  }
}

// Initialize and start the server
const server = new VectorSearchServer();

async function start() {
  try {
    await server.initialize();
    await server.server.start();
    console.log('Vector Search MCP Server started successfully');
  } catch (error) {
    console.error('Failed to start Vector Search MCP Server:', error);
    process.exit(1);
  }
}

start();