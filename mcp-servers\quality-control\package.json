{"name": "quality-control-mcp-server", "version": "1.0.0", "description": "MCP server for quality control and CEO-level review of political documents", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "@anthropic-ai/sdk": "^0.17.0", "axios": "^1.6.0", "axios-retry": "^3.8.0", "circuit-breaker-js": "^1.2.0", "fs-extra": "^11.2.0", "natural": "^6.7.0", "openai": "^4.20.0", "path": "^0.12.7", "pg": "^8.11.3", "redis": "^4.6.10", "sentiment": "^5.0.2", "tiktoken": "^1.0.15", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}