#!/usr/bin/env node

import { MCPServer } from '@anthropic-ai/mcp-sdk';
import fs from 'fs-extra';
import path from 'path';
import { Client } from 'pg';
import { createClient } from 'redis';
import { encoding_for_model } from 'tiktoken';
import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import mammoth from 'mammoth';
import pdfParse from 'pdf-parse';
import { marked } from 'marked';
import axios from 'axios';
import { withCircuitBreaker, logStructuredError, getHealthCheck } from '../shared/error-handling.js';

/**
 * Political Content MCP Server
 * Handles document generation, editing, and processing for political content
 */

class PoliticalContentServer {
  constructor() {
    this.server = new MCPServer({
      name: 'political-content',
      version: '1.0.0'
    });
    
    this.tokenizer = encoding_for_model('gpt-4');
    
    // AI clients
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY
    });
    
    // Database connections
    this.pgClient = null;
    this.redisClient = null;
    
    this.outputPath = process.env.OUTPUT_PATH || '/app/output';
    
    // Vector search integration
    this.vectorSearchUrl = process.env.VECTOR_SEARCH_URL || 'http://mcp-vector-search:8088';
    this.manifestoContextUrl = process.env.MANIFESTO_CONTEXT_URL || 'http://mcp-manifesto-context:8080';
    
    this.setupTools();
    this.setupResources();
  }

  async initialize() {
    // Initialize PostgreSQL connection
    this.pgClient = new Client({
      host: process.env.POSTGRES_HOST || 'postgres',
      port: process.env.POSTGRES_PORT || 5432,
      database: process.env.POSTGRES_DB || 'political_conversations',
      user: process.env.POSTGRES_USER || 'n8n_user',
      password: process.env.POSTGRES_PASSWORD || 'n8n_secure_password'
    });

    // Initialize Redis connection
    this.redisClient = createClient({
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379
    });

    try {
      // Connect to PostgreSQL with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.pgClient.connect();
      });
      
      // Connect to Redis with circuit breaker protection
      await withCircuitBreaker('database', async () => {
        await this.redisClient.connect();
      });
      
      console.log('Political Content Server: Connected to databases');
      
      // Ensure output directory exists
      await fs.ensureDir(this.outputPath);
    } catch (error) {
      logStructuredError(error, 'PoliticalContentServer.initialize', {
        component: 'database_connection'
      });
      throw error;
    }
  }

  setupTools() {
    // Tool: Generate White Paper
    this.server.addTool({
      name: 'generate_white_paper',
      description: 'Generate a comprehensive white paper on political topic',
      inputSchema: {
        type: 'object',
        properties: {
          topic: {
            type: 'string',
            description: 'Main topic for the white paper'
          },
          category: {
            type: 'string',
            description: 'Policy category',
            enum: ['healthcare', 'education', 'economic_policy', 'housing', 'jobs_automation', 'constitutional_amendments', 'ethics_accountability', 'rights_repair_grow', 'funding_revenue']
          },
          token_tier: {
            type: 'integer',
            minimum: 1,
            maximum: 4,
            description: 'Token tier for context loading'
          },
          research_level: {
            type: 'string',
            enum: ['basic', 'comprehensive', 'expert'],
            default: 'comprehensive',
            description: 'Level of research integration'
          },
          output_format: {
            type: 'string',
            enum: ['markdown', 'docx', 'pdf'],
            default: 'markdown',
            description: 'Output format'
          }
        },
        required: ['topic', 'category', 'token_tier']
      }
    }, this.generateWhitePaper.bind(this));

    // Tool: Edit Document
    this.server.addTool({
      name: 'edit_document',
      description: 'Edit existing political document with specific instructions',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Document ID or file path'
          },
          edit_instructions: {
            type: 'string',
            description: 'Specific editing instructions'
          },
          preserve_voice: {
            type: 'boolean',
            default: true,
            description: 'Maintain Beau Lewis voice and style'
          },
          token_tier: {
            type: 'integer',
            minimum: 1,
            maximum: 4,
            default: 2
          }
        },
        required: ['document_id', 'edit_instructions']
      }
    }, this.editDocument.bind(this));

    // Tool: Combine Documents
    this.server.addTool({
      name: 'combine_documents',
      description: 'Combine multiple documents into unified political document',
      inputSchema: {
        type: 'object',
        properties: {
          document_ids: {
            type: 'array',
            items: { type: 'string' },
            description: 'List of document IDs to combine'
          },
          combination_strategy: {
            type: 'string',
            enum: ['chronological', 'thematic', 'priority', 'custom'],
            default: 'thematic',
            description: 'How to organize combined content'
          },
          output_title: {
            type: 'string',
            description: 'Title for combined document'
          },
          category: {
            type: 'string',
            description: 'Category for the combined document'
          }
        },
        required: ['document_ids', 'output_title']
      }
    }, this.combineDocuments.bind(this));

    // Tool: Generate Policy Brief
    this.server.addTool({
      name: 'generate_policy_brief',
      description: 'Generate concise policy brief for decision makers',
      inputSchema: {
        type: 'object',
        properties: {
          policy_topic: {
            type: 'string',
            description: 'Specific policy topic'
          },
          target_audience: {
            type: 'string',
            enum: ['legislators', 'advocates', 'media', 'public'],
            default: 'legislators',
            description: 'Primary audience for the brief'
          },
          urgency_level: {
            type: 'string',
            enum: ['routine', 'important', 'urgent', 'critical'],
            default: 'important'
          },
          length: {
            type: 'string',
            enum: ['short', 'medium', 'long'],
            default: 'medium',
            description: 'Brief length (1-2, 3-4, or 5-6 pages)'
          }
        },
        required: ['policy_topic']
      }
    }, this.generatePolicyBrief.bind(this));

    // Tool: Convert Document Format
    this.server.addTool({
      name: 'convert_document_format',
      description: 'Convert document between formats (MD, DOCX, PDF)',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Document ID or file path'
          },
          target_format: {
            type: 'string',
            enum: ['markdown', 'docx', 'pdf', 'html'],
            description: 'Target format for conversion'
          },
          styling_options: {
            type: 'object',
            properties: {
              professional_layout: { type: 'boolean', default: true },
              include_toc: { type: 'boolean', default: true },
              header_footer: { type: 'boolean', default: true }
            }
          }
        },
        required: ['document_id', 'target_format']
      }
    }, this.convertDocumentFormat.bind(this));

    // Tool: Analyze Document Quality
    this.server.addTool({
      name: 'analyze_document_quality',
      description: 'Analyze document for manifesto alignment, voice consistency, and quality',
      inputSchema: {
        type: 'object',
        properties: {
          document_id: {
            type: 'string',
            description: 'Document ID or content to analyze'
          },
          analysis_type: {
            type: 'string',
            enum: ['full', 'voice_only', 'manifesto_only', 'factual_only'],
            default: 'full'
          }
        },
        required: ['document_id']
      }
    }, this.analyzeDocumentQuality.bind(this));
  }

  setupResources() {
    // Resource: Generated Documents
    this.server.addResource({
      uri: 'content://generated_documents',
      name: 'Generated Political Documents',
      description: 'List of generated political documents',
      mimeType: 'application/json'
    });

    this.server.addResource({
      uri: 'content://document_templates',
      name: 'Document Templates',
      description: 'Political document templates and structures',
      mimeType: 'application/json'
    });
  }

  async generateWhitePaper({ topic, category, token_tier, research_level = 'comprehensive', output_format = 'markdown' }) {
    try {
      const jobId = this.generateJobId();
      
      // Start processing job
      await this.createProcessingJob(jobId, {
        task_type: 'generate_whitepaper',
        filename: `${topic.replace(/\s+/g, '_').toLowerCase()}_whitepaper.${output_format}`,
        manifesto_category: category,
        token_tier,
        processing_metadata: { topic, research_level, output_format }
      });

      // Get enhanced context using vector search
      const enhancedContext = await this.getEnhancedContext(topic, category, token_tier);
      
      // Get manifesto context for the tier
      const manifestoContext = await this.getManifestoContext(token_tier, category);
      
      // Generate the white paper content
      const prompt = this.buildWhitePaperPrompt(topic, category, research_level, manifestoContext, enhancedContext);
      
      const content = await this.generateWithAI(prompt, token_tier);
      
      // Save the document
      const filename = `${topic.replace(/\s+/g, '_').toLowerCase()}_whitepaper.${output_format}`;
      const filePath = path.join(this.outputPath, filename);
      
      if (output_format === 'markdown') {
        await fs.writeFile(filePath, content, 'utf8');
      } else {
        // Convert to other formats as needed
        await this.convertAndSave(content, filePath, output_format);
      }

      // Update processing job
      await this.completeProcessingJob(jobId, {
        output_filename: filename,
        output_path: filePath,
        tokens_used: this.tokenizer.encode(content).length
      });

      return {
        jobId,
        filename,
        filePath,
        contentPreview: content.substring(0, 500) + '...',
        tokens: this.tokenizer.encode(content).length,
        category,
        topic
      };

    } catch (error) {
      console.error('Error generating white paper:', error);
      throw error;
    }
  }

  async editDocument({ document_id, edit_instructions, preserve_voice = true, token_tier = 2 }) {
    try {
      const jobId = this.generateJobId();
      
      // Load existing document
      const document = await this.loadDocument(document_id);
      if (!document) {
        throw new Error(`Document not found: ${document_id}`);
      }

      await this.createProcessingJob(jobId, {
        task_type: 'edit',
        filename: document.filename,
        file_path: document.path,
        token_tier,
        processing_metadata: { edit_instructions, preserve_voice }
      });

      // Get context if preserving voice
      let context = '';
      if (preserve_voice) {
        const manifestoContext = await this.getManifestoContext(token_tier);
        context = manifestoContext.context;
      }

      // Build edit prompt
      const prompt = this.buildEditPrompt(document.content, edit_instructions, context, preserve_voice);
      
      const editedContent = await this.generateWithAI(prompt, token_tier);
      
      // Save edited document
      const filename = `${document.name}_edited.md`;
      const filePath = path.join(this.outputPath, filename);
      await fs.writeFile(filePath, editedContent, 'utf8');

      await this.completeProcessingJob(jobId, {
        output_filename: filename,
        output_path: filePath,
        tokens_used: this.tokenizer.encode(editedContent).length
      });

      return {
        jobId,
        filename,
        filePath,
        originalTokens: this.tokenizer.encode(document.content).length,
        editedTokens: this.tokenizer.encode(editedContent).length,
        changesSummary: await this.summarizeChanges(document.content, editedContent)
      };

    } catch (error) {
      console.error('Error editing document:', error);
      throw error;
    }
  }

  async combineDocuments({ document_ids, combination_strategy = 'thematic', output_title, category }) {
    try {
      const jobId = this.generateJobId();
      
      await this.createProcessingJob(jobId, {
        task_type: 'combine',
        filename: `${output_title.replace(/\s+/g, '_').toLowerCase()}_combined.md`,
        manifesto_category: category,
        processing_metadata: { document_ids, combination_strategy, output_title }
      });

      // Load all documents
      const documents = [];
      for (const docId of document_ids) {
        const doc = await this.loadDocument(docId);
        if (doc) documents.push(doc);
      }

      if (documents.length === 0) {
        throw new Error('No valid documents found to combine');
      }

      // Combine based on strategy
      const combinedContent = await this.combineDocumentsWithStrategy(documents, combination_strategy, output_title);
      
      const filename = `${output_title.replace(/\s+/g, '_').toLowerCase()}_combined.md`;
      const filePath = path.join(this.outputPath, filename);
      await fs.writeFile(filePath, combinedContent, 'utf8');

      await this.completeProcessingJob(jobId, {
        output_filename: filename,
        output_path: filePath,
        tokens_used: this.tokenizer.encode(combinedContent).length
      });

      return {
        jobId,
        filename,
        filePath,
        documentsCount: documents.length,
        totalTokens: this.tokenizer.encode(combinedContent).length,
        strategy: combination_strategy
      };

    } catch (error) {
      console.error('Error combining documents:', error);
      throw error;
    }
  }

  async generatePolicyBrief({ policy_topic, target_audience = 'legislators', urgency_level = 'important', length = 'medium' }) {
    try {
      const jobId = this.generateJobId();
      
      await this.createProcessingJob(jobId, {
        task_type: 'generate_policy_brief',
        filename: `${policy_topic.replace(/\s+/g, '_').toLowerCase()}_brief.md`,
        processing_metadata: { policy_topic, target_audience, urgency_level, length }
      });

      // Get appropriate context (tier 2 for policy briefs)
      const manifestoContext = await this.getManifestoContext(2);
      
      const prompt = this.buildPolicyBriefPrompt(policy_topic, target_audience, urgency_level, length, manifestoContext);
      
      const content = await this.generateWithAI(prompt, 2);
      
      const filename = `${policy_topic.replace(/\s+/g, '_').toLowerCase()}_brief.md`;
      const filePath = path.join(this.outputPath, filename);
      await fs.writeFile(filePath, content, 'utf8');

      await this.completeProcessingJob(jobId, {
        output_filename: filename,
        output_path: filePath,
        tokens_used: this.tokenizer.encode(content).length
      });

      return {
        jobId,
        filename,
        filePath,
        topic: policy_topic,
        audience: target_audience,
        tokens: this.tokenizer.encode(content).length
      };

    } catch (error) {
      console.error('Error generating policy brief:', error);
      throw error;
    }
  }

  async convertDocumentFormat({ document_id, target_format, styling_options = {} }) {
    try {
      const document = await this.loadDocument(document_id);
      if (!document) {
        throw new Error(`Document not found: ${document_id}`);
      }

      const outputFilename = `${document.name}.${target_format}`;
      const outputPath = path.join(this.outputPath, outputFilename);

      // Perform conversion based on target format
      await this.convertAndSave(document.content, outputPath, target_format, styling_options);

      return {
        originalFormat: document.format || 'markdown',
        targetFormat: target_format,
        outputPath,
        filename: outputFilename
      };

    } catch (error) {
      console.error('Error converting document format:', error);
      throw error;
    }
  }

  async analyzeDocumentQuality({ document_id, analysis_type = 'full' }) {
    try {
      const document = await this.loadDocument(document_id);
      if (!document) {
        throw new Error(`Document not found: ${document_id}`);
      }

      const manifestoContext = await this.getManifestoContext(2);
      
      const analysisPrompt = this.buildAnalysisPrompt(document.content, analysis_type, manifestoContext);
      
      const analysis = await this.generateWithAI(analysisPrompt, 2);
      
      // Parse analysis results
      const scores = this.parseAnalysisScores(analysis);
      
      // Store quality review
      await this.storeQualityReview(document_id, 'political-content-server', scores, analysis);

      return {
        document_id,
        analysis_type,
        scores,
        analysis,
        recommendations: this.extractRecommendations(analysis)
      };

    } catch (error) {
      console.error('Error analyzing document quality:', error);
      throw error;
    }
  }

  // Helper methods

  async getManifestoContext(tier, category = null) {
    // This would call the manifesto-context MCP server
    // For now, return basic context structure
    return {
      context: 'Core manifesto context would be loaded here',
      totalTokens: 2000,
      tier,
      category
    };
  }

  async generateWithAI(prompt, tier) {
    const tokenLimits = { 1: 5000, 2: 10000, 3: 25000, 4: 50000 };
    const maxTokens = Math.min(4000, Math.floor(tokenLimits[tier] * 0.8));

    try {
      // Use Claude for higher quality political content with circuit breaker protection
      const response = await withCircuitBreaker('anthropic', async () => {
        return await this.anthropic.messages.create({
          model: 'claude-3-sonnet-20240229',
          max_tokens: maxTokens,
          messages: [{ role: 'user', content: prompt }]
        });
      }, {
        enableFallback: true,
        fallbackFunction: async (error) => {
          logStructuredError(error, 'PoliticalContentServer.generateWithAI.fallback', {
            component: 'ai_generation_fallback',
            tier: tier,
            model: 'claude-3-sonnet-20240229'
          });
          
          // Fallback to OpenAI if Anthropic fails
          try {
            const fallbackResponse = await withCircuitBreaker('openai', async () => {
              return await this.openai.chat.completions.create({
                model: 'gpt-4',
                max_tokens: maxTokens,
                messages: [{ role: 'user', content: prompt }]
              });
            });
            
            return {
              content: [{ text: fallbackResponse.choices[0].message.content }]
            };
          } catch (fallbackError) {
            logStructuredError(fallbackError, 'PoliticalContentServer.generateWithAI.fallback_failed', {
              component: 'ai_generation_fallback_failed',
              tier: tier
            });
            throw new Error('Both primary and fallback AI services are unavailable');
          }
        }
      });

      return response.content[0].text;
    } catch (error) {
      logStructuredError(error, 'PoliticalContentServer.generateWithAI', {
        component: 'ai_generation',
        tier: tier,
        prompt_length: prompt.length
      });
      throw error;
    }
  }

  buildWhitePaperPrompt(topic, category, researchLevel, manifestoContext, enhancedContext = null) {
    let prompt = `${manifestoContext.context}

---

${enhancedContext && enhancedContext.context ? `
## Relevant Political Documents and Context

${enhancedContext.context}

### Context Metadata
- Documents analyzed: ${enhancedContext.metadata.documents_included || 0}
- Token count: ${enhancedContext.metadata.token_count || 0}
- Category focus: ${enhancedContext.metadata.category || 'general'}

---

` : ''}

Generate a comprehensive white paper on "${topic}" in the ${category} policy area.

Research level: ${researchLevel}

The white paper should:
1. Follow Beau Lewis's voice and political vision exactly
2. Include executive summary, problem analysis, solution framework, implementation plan, and conclusion
3. Use evidence-based arguments with international examples
4. Maintain passionate but professional tone
5. Connect to the American Social Trust Fund where relevant
6. Include specific policy recommendations
7. Target 5,000-8,000 words
${enhancedContext && enhancedContext.context ? '8. Incorporate insights from the relevant political documents provided above' : ''}

Structure:
- Executive Summary
- The Challenge We Face
- Our Vision for Change
- Policy Framework
- Implementation Strategy
- Expected Outcomes
- Call to Action

Focus on economic justice, working families, and democratic renewal.${enhancedContext && enhancedContext.context ? ' Use the provided context to ensure consistency with existing political positions and to avoid redundancy.' : ''}`;

    return prompt;
  }

  buildEditPrompt(originalContent, editInstructions, context, preserveVoice) {
    let prompt = '';
    
    if (preserveVoice && context) {
      prompt += `${context}\n\n---\n\n`;
    }
    
    prompt += `Edit the following document according to these instructions: ${editInstructions}

${preserveVoice ? 'IMPORTANT: Maintain Beau Lewis\'s voice, style, and political vision throughout all edits.' : ''}

Original Document:
${originalContent}

Provide the complete edited document:`;

    return prompt;
  }

  buildPolicyBriefPrompt(topic, audience, urgency, length, manifestoContext) {
    const lengthGuidelines = {
      short: '1-2 pages, bullet points, key facts only',
      medium: '3-4 pages, detailed analysis with executive summary',
      long: '5-6 pages, comprehensive analysis with appendices'
    };

    return `${manifestoContext.context}

---

Generate a ${length} policy brief on "${topic}" for ${audience}.

Urgency level: ${urgency}
Length: ${lengthGuidelines[length]}

Structure for ${audience}:
- Executive Summary
- Issue Background
- Policy Recommendations
- Implementation Steps
- Benefits and Impact
- Next Actions

Use Beau Lewis's voice and focus on practical solutions that advance economic justice and democratic renewal.`;
  }

  buildAnalysisPrompt(content, analysisType, manifestoContext) {
    return `${manifestoContext.context}

---

Analyze the following document for:
${analysisType === 'full' ? '- Manifesto alignment (1-10 score)\n- Voice consistency (1-10 score)\n- Factual accuracy (1-10 score)\n- Overall quality (1-10 score)' : `- ${analysisType.replace('_only', '')} (1-10 score)`}

Document to analyze:
${content}

Provide scores and detailed feedback on how well this document embodies Beau Lewis's political vision and voice.`;
  }

  generateJobId() {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async createProcessingJob(jobId, jobData) {
    try {
      await this.pgClient.query(`
        INSERT INTO document_processing_jobs (
          job_id, filename, file_path, document_type, processing_status, task_type,
          manifesto_category, token_tier, processing_start, processing_metadata
        ) VALUES ($1, $2, $3, 'content', 'processing', $4, $5, $6, CURRENT_TIMESTAMP, $7)
      `, [
        jobId, 
        jobData.filename || '', 
        jobData.file_path || '', 
        jobData.task_type,
        jobData.manifesto_category || null,
        jobData.token_tier || null,
        JSON.stringify(jobData.processing_metadata || {})
      ]);
    } catch (error) {
      console.error('Error creating processing job:', error);
    }
  }

  async completeProcessingJob(jobId, completionData) {
    try {
      await this.pgClient.query(`
        UPDATE document_processing_jobs 
        SET processing_status = 'completed', processing_end = CURRENT_TIMESTAMP,
            output_filename = $2, output_path = $3, tokens_used = $4
        WHERE job_id = $1
      `, [jobId, completionData.output_filename, completionData.output_path, completionData.tokens_used]);
    } catch (error) {
      console.error('Error completing processing job:', error);
    }
  }

  async loadDocument(documentId) {
    // Implementation for loading documents from various sources
    // This would handle file paths, database IDs, etc.
    return null; // Placeholder
  }

  async convertAndSave(content, outputPath, format, options = {}) {
    // Implementation for format conversion
    // Would handle markdown -> docx, pdf, etc.
    await fs.writeFile(outputPath, content, 'utf8');
  }

  async combineDocumentsWithStrategy(documents, strategy, title) {
    // Implementation for combining documents with different strategies
    return `# ${title}\n\n` + documents.map(doc => doc.content).join('\n\n---\n\n');
  }

  async summarizeChanges(original, edited) {
    // Implementation for summarizing document changes
    return 'Document edited successfully';
  }

  parseAnalysisScores(analysis) {
    // Implementation for parsing analysis scores from AI response
    return {
      manifesto_alignment_score: 8.5,
      voice_consistency_score: 9.0,
      factual_accuracy_score: 8.0,
      overall_quality_score: 8.5
    };
  }

  extractRecommendations(analysis) {
    // Implementation for extracting recommendations from analysis
    return ['Maintain strong voice consistency', 'Add more specific policy details'];
  }

  async storeQualityReview(jobId, reviewer, scores, comments) {
    try {
      await this.pgClient.query(`
        INSERT INTO quality_control_reviews (
          job_id, reviewer_agent, manifesto_alignment_score, voice_consistency_score,
          factual_accuracy_score, overall_quality_score, review_comments, approved
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        jobId, reviewer, scores.manifesto_alignment_score, scores.voice_consistency_score,
        scores.factual_accuracy_score, scores.overall_quality_score, comments,
        scores.overall_quality_score >= 7.0
      ]);
    } catch (error) {
      console.error('Error storing quality review:', error);
    }
  }

  async getEnhancedContext(topic, category, token_tier) {
    try {
      // Calculate token limit based on tier
      const tokenLimits = {
        1: 3000,  // 5K tier - reserve 2K for manifesto
        2: 6000,  // 10K tier - reserve 4K for manifesto  
        3: 15000, // 25K tier - reserve 10K for manifesto
        4: 30000  // 50K tier - reserve 20K for manifesto
      };
      
      const tokenLimit = tokenLimits[token_tier] || 6000;
      
      // Call vector search service to get relevant context
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'get_document_context',
        parameters: {
          topic,
          category,
          token_limit: tokenLimit,
          include_manifesto: false // We'll get manifesto separately
        }
      });

      if (response.data.success) {
        return {
          context: response.data.context,
          metadata: response.data.metadata
        };
      } else {
        console.warn('Vector search failed, falling back to basic context');
        return { context: '', metadata: {} };
      }
    } catch (error) {
      console.error('Error getting enhanced context:', error);
      return { context: '', metadata: {} };
    }
  }

  async getSimilarDocuments(topic, category, limit = 3) {
    try {
      const response = await axios.post(`${this.vectorSearchUrl}/mcp/call`, {
        tool: 'search_similar_content',
        parameters: {
          query: topic,
          category,
          limit,
          minimum_similarity: 0.7
        }
      });

      if (response.data.success) {
        return response.data.results;
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error getting similar documents:', error);
      return [];
    }
  }

  async start() {
    await this.initialize();
    await this.server.start();
    console.log('Political Content MCP Server started');
  }

  async stop() {
    if (this.pgClient) await this.pgClient.end();
    if (this.redisClient) await this.redisClient.quit();
    await this.server.stop();
  }
}

// Start the server
const server = new PoliticalContentServer();

process.on('SIGINT', async () => {
  console.log('Shutting down Political Content Server...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down Political Content Server...');
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);