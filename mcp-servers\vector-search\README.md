# Vector Search MCP Server

ChromaDB-powered vector search for intelligent political document context retrieval.

## Features

- **Document Indexing**: Index political documents and manifestos with vector embeddings
- **Semantic Search**: Find similar content using vector similarity search
- **Context Retrieval**: Get relevant context for document generation with token limits
- **Category Filtering**: Filter searches by political category
- **Manifesto Integration**: Deep integration with Beau Lewis manifesto content
- **Caching**: Redis caching for performance optimization
- **Tracking**: PostgreSQL tracking of indexed documents

## API Endpoints

### Health Check
- `GET /health` - Server health status

## MCP Tools

### index_document
Index a political document for vector search.

**Parameters:**
- `document_id` (string, required) - Unique identifier for the document
- `content` (string, required) - Full text content of the document
- `metadata` (object, optional) - Document metadata (title, category, etc.)

### search_similar_content
Find similar political documents using vector search.

**Parameters:**
- `query` (string, required) - Search query for finding similar content
- `category` (string, optional) - Filter by document category
- `limit` (number, default: 5) - Maximum number of results to return
- `minimum_similarity` (number, default: 0.7) - Minimum similarity score threshold

### get_document_context
Get relevant context for document generation using vector search.

**Parameters:**
- `topic` (string, required) - Topic or theme for context retrieval
- `category` (string, optional) - Document category for focused search
- `token_limit` (number, default: 10000) - Maximum tokens for context
- `include_manifesto` (boolean, default: true) - Include core manifesto content

### index_manifesto_documents
Index all manifesto and political documents from the filesystem.

**Parameters:**
- `manifesto_path` (string, default: '/app/manifesto') - Path to manifesto documents
- `white_papers_path` (string, default: '/app/white_papers_markdown') - Path to white papers
- `force_reindex` (boolean, default: false) - Force reindexing of all documents

### get_collection_stats
Get statistics about the indexed document collection.

### update_document
Update an existing document in the vector store.

**Parameters:**
- `document_id` (string, required) - Unique identifier for the document to update
- `content` (string, optional) - Updated content of the document
- `metadata` (object, optional) - Updated metadata for the document

### delete_document
Remove a document from the vector store.

**Parameters:**
- `document_id` (string, required) - Unique identifier for the document to delete

## Configuration

### Environment Variables

- `CHROMADB_URL` - ChromaDB server URL (default: http://chromadb:8000)
- `OPENAI_API_KEY` - OpenAI API key for embeddings
- `POSTGRES_HOST` - PostgreSQL host
- `POSTGRES_PORT` - PostgreSQL port
- `POSTGRES_DB` - PostgreSQL database name
- `POSTGRES_USER` - PostgreSQL username
- `POSTGRES_PASSWORD` - PostgreSQL password
- `REDIS_HOST` - Redis host
- `REDIS_PORT` - Redis port
- `REDIS_PASSWORD` - Redis password
- `MCP_SERVER_PORT` - Server port (default: 8088)

### Database Schema

The server requires a PostgreSQL table for tracking indexed documents:

```sql
CREATE TABLE indexed_documents (
    document_id VARCHAR PRIMARY KEY,
    title VARCHAR,
    category VARCHAR,
    chunk_count INTEGER,
    total_tokens INTEGER,
    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Docker Usage

```bash
# Build the image
docker build -t vector-search-mcp .

# Run the container
docker run -d \
  --name vector-search-mcp \
  -p 8088:8088 \
  -e CHROMADB_URL=http://chromadb:8000 \
  -e OPENAI_API_KEY=your-api-key \
  vector-search-mcp
```

## Integration with n8n

The Vector Search MCP Server integrates with n8n workflows to provide intelligent context retrieval for document generation:

1. **Context Retrieval**: Use `get_document_context` tool to get relevant content for document generation
2. **Similarity Search**: Use `search_similar_content` to find related documents
3. **Indexing**: Use `index_document` to add new documents to the vector store

## Performance

- **Embedding Model**: Uses OpenAI's text-embedding-3-small for fast, cost-effective embeddings
- **Chunking**: Documents are chunked into 1000-word segments with 200-word overlap
- **Caching**: Redis caching for frequently accessed documents
- **Similarity Threshold**: Configurable minimum similarity for search results

## Monitoring

The server provides comprehensive logging and health checks:
- Health endpoint at `/health`
- Structured logging with Winston
- Performance metrics and error tracking
- Database connection monitoring