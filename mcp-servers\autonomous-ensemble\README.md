# Autonomous Model Ensemble MCP Server

A comprehensive Model Context Protocol (MCP) server for political document processing that integrates autonomous AI orchestration patterns with intelligent model routing, memory management, and error handling.

## Overview

This MCP server implements an autonomous model ensemble system specifically designed for political document processing. It incorporates patterns from the autonomous AI development ecosystem to provide:

- **Multi-Agent Orchestration**: Intelligent task distribution across specialized agents
- **Model Ensemble Routing**: Dynamic routing to optimal AI models based on task requirements
- **Advanced Memory Management**: Hierarchical caching with Redis and SQLite
- **Comprehensive Error Handling**: Circuit breakers, retries, and fallback mechanisms
- **Context Management**: Multi-turn conversation support with context compression

## Architecture

The system follows a 5-layer architecture:

```
┌─────────────────────────────────────────────────────────────────┐
│            Political Document Processing System                 │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               MCP Interface Layer                           │ │
│  │  Document Ingestion │ Query Processing │ Result Delivery    │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                ↓                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Orchestration Layer                            │ │
│  │  Master Orchestrator │ Model Router │ Task Scheduler        │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                ↓                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               Model Ensemble Layer                          │ │
│  │  Research Agent │ Analysis Agent │ Synthesis Agent         │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                ↓                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Processing Layer                               │ │
│  │  Vector Search │ Document Intelligence │ Content Analysis   │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                ↓                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Infrastructure Layer                           │ │
│  │  ChromaDB │ Memory Management │ Security │ Monitoring       │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Components

### 1. Political Document Orchestrator (`political_document_orchestrator.py`)

- **Multi-Agent Coordination**: Manages specialized agents for different document processing tasks
- **Task Scheduling**: Intelligent task distribution with dependency management
- **Resource Management**: Load balancing and performance monitoring
- **Event-Driven Architecture**: Asynchronous event processing for system coordination

### 2. Model Ensemble Router (`model_ensemble_router.py`)

- **Intelligent Routing**: Multiple routing strategies (capability-based, cost-optimized, performance-based)
- **Model Management**: Dynamic model selection and load balancing
- **Performance Tracking**: Real-time analytics and optimization recommendations
- **Fallback Mechanisms**: Automatic fallback to alternative models

### 3. Context Memory Manager (`context_memory_manager.py`)

- **Hierarchical Caching**: L1 (Redis) and L2 (SQLite) caching with in-memory optimization
- **Context Windows**: Multi-turn conversation support with automatic compression
- **Memory Optimization**: LRU eviction, access pattern analysis, and memory pressure handling
- **Event-Driven Cleanup**: Automatic cleanup of expired and unused entries

### 4. Error Handling System (`error_handling_system.py`)

- **Circuit Breakers**: Automatic failure detection and recovery
- **Retry Policies**: Exponential backoff with jitter
- **Fallback Strategies**: Multiple fallback mechanisms for different error types
- **Monitoring & Alerting**: Real-time error tracking and alert generation

### 5. MCP Server Integration (`autonomous_ensemble_mcp_server.py`)

- **Tool Integration**: Comprehensive MCP tool interface
- **Resource Management**: System status and analytics resources
- **Event Coordination**: Cross-component event handling
- **Health Monitoring**: Overall system health scoring

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd autonomous-ensemble-mcp-server
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Set up Redis** (required for caching):
```bash
# Install Redis
sudo apt-get install redis-server  # Ubuntu/Debian
brew install redis                 # macOS

# Start Redis
redis-server
```

4. **Configure environment variables**:
```bash
# Create .env file
cp .env.example .env

# Edit .env with your configuration
REDIS_HOST=localhost
REDIS_PORT=6379
DATABASE_PATH=context_memory.db
LOG_LEVEL=INFO
```

## Usage

### Starting the Server

```bash
# Start the MCP server
python src/autonomous_ensemble_mcp_server.py
```

### MCP Tools

The server provides the following tools:

#### `submit_analysis_task`
Submit a political document analysis task.

```json
{
  "task_type": "document_analysis",
  "content": "Political document content to analyze...",
  "priority": "high",
  "context": {
    "source": "political_party_a",
    "date": "2024-01-15"
  },
  "routing_strategy": "capability_based"
}
```

#### `get_task_status`
Check the status of a submitted task.

```json
{
  "task_id": "task_123_1640995200"
}
```

#### `list_active_tasks`
List all active tasks with optional filtering.

```json
{
  "status_filter": "running"
}
```

#### `get_system_status`
Get comprehensive system health and status.

```json
{}
```

#### `get_model_analytics`
Get model routing analytics and performance data.

```json
{}
```

#### `get_memory_stats`
Get memory usage statistics.

```json
{}
```

#### `get_error_stats`
Get error statistics and recovery recommendations.

```json
{}
```

#### `create_context_session`
Create a new context session for multi-turn conversations.

```json
{
  "session_id": "session_001",
  "task_type": "document_analysis",
  "context_data": {
    "user": "analyst",
    "preferences": {}
  }
}
```

#### `optimize_system`
Analyze and optimize system performance.

```json
{
  "optimization_type": "routing"
}
```

### MCP Resources

The server provides the following resources:

- `autonomous-ensemble://system/status` - Current system status and health metrics
- `autonomous-ensemble://analytics/routing` - Model routing analytics
- `autonomous-ensemble://analytics/memory` - Memory usage statistics
- `autonomous-ensemble://analytics/errors` - Error statistics and diagnostics
- `autonomous-ensemble://config/orchestrator` - Orchestrator configuration

## Configuration

### Environment Variables

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Database Configuration
DATABASE_PATH=context_memory.db

# Logging Configuration
LOG_LEVEL=INFO

# System Configuration
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=300
MEMORY_LIMIT_MB=1024
```

### Model Configuration

Models are configured in the `ModelEnsembleRouter` initialization. You can add new models by extending the `model_configs` list:

```python
{
    "model_id": "new_model",
    "model_type": ModelType.SPECIALIZED,
    "strengths": ["specialized_task"],
    "weaknesses": ["general_tasks"],
    "max_tokens": 50000,
    "cost_per_token": 0.005,
    "latency_ms": 700,
    "accuracy_score": 0.90,
    "specializations": ["political_analysis"],
    "max_concurrent": 8
}
```

### Agent Configuration

Agents are configured in the `PoliticalDocumentOrchestrator` initialization:

```python
{
    "agent_id": "new_agent_01",
    "agent_type": AgentType.RESEARCH,
    "capabilities": ["web_search", "fact_checking", "source_verification"]
}
```

## Monitoring and Analytics

The system provides comprehensive monitoring through:

### Health Monitoring
- Overall system health score
- Component status tracking
- Resource utilization monitoring

### Performance Analytics
- Model routing performance
- Task completion rates
- Memory usage patterns
- Error rates and patterns

### Error Tracking
- Real-time error logging
- Circuit breaker status
- Recovery recommendations
- Alert generation

## Development

### Running Tests

```bash
# Run all tests
pytest tests/

# Run with coverage
pytest --cov=src tests/

# Run specific test file
pytest tests/test_orchestrator.py
```

### Code Quality

```bash
# Format code
black src/

# Lint code
flake8 src/

# Type checking
mypy src/
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Integration Examples

### With n8n Workflow

```javascript
// n8n HTTP Request node configuration
{
  "method": "POST",
  "url": "mcp://autonomous-ensemble/submit_analysis_task",
  "body": {
    "task_type": "document_analysis",
    "content": "{{ $json.document_content }}",
    "priority": "high",
    "context": {
      "workflow_id": "{{ $workflow.id }}",
      "execution_id": "{{ $execution.id }}"
    }
  }
}
```

### With Python Client

```python
import asyncio
from mcp.client import ClientSession

async def analyze_document(content: str):
    async with ClientSession("autonomous-ensemble") as session:
        result = await session.call_tool(
            "submit_analysis_task",
            {
                "task_type": "document_analysis",
                "content": content,
                "priority": "high"
            }
        )
        return result
```

## Security Considerations

- All API endpoints require authentication
- Input validation on all tools
- Rate limiting on resource-intensive operations
- Secure storage of sensitive data
- Audit logging for all operations

## Performance Optimization

### Memory Management
- Hierarchical caching strategy
- Automatic memory pressure handling
- LRU eviction policies
- Context compression for large conversations

### Model Routing
- Intelligent model selection
- Load balancing across models
- Performance-based routing
- Automatic fallback mechanisms

### Error Handling
- Circuit breakers for fault tolerance
- Exponential backoff retry policies
- Graceful degradation strategies
- Comprehensive monitoring

## Troubleshooting

### Common Issues

1. **Redis Connection Issues**
   - Ensure Redis is running
   - Check Redis configuration
   - Verify network connectivity

2. **High Memory Usage**
   - Check memory statistics
   - Trigger memory cleanup
   - Adjust memory limits

3. **Model Routing Failures**
   - Check model availability
   - Verify API credentials
   - Review circuit breaker status

4. **Task Processing Delays**
   - Check agent availability
   - Review task queue status
   - Monitor resource utilization

### Debugging

Enable debug logging:
```bash
LOG_LEVEL=DEBUG python src/autonomous_ensemble_mcp_server.py
```

Check system status:
```bash
curl -X POST mcp://autonomous-ensemble/get_system_status
```

## License

MIT License - see LICENSE file for details.

## Support

For support, please open an issue in the GitHub repository or contact the development team.