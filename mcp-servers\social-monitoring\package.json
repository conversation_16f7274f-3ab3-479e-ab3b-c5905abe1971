{"name": "@political-system/social-monitoring-mcp", "version": "1.0.0", "description": "Social Monitoring MCP Server for comprehensive social media analysis, sentiment tracking, and influence network analysis", "main": "server.js", "type": "module", "author": "<PERSON>", "license": "MIT", "keywords": ["mcp", "social-monitoring", "sentiment-analysis", "social-media", "public-opinion", "trend-detection", "demographic-analysis", "influence-networks", "political-analysis", "opinion-leaders"], "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "security-audit": "npm audit && snyk test", "trend-refresh": "node scripts/refresh-trends.js"}, "dependencies": {"@anthropic-ai/mcp-sdk": "^1.0.0", "@anthropic-ai/sdk": "^0.17.0", "openai": "^4.20.0", "express": "^4.19.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0", "joi": "^17.11.0", "uuid": "^9.0.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-validator": "^7.0.1", "express-slow-down": "^2.0.1", "redis": "^4.6.12", "pg": "^8.11.3", "node-cron": "^3.0.3", "crypto": "^1.0.1", "morgan": "^1.10.0", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "axios": "^1.6.2", "natural": "^6.8.0", "sentiment": "^5.0.2", "compromise": "^14.13.0", "node-nlp": "^4.27.0", "stopword": "^2.0.8", "twitter-api-v2": "^1.15.2", "youtube-transcript": "^1.0.6", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.6.1", "simple-statistics": "^7.8.3", "regression": "^2.0.1", "d3-scale": "^4.0.2", "mathjs": "^12.2.1", "tiktoken": "^1.0.15", "fs-extra": "^11.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "snyk": "^1.1266.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/beau-lewis/political-document-system"}, "bugs": {"url": "https://github.com/beau-lewis/political-document-system/issues"}, "homepage": "https://github.com/beau-lewis/political-document-system#readme", "config": {"social_platforms": {"twitter": "Twitter/X Social Media Platform", "facebook": "Facebook Social Network", "instagram": "Instagram Photo/Video Platform", "tiktok": "TikTok Short Video Platform", "youtube": "YouTube Video Platform", "reddit": "Reddit Discussion Forums", "linkedin": "LinkedIn Professional Network"}}, "funding": {"type": "individual", "url": "https://github.com/sponsors/beau-lewis"}}