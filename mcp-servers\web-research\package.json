{"name": "web-research-mcp-server", "version": "1.0.0", "description": "Comprehensive web research MCP server with scraping, search, and RAG integration", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "health": "node -e \"console.log('Web Research MCP Server Health Check')\""}, "dependencies": {"@anthropic-ai/mcp-sdk": "^0.4.0", "chromadb": "^1.8.1", "openai": "^4.28.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12", "puppeteer": "^21.5.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "pg": "^8.11.3", "redis": "^4.6.10", "fs-extra": "^11.2.0", "uuid": "^9.0.1", "tiktoken": "^1.0.10", "winston": "^3.11.0", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "readability": "^0.4.4", "node-html-parser": "^6.1.12", "url-parse": "^1.5.10", "robots-parser": "^3.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "web-research", "scraping", "search", "rag", "chromadb", "political", "ai"], "author": "Political Document Processing System", "license": "MIT"}