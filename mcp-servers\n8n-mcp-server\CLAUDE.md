# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Status

This appears to be a new/empty repository for an n8n MCP (Model Context Protocol) server project.

## Getting Started

When this project is initialized, common commands to look for:
- `npm install` - Install dependencies
- `npm run build` - Build the project
- `npm run dev` - Run in development mode
- `npm run test` - Run tests
- `npm run lint` - Run linting

## Architecture Notes

This will be populated once the project structure is established. Key areas to document:
- MCP server implementation details
- n8n integration patterns
- Configuration management
- API endpoints and protocols