{"name": "mcp-multimodal-chromadb", "version": "1.0.0", "description": "Multi-modal ChromaDB MCP Server with image, video, and voice processing", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint ."}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "express": "^4.18.3", "helmet": "^7.1.0", "cors": "^2.8.5", "chromadb": "^1.8.1", "openai": "^4.28.0", "winston": "^3.11.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.2", "jimp": "^0.22.12", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.2", "speech-to-text": "^3.0.0", "node-whisper": "^1.3.0", "audio-buffer": "^4.0.3", "audio-decode": "^2.1.3", "wav-decoder": "^1.3.0", "form-data": "^4.0.0", "image-size": "^1.1.1", "mime": "^4.0.1", "uuid": "^9.0.1", "prom-client": "^15.1.0", "express-prometheus-middleware": "^1.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.56.0", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["mcp", "chromadb", "multimodal", "image-processing", "video-processing", "voice-processing", "vector-search"], "author": "Political Document Processing System", "license": "MIT"}