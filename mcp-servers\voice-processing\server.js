const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const winston = require('winston');
const multer = require('multer');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const session = require('express-session');
const RedisStore = require('connect-redis').default;
const redis = require('redis');
const { Pool } = require('pg');
const http = require('http');
const socketIo = require('socket.io');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('ffmpeg-static');
const fs = require('fs').promises;
const path = require('path');
const uuid = require('uuid').v4;
const mime = require('mime');
const OpenAI = require('openai');
const sentiment = require('sentiment');
const natural = require('natural');
const compromise = require('compromise');
const client = require('prom-client');
const promMiddleware = require('express-prometheus-middleware');

// Configure FFmpeg
ffmpeg.setFfmpegPath(ffmpegPath);

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Configure Winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Initialize Redis client
const redisClient = redis.createClient({
  host: process.env.REDIS_HOST || 'redis',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD
});

// Initialize PostgreSQL client
const pgPool = new Pool({
  host: process.env.POSTGRES_HOST || 'postgresql',
  port: process.env.POSTGRES_PORT || 5432,
  database: process.env.POSTGRES_DB || 'political_conversations',
  user: process.env.POSTGRES_USER || 'n8n_user',
  password: process.env.POSTGRES_PASSWORD
});

// Prometheus metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });

const voiceProcessingCounter = new client.Counter({
  name: 'voice_processing_total',
  help: 'Total number of voice processing requests',
  labelNames: ['type', 'status', 'language']
});

const voiceProcessingDuration = new client.Histogram({
  name: 'voice_processing_duration_seconds',
  help: 'Duration of voice processing operations',
  labelNames: ['type'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60, 120]
});

const realtimeConnectionsGauge = new client.Gauge({
  name: 'realtime_voice_connections',
  help: 'Number of active real-time voice connections'
});

const politicalContentDetected = new client.Counter({
  name: 'political_content_detected_total',
  help: 'Total political content detected in voice processing',
  labelNames: ['category', 'sentiment']
});

const transcriptionAccuracy = new client.Histogram({
  name: 'transcription_accuracy_score',
  help: 'Transcription accuracy scores',
  buckets: [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
});

register.registerMetric(voiceProcessingCounter);
register.registerMetric(voiceProcessingDuration);
register.registerMetric(realtimeConnectionsGauge);
register.registerMetric(politicalContentDetected);
register.registerMetric(transcriptionAccuracy);

// Initialize Express app and Socket.IO
const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5678'],
    methods: ['GET', 'POST'],
    credentials: true
  }
});

const port = process.env.MCP_SERVER_PORT || 8092;

// Political keywords and phrases for analysis
const politicalKeywords = {
  governance: ['government', 'policy', 'regulation', 'law', 'legislation', 'congress', 'senate', 'parliament'],
  economy: ['economy', 'budget', 'tax', 'fiscal', 'debt', 'gdp', 'inflation', 'unemployment'],
  international: ['foreign', 'international', 'diplomacy', 'trade', 'treaty', 'alliance', 'sanctions'],
  social: ['healthcare', 'education', 'welfare', 'social security', 'immigration', 'civil rights'],
  security: ['defense', 'military', 'security', 'terrorism', 'homeland', 'intelligence', 'surveillance'],
  environment: ['climate', 'environment', 'energy', 'renewable', 'carbon', 'pollution', 'sustainability']
};

// OAuth 2.1 and JWT configuration
const jwtSecret = process.env.JWT_SECRET || 'ultra_secure_jwt_secret_2025_voice';
const tokenExpiry = process.env.TOKEN_EXPIRY || 3600; // 1 hour

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "blob:"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5678'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting with different tiers
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000,
  message: {
    error: 'Too many requests',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

const voiceUploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 50, // Stricter for voice uploads
  message: {
    error: 'Too many voice uploads',
    retryAfter: '15 minutes'
  }
});

const realtimeLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 200, // Allow more for real-time
  message: {
    error: 'Too many real-time requests',
    retryAfter: '1 minute'
  }
});

app.use(generalLimiter);

// Session management with Redis
app.use(session({
  store: new RedisStore({ client: redisClient }),
  secret: process.env.SESSION_SECRET || 'ultra_secure_session_secret_2025',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Body parsing middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Logging middleware
app.use(morgan('combined', {
  stream: { write: (message) => logger.info(message.trim()) }
}));

// Prometheus metrics middleware
app.use(promMiddleware({
  metricsPath: '/metrics',
  collectDefaultMetrics: true,
  requestDurationBuckets: [0.1, 0.5, 1, 1.5, 2, 3, 5, 10],
  requestLengthBuckets: [512, 1024, 5120, 10240, 51200, 102400],
  responseLengthBuckets: [512, 1024, 5120, 10240, 51200, 102400]
}));

// Configure multer for audio uploads
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB for audio files
    files: 5
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4', 'audio/webm',
      'audio/flac', 'audio/aac', 'video/mp4', 'video/webm' // Allow video for audio extraction
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported audio file type'), false);
    }
  }
});

// JWT Authentication middleware
function authenticateJWT(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (authHeader) {
    const token = authHeader.split(' ')[1];
    
    jwt.verify(token, jwtSecret, (err, user) => {
      if (err) {
        return res.status(403).json({ error: 'Invalid or expired token' });
      }
      
      req.user = user;
      next();
    });
  } else {
    res.status(401).json({ error: 'Access token required' });
  }
}

// Voice processing functions
async function transcribeAudio(buffer, filename, options = {}) {
  const timer = voiceProcessingDuration.startTimer({ type: 'transcription' });
  
  try {
    const tempAudioPath = path.join('/tmp', `${uuid()}_${filename}`);
    const tempWavPath = path.join('/tmp', `${uuid()}_converted.wav`);
    
    // Write audio to temp file
    await fs.writeFile(tempAudioPath, buffer);
    
    // Convert to WAV for better compatibility
    await new Promise((resolve, reject) => {
      ffmpeg(tempAudioPath)
        .toFormat('wav')
        .audioChannels(1)
        .audioFrequency(16000)
        .on('end', resolve)
        .on('error', reject)
        .save(tempWavPath);
    });
    
    // Read converted audio
    const wavBuffer = await fs.readFile(tempWavPath);
    
    // Transcribe using OpenAI Whisper with enhanced options
    const transcription = await openai.audio.transcriptions.create({
      file: new File([wavBuffer], 'audio.wav', { type: 'audio/wav' }),
      model: process.env.WHISPER_MODEL || 'whisper-1',
      language: options.language || 'en',
      response_format: 'verbose_json',
      temperature: 0.2,
      prompt: options.prompt || "This is a political speech or discussion. Please transcribe accurately including political terms, names, and policy discussions."
    });
    
    // Calculate confidence score
    const confidence = transcription.segments?.reduce((acc, seg) => {
      return acc + (seg.avg_logprob || 0);
    }, 0) / (transcription.segments?.length || 1);
    
    // Clean up temp files
    await fs.unlink(tempAudioPath);
    await fs.unlink(tempWavPath);
    
    voiceProcessingCounter.inc({ 
      type: 'transcription', 
      status: 'success', 
      language: transcription.language || 'unknown' 
    });
    
    transcriptionAccuracy.observe(Math.exp(confidence)); // Convert log prob to 0-1
    timer();
    
    return {
      text: transcription.text,
      segments: transcription.segments,
      duration: transcription.duration,
      language: transcription.language,
      confidence: Math.exp(confidence)
    };
    
  } catch (error) {
    voiceProcessingCounter.inc({ 
      type: 'transcription', 
      status: 'error', 
      language: 'unknown' 
    });
    timer();
    throw error;
  }
}

async function analyzePoliticalContent(text) {
  const timer = voiceProcessingDuration.startTimer({ type: 'political_analysis' });
  
  try {
    // Sentiment analysis
    const sentimentResult = sentiment(text);
    
    // Political keyword detection
    const detectedCategories = {};
    const textLower = text.toLowerCase();
    
    for (const [category, keywords] of Object.entries(politicalKeywords)) {
      const matches = keywords.filter(keyword => textLower.includes(keyword.toLowerCase()));
      if (matches.length > 0) {
        detectedCategories[category] = matches;
        politicalContentDetected.inc({ 
          category, 
          sentiment: sentimentResult.score > 0 ? 'positive' : sentimentResult.score < 0 ? 'negative' : 'neutral' 
        });
      }
    }
    
    // Named entity recognition using compromise
    const doc = compromise(text);
    const entities = {
      people: doc.people().out('array'),
      places: doc.places().out('array'),
      organizations: doc.organizations().out('array'),
      topics: doc.topics().out('array')
    };
    
    // Enhanced analysis using OpenAI
    const enhancedAnalysis = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a political analyst specializing in speech and discourse analysis. Analyze the following transcription for political content, key themes, policy positions, and rhetorical strategies."
        },
        {
          role: "user",
          content: `Transcription: "${text}"\n\nProvide analysis including:\n1. Key political themes\n2. Policy positions mentioned\n3. Rhetorical strategies used\n4. Factual claims to verify\n5. Overall political lean/bias\n6. Important quotes or statements`
        }
      ],
      max_tokens: 500,
      temperature: 0.3
    });
    
    timer();
    
    return {
      sentiment: {
        score: sentimentResult.score,
        comparative: sentimentResult.comparative,
        classification: sentimentResult.score > 0 ? 'positive' : sentimentResult.score < 0 ? 'negative' : 'neutral'
      },
      politicalCategories: detectedCategories,
      entities,
      enhancedAnalysis: enhancedAnalysis.choices[0].message.content,
      metadata: {
        wordCount: text.split(' ').length,
        readingTime: Math.ceil(text.split(' ').length / 200), // Average reading speed
        analysisTimestamp: new Date().toISOString()
      }
    };
    
  } catch (error) {
    timer();
    throw error;
  }
}

async function storeVoiceProcessingResult(result, userId, metadata = {}) {
  try {
    const query = `
      INSERT INTO voice_processing_results 
      (id, user_id, transcription_text, confidence_score, language, duration, 
       political_analysis, entities, sentiment_analysis, created_at, metadata)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
      RETURNING id, created_at
    `;
    
    const values = [
      uuid(),
      userId,
      result.transcription.text,
      result.transcription.confidence,
      result.transcription.language,
      result.transcription.duration,
      JSON.stringify(result.analysis.enhancedAnalysis),
      JSON.stringify(result.analysis.entities),
      JSON.stringify(result.analysis.sentiment),
      JSON.stringify(metadata)
    ];
    
    const dbResult = await pgPool.query(query, values);
    return dbResult.rows[0];
    
  } catch (error) {
    logger.error('Failed to store voice processing result:', error);
    throw error;
  }
}

// MCP Tools
const tools = [
  {
    name: "transcribe_audio_file",
    description: "Transcribe uploaded audio file with political content analysis",
    inputSchema: {
      type: "object",
      properties: {
        language: {
          type: "string",
          description: "Expected language of the audio (e.g., 'en', 'es', 'fr')"
        },
        prompt: {
          type: "string",
          description: "Context prompt to improve transcription accuracy"
        },
        includePoliticalAnalysis: {
          type: "boolean",
          description: "Whether to perform political content analysis",
          default: true
        }
      }
    }
  },
  {
    name: "realtime_transcription_session",
    description: "Start a real-time voice transcription session with live political analysis",
    inputSchema: {
      type: "object",
      properties: {
        sessionId: {
          type: "string",
          description: "Unique session identifier"
        },
        language: {
          type: "string",
          description: "Expected language for real-time transcription"
        },
        enableLivePoliticalAnalysis: {
          type: "boolean",
          description: "Enable live political content analysis",
          default: true
        }
      },
      required: ["sessionId"]
    }
  },
  {
    name: "analyze_voice_sentiment",
    description: "Perform detailed sentiment and political bias analysis on transcribed text",
    inputSchema: {
      type: "object",
      properties: {
        text: {
          type: "string",
          description: "Transcribed text to analyze"
        },
        includeFactChecking: {
          type: "boolean",
          description: "Include fact-checking suggestions",
          default: false
        }
      },
      required: ["text"]
    }
  },
  {
    name: "get_voice_processing_history",
    description: "Retrieve historical voice processing results with filtering options",
    inputSchema: {
      type: "object",
      properties: {
        userId: {
          type: "string",
          description: "User ID to filter results"
        },
        dateFrom: {
          type: "string",
          description: "Start date for filtering (ISO format)"
        },
        dateTo: {
          type: "string",
          description: "End date for filtering (ISO format)"
        },
        language: {
          type: "string",
          description: "Filter by language"
        },
        minConfidence: {
          type: "number",
          description: "Minimum confidence score"
        },
        limit: {
          type: "number",
          description: "Maximum number of results",
          default: 50
        }
      }
    }
  },
  {
    name: "voice_processing_analytics",
    description: "Get analytics and statistics about voice processing usage and accuracy",
    inputSchema: {
      type: "object",
      properties: {
        timeRange: {
          type: "string",
          enum: ["1h", "24h", "7d", "30d"],
          description: "Time range for analytics",
          default: "24h"
        },
        groupBy: {
          type: "string",
          enum: ["hour", "day", "language", "user"],
          description: "How to group the analytics"
        }
      }
    }
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    service: 'voice-processing-mcp',
    features: {
      realTimeTranscription: true,
      politicalAnalysis: true,
      multiLanguageSupport: true,
      oauth2Security: true
    }
  });
});

app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
});

// Authentication routes
app.post('/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // In production, this would validate against a proper user database
    // For now, using environment variables for demo
    const validUsername = process.env.ADMIN_USERNAME || 'admin';
    const validPassword = process.env.ADMIN_PASSWORD || 'secure_password_2025';
    
    if (username === validUsername && password === validPassword) {
      const token = jwt.sign(
        { 
          userId: 'admin-user',
          username,
          permissions: ['transcribe', 'analyze', 'realtime']
        },
        jwtSecret,
        { expiresIn: tokenExpiry }
      );
      
      res.json({ 
        token,
        expiresIn: tokenExpiry,
        user: { id: 'admin-user', username }
      });
    } else {
      res.status(401).json({ error: 'Invalid credentials' });
    }
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
});

// Protected MCP tool endpoint
app.post('/mcp/call', authenticateJWT, upload.array('audioFiles'), async (req, res) => {
  try {
    const { tool, arguments: args } = req.body;
    
    if (!tools.find(t => t.name === tool)) {
      return res.status(400).json({ error: 'Unknown tool' });
    }
    
    let result;
    
    switch (tool) {
      case 'transcribe_audio_file':
        result = await handleTranscribeAudioFile(req.files, args, req.user);
        break;
      case 'realtime_transcription_session':
        result = await handleRealtimeTranscriptionSession(args, req.user);
        break;
      case 'analyze_voice_sentiment':
        result = await handleAnalyzeVoiceSentiment(args);
        break;
      case 'get_voice_processing_history':
        result = await handleGetVoiceProcessingHistory(args);
        break;
      case 'voice_processing_analytics':
        result = await handleVoiceProcessingAnalytics(args);
        break;
      default:
        return res.status(400).json({ error: 'Tool not implemented' });
    }
    
    res.json({ result });
    
  } catch (error) {
    logger.error('MCP tool error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Tool handlers
async function handleTranscribeAudioFile(files, args, user) {
  if (!files || files.length === 0) {
    throw new Error('No audio files provided');
  }
  
  const results = [];
  
  for (const file of files) {
    const { buffer, originalname } = file;
    
    // Transcribe audio
    const transcription = await transcribeAudio(buffer, originalname, {
      language: args.language,
      prompt: args.prompt
    });
    
    let analysis = null;
    if (args.includePoliticalAnalysis !== false) {
      analysis = await analyzePoliticalContent(transcription.text);
    }
    
    const result = {
      filename: originalname,
      transcription,
      analysis
    };
    
    // Store result
    const stored = await storeVoiceProcessingResult(result, user.userId, {
      originalFilename: originalname,
      fileSize: buffer.length,
      processingOptions: args
    });
    
    results.push({
      ...result,
      id: stored.id,
      processedAt: stored.created_at
    });
  }
  
  return {
    message: `Successfully processed ${results.length} audio files`,
    results
  };
}

async function handleRealtimeTranscriptionSession(args, user) {
  const { sessionId, language, enableLivePoliticalAnalysis } = args;
  
  // Store session info in Redis
  await redisClient.setex(`session:${sessionId}`, 3600, JSON.stringify({
    userId: user.userId,
    language,
    enableLivePoliticalAnalysis,
    startTime: new Date().toISOString()
  }));
  
  return {
    sessionId,
    status: 'ready',
    websocketUrl: `/realtime/${sessionId}`,
    message: 'Connect to the WebSocket endpoint to start real-time transcription'
  };
}

async function handleAnalyzeVoiceSentiment(args) {
  const { text, includeFactChecking } = args;
  
  const analysis = await analyzePoliticalContent(text);
  
  let factCheckingSuggestions = null;
  if (includeFactChecking) {
    // Generate fact-checking suggestions
    const factCheckResponse = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a fact-checker. Identify claims in the text that should be fact-checked and suggest reliable sources."
        },
        {
          role: "user",
          content: `Text: "${text}"\n\nIdentify factual claims that need verification and suggest appropriate fact-checking sources.`
        }
      ],
      max_tokens: 300
    });
    
    factCheckingSuggestions = factCheckResponse.choices[0].message.content;
  }
  
  return {
    ...analysis,
    factCheckingSuggestions
  };
}

async function handleGetVoiceProcessingHistory(args) {
  const { 
    userId, 
    dateFrom, 
    dateTo, 
    language, 
    minConfidence, 
    limit = 50 
  } = args;
  
  let query = `
    SELECT id, transcription_text, confidence_score, language, duration,
           political_analysis, entities, sentiment_analysis, created_at, metadata
    FROM voice_processing_results 
    WHERE 1=1
  `;
  
  const queryParams = [];
  let paramCount = 0;
  
  if (userId) {
    queryParams.push(userId);
    query += ` AND user_id = $${++paramCount}`;
  }
  
  if (dateFrom) {
    queryParams.push(dateFrom);
    query += ` AND created_at >= $${++paramCount}`;
  }
  
  if (dateTo) {
    queryParams.push(dateTo);
    query += ` AND created_at <= $${++paramCount}`;
  }
  
  if (language) {
    queryParams.push(language);
    query += ` AND language = $${++paramCount}`;
  }
  
  if (minConfidence !== undefined) {
    queryParams.push(minConfidence);
    query += ` AND confidence_score >= $${++paramCount}`;
  }
  
  queryParams.push(limit);
  query += ` ORDER BY created_at DESC LIMIT $${++paramCount}`;
  
  const result = await pgPool.query(query, queryParams);
  
  return {
    totalResults: result.rows.length,
    results: result.rows
  };
}

async function handleVoiceProcessingAnalytics(args) {
  const { timeRange = '24h', groupBy } = args;
  
  const timeRangeMap = {
    '1h': '1 hour',
    '24h': '24 hours',
    '7d': '7 days',
    '30d': '30 days'
  };
  
  const interval = timeRangeMap[timeRange];
  
  // Get processing statistics
  const statsQuery = `
    SELECT 
      COUNT(*) as total_processed,
      AVG(confidence_score) as avg_confidence,
      AVG(duration) as avg_duration,
      COUNT(DISTINCT language) as languages_detected
    FROM voice_processing_results 
    WHERE created_at >= NOW() - INTERVAL '${interval}'
  `;
  
  const stats = await pgPool.query(statsQuery);
  
  // Get language distribution
  const langQuery = `
    SELECT language, COUNT(*) as count
    FROM voice_processing_results 
    WHERE created_at >= NOW() - INTERVAL '${interval}'
    GROUP BY language
    ORDER BY count DESC
  `;
  
  const langStats = await pgPool.query(langQuery);
  
  return {
    timeRange,
    statistics: stats.rows[0],
    languageDistribution: langStats.rows,
    generatedAt: new Date().toISOString()
  };
}

// Real-time WebSocket handling
io.on('connection', (socket) => {
  logger.info(`New WebSocket connection: ${socket.id}`);
  realtimeConnectionsGauge.inc();
  
  socket.on('join-session', async (data) => {
    const { sessionId, token } = data;
    
    try {
      // Verify JWT token
      const decoded = jwt.verify(token, jwtSecret);
      
      // Get session info from Redis
      const sessionData = await redisClient.get(`session:${sessionId}`);
      
      if (!sessionData) {
        socket.emit('error', { message: 'Invalid session ID' });
        return;
      }
      
      const session = JSON.parse(sessionData);
      
      socket.join(sessionId);
      socket.userId = decoded.userId;
      socket.sessionId = sessionId;
      
      socket.emit('session-joined', { 
        sessionId,
        message: 'Ready for real-time transcription',
        config: session
      });
      
    } catch (error) {
      socket.emit('error', { message: 'Authentication failed' });
    }
  });
  
  socket.on('audio-chunk', async (data) => {
    if (!socket.sessionId) {
      socket.emit('error', { message: 'Not joined to any session' });
      return;
    }
    
    try {
      // In a real implementation, you would process audio chunks
      // For now, simulate real-time processing
      const transcription = {
        text: "Real-time transcription would be processed here",
        confidence: 0.95,
        timestamp: Date.now()
      };
      
      socket.emit('transcription-chunk', transcription);
      
    } catch (error) {
      socket.emit('error', { message: 'Audio processing failed' });
    }
  });
  
  socket.on('disconnect', () => {
    logger.info(`WebSocket disconnected: ${socket.id}`);
    realtimeConnectionsGauge.dec();
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'Audio file too large' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Too many files' });
    }
  }
  
  logger.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Initialize database schema
async function initializeDatabase() {
  try {
    const createTableQuery = `
      CREATE TABLE IF NOT EXISTS voice_processing_results (
        id UUID PRIMARY KEY,
        user_id VARCHAR(255) NOT NULL,
        transcription_text TEXT NOT NULL,
        confidence_score FLOAT,
        language VARCHAR(10),
        duration FLOAT,
        political_analysis JSONB,
        entities JSONB,
        sentiment_analysis JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        metadata JSONB
      );
      
      CREATE INDEX IF NOT EXISTS idx_voice_results_user_id ON voice_processing_results(user_id);
      CREATE INDEX IF NOT EXISTS idx_voice_results_created_at ON voice_processing_results(created_at);
      CREATE INDEX IF NOT EXISTS idx_voice_results_language ON voice_processing_results(language);
    `;
    
    await pgPool.query(createTableQuery);
    logger.info('Database schema initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize database schema:', error);
    throw error;
  }
}

// Start server
async function startServer() {
  try {
    await redisClient.connect();
    await initializeDatabase();
    
    server.listen(port, '0.0.0.0', () => {
      logger.info(`Voice Processing MCP Server running on port ${port}`);
      logger.info('Features enabled: Real-time transcription, Political analysis, OAuth 2.1 security');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();