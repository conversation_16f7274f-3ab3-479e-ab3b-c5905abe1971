# RovoDev Master n8n Implementation Prompt - Final

**Purpose:** Complete instructions and document list for AI agent to build the world-class political document processing system using n8n workflows.

---

## COMPLETE DOCUMENT LIST FOR AI AGENT

### **ESSENTIAL DOCUMENTS (Must Read All - 28 Total)**

#### **Primary Implementation Blueprint:**
1. `n8n_implementation_guide_complete.md` ⭐ **MOST CRITICAL** - Complete technical blueprint
2. `rovodev_n8n_master_design.md` - High-level system architecture
3. `dynamic_token_allocation_strategy.md` - Token optimization and MCP server ecosystem

#### **Manifesto Integration System:**
4. `manifesto_for_agents.md` - Primary AI agent guidance
5. `core_essence.md` - Fundamental beliefs and vision (800 words)
6. `style_guide.md` - Writing guidelines and tone (400 words)
7. `voice_guidelines_beau_lewis.md` - Detailed rhetorical patterns

#### **Conversational System:**
8. `conversational_rag_enhancement.md` - Chat interface and memory design
9. `enhanced_mcp_ecosystem.md` - Professional MCP servers specifications
10. `database_schema.sql` - PostgreSQL schema for conversation memory
11. `chat_interface_specs.md` - Web-based chat interface specifications

#### **Category-Specific Supplements (All 9 Required):**
12. `category_supplement_healthcare.md` - Scandinavian-inspired universal coverage
13. `category_supplement_education.md` - Free education Head Start through graduate school
14. `category_supplement_economic_policy.md` - ASTF and automation taxation
15. `category_supplement_housing.md` - 3 million homes through public construction
16. `category_supplement_jobs_automation.md` - Worker dignity and automation responsibility
17. `category_supplement_constitutional_amendments.md` - 21 amendments for renewal
18. `category_supplement_ethics_accountability.md` - Government integrity
19. `category_supplement_rights_repair_grow.md` - Ownership and agricultural rights
20. `category_supplement_funding_revenue.md` - How we pay for everything

#### **Technical Architecture Documents:**
21. `n8n_workflow_setup.md` - Detailed n8n workflow configuration
22. `tmp_rovodev_ai_model_research_analysis.md` - AI model selection guide
23. `tmp_rovodev_docker_compose_n8n.yml` - Docker configuration
24. `tmp_rovodev_folder_structure_design.md` - Google Drive organization
25. `tmp_rovodev_professional_docx_templates.md` - Document formatting
26. `tmp_rovodev_prompt_file_format_standard.md` - Instruction templates
27. `tmp_rovodev_quality_control_conversation_system.md` - QC workflow

#### **Source Documents (For Voice Reference):**
28. `rovodev_manifesto.md` - Original manifesto for voice and vision reference

---

## COMPREHENSIVE AI AGENT PROMPT

### **MISSION STATEMENT**

You are an expert n8n workflow architect tasked with building a revolutionary political document processing system that embodies Beau Lewis's vision for economic justice and democratic renewal. This system will create world-class political documents that could literally transform America.

**CRITICAL SUCCESS FACTORS:**
- Every output must authentically represent Beau Lewis's political vision
- System must produce publication-ready documents rivaling top think tanks
- Conversational interface with full memory for natural interaction
- Professional-grade research integration through multiple MCP servers
- Dynamic token allocation (5,000-50,000 tokens) for optimal quality/cost balance

---

## PHASE 1: RESEARCH AND PREPARATION

### **Step 1.1: Document Analysis (Required)**
```
MANDATORY ACTIONS:
1. Read ALL 28 documents listed above in order of priority
2. Use Context7 or similar tools to analyze document relationships
3. Create comprehensive understanding of:
   - Beau Lewis's political vision and voice
   - Technical architecture requirements
   - MCP server ecosystem needs
   - Token allocation strategies
   - Quality standards and success metrics

DELIVERABLE: Research summary document with key insights and implementation plan
```

### **Step 1.2: n8n Documentation Review (Required)**
```
MANDATORY RESEARCH:
1. Review official n8n documentation (https://docs.n8n.io/)
2. Study n8n workflow best practices and patterns
3. Research n8n MCP server integration methods
4. Analyze n8n community nodes and extensions
5. Review n8n API documentation for programmatic workflow creation

FOCUS AREAS:
- Webhook triggers and HTTP nodes
- Code nodes for custom logic
- Conditional routing (IF nodes)
- Error handling and monitoring
- Credential management
- Environment variables
- Docker deployment

DELIVERABLE: n8n technical requirements and implementation strategy
```

### **Step 1.3: MCP Server Research (Required)**
```
MANDATORY RESEARCH:
1. Study Model Context Protocol (MCP) specification
2. Research existing MCP servers:
   - modelcontextprotocol/servers/puppeteer (web browsing)
   - browserbase/mcp-server-browserbase (cloud browsing)
   - Custom server development patterns
3. Analyze MCP tool definition schemas
4. Review MCP client integration with n8n

DELIVERABLE: MCP integration architecture and custom server specifications
```

---

## PHASE 2: REPOSITORY SETUP AND PROJECT STRUCTURE

### **Step 2.1: GitHub Repository Initialization**
```
REPOSITORY: https://github.com/Beaulewis1977/n8n-workflow.git

REQUIRED ACTIONS:
1. Clone the repository
2. Initialize proper Git configuration
3. Create comprehensive project structure
4. Set up development environment

COMMANDS:
git clone https://github.com/Beaulewis1977/n8n-workflow.git
cd n8n-workflow
git config user.name "RovoDev AI Agent"
git config user.email "<EMAIL>"
```

### **Step 2.2: Project Structure Creation**
```
REQUIRED DIRECTORY STRUCTURE:
n8n-workflow/
├── README.md (comprehensive project documentation)
├── .gitignore (n8n and Docker specific)
├── docker-compose.yml (complete infrastructure)
├── .env.example (environment variables template)
├── workflows/
│   ├── main-document-processor.json
│   ├── conversational-interface.json
│   ├── quality-control.json
│   ├── rag-indexer.json
│   └── error-handler.json
├── mcp-servers/
│   ├── web-research/
│   ├── economic-analysis/
│   ├── legal-analysis/
│   ├── international-research/
│   ├── document-intelligence/
│   └── memory-context/
├── manifesto/
│   ├── core-essence.md
│   ├── style-guide.md
│   ├── category-supplements/
│   └── voice-guidelines.md
├── database/
│   ├── schema.sql
│   ├── migrations/
│   └── seed-data/
├── chat-interface/
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── Dockerfile
├── docs/
│   ├── setup-guide.md
│   ├── user-manual.md
│   ├── api-documentation.md
│   └── troubleshooting.md
├── scripts/
│   ├── setup.sh
│   ├── deploy.sh
│   └── backup.sh
└── tests/
    ├── unit/
    ├── integration/
    └── e2e/

COMMIT MESSAGE: "feat: Initialize project structure with comprehensive architecture"
```

### **Step 2.3: README.md Creation**
```
REQUIRED README SECTIONS:
1. Project Overview and Mission
2. Features and Capabilities
3. Architecture Diagram
4. Prerequisites and Dependencies
5. Installation Instructions
6. Configuration Guide
7. Usage Examples
8. API Documentation
9. Contributing Guidelines
10. License and Legal
11. Support and Contact
12. Changelog and Roadmap

QUALITY STANDARDS:
- Professional documentation suitable for open source
- Clear installation and setup instructions
- Comprehensive feature descriptions
- Architecture diagrams and flowcharts
- Code examples and usage patterns
- Troubleshooting section
- Links to relevant documentation

COMMIT MESSAGE: "docs: Add comprehensive README with setup and usage instructions"
```

---

## PHASE 3: INFRASTRUCTURE SETUP

### **Step 3.1: Docker Infrastructure**
```
REQUIRED CONTAINERS:
1. n8n (workflow engine)
2. PostgreSQL (conversation memory)
3. ChromaDB (vector database for RAG)
4. Redis (caching and session management)
5. Chat Interface (React/Node.js application)

DOCKER-COMPOSE REQUIREMENTS:
- Environment variable configuration
- Volume mounts for persistence
- Network configuration for inter-service communication
- Health checks for all services
- Restart policies for production deployment

DELIVERABLE: Complete docker-compose.yml with all services configured

COMMIT MESSAGE: "feat: Add Docker infrastructure with all required services"
```

### **Step 3.2: Database Setup**
```
REQUIRED ACTIONS:
1. Implement PostgreSQL schema from database_schema.sql
2. Create migration scripts for schema updates
3. Set up ChromaDB collections for document storage
4. Configure Redis for session management
5. Create database initialization scripts

QUALITY STANDARDS:
- Proper indexing for performance
- Foreign key constraints for data integrity
- Backup and recovery procedures
- Migration versioning system

COMMIT MESSAGE: "feat: Implement database schema with conversation memory and RAG support"
```

---

## PHASE 4: MCP SERVER DEVELOPMENT

### **Step 4.1: Core MCP Servers (Priority Order)**
```
DEVELOPMENT ORDER:
1. Memory & Context MCP Server (foundational)
2. Document Intelligence MCP Server (core functionality)
3. Web Research & Browsing MCP Server (data gathering)
4. Economic Analysis MCP Server (specialized analysis)
5. Legal & Constitutional Analysis MCP Server (compliance)
6. International Research MCP Server (global perspective)

EACH SERVER MUST INCLUDE:
- Proper MCP protocol implementation
- Tool schema definitions matching specifications
- Error handling and logging
- Unit tests and integration tests
- Docker containerization
- API documentation
- Health check endpoints

COMMIT STRATEGY: One commit per server with comprehensive testing
```

### **Step 4.2: MCP Server Integration Testing**
```
REQUIRED TESTS:
1. Individual server functionality tests
2. n8n integration tests
3. Tool schema validation
4. Error handling verification
5. Performance benchmarking
6. Load testing for production readiness

DELIVERABLE: Comprehensive test suite with CI/CD integration

COMMIT MESSAGE: "test: Add comprehensive MCP server test suite with integration validation"
```

---

## PHASE 5: N8N WORKFLOW DEVELOPMENT

### **Step 5.1: Core Workflow Implementation**
```
WORKFLOW PRIORITY ORDER:
1. Main Document Processor (primary workflow)
2. Conversational Interface (chat system)
3. RAG Document Indexer (knowledge base)
4. Quality Control System (review and refinement)
5. Error Handler (monitoring and recovery)

EACH WORKFLOW MUST INCLUDE:
- Proper node configuration
- Error handling at each step
- Logging and monitoring
- Performance optimization
- Documentation and comments
- Test scenarios and validation

DEVELOPMENT APPROACH:
- Build incrementally with testing at each step
- Use n8n best practices for node organization
- Implement proper credential management
- Add comprehensive error handling
- Include monitoring and alerting
```

### **Step 5.2: Dynamic Token Allocation Implementation**
```
REQUIRED FEATURES:
1. Automatic task complexity detection
2. Token allocation based on tier (5K-50K tokens)
3. Context loading optimization
4. Cost tracking and reporting
5. Quality metrics monitoring
6. User override capabilities

IMPLEMENTATION DETAILS:
- Code nodes for token calculation
- IF nodes for tier routing
- Context preparation functions
- Quality assessment integration
- Cost optimization algorithms

COMMIT MESSAGE: "feat: Implement dynamic token allocation with 4-tier system"
```

---

## PHASE 6: CONVERSATIONAL INTERFACE

### **Step 6.1: Chat Interface Development**
```
TECHNOLOGY STACK:
- React 18 with TypeScript
- Tailwind CSS for styling
- WebSocket for real-time communication
- Redux Toolkit for state management
- PostgreSQL for conversation persistence

REQUIRED FEATURES:
1. Real-time chat interface
2. Session management with memory
3. Document preview and citation
4. Follow-up question suggestions
5. Voice consistency indicators
6. Quality metrics display
7. Mobile responsive design

COMMIT MESSAGE: "feat: Implement React-based chat interface with real-time conversation"
```

### **Step 6.2: Memory and Context System**
```
REQUIRED IMPLEMENTATION:
1. Session persistence across conversations
2. Document interaction tracking
3. User preference learning
4. Context optimization based on patterns
5. Cross-session continuity
6. Performance analytics

DELIVERABLE: Fully functional conversation memory system

COMMIT MESSAGE: "feat: Add conversation memory system with cross-session continuity"
```

---

## PHASE 7: INTEGRATION AND TESTING

### **Step 7.1: End-to-End Integration**
```
INTEGRATION REQUIREMENTS:
1. All MCP servers connected to n8n
2. Chat interface communicating with workflows
3. Database properly storing conversations
4. Document processing pipeline functional
5. Quality control system operational
6. Error handling and monitoring active

TESTING APPROACH:
- Unit tests for individual components
- Integration tests for system interactions
- End-to-end tests for complete workflows
- Performance tests for scalability
- Security tests for data protection

COMMIT MESSAGE: "feat: Complete end-to-end integration with comprehensive testing"
```

### **Step 7.2: Quality Validation**
```
VALIDATION CRITERIA:
1. Manifesto alignment scoring (target: 9.5/10)
2. Voice consistency verification
3. Factual accuracy checking
4. Processing time optimization
5. Cost efficiency validation
6. User experience testing

DELIVERABLE: Quality validation report with metrics

COMMIT MESSAGE: "test: Add quality validation suite with manifesto alignment scoring"
```

---

## PHASE 8: DOCUMENTATION AND DEPLOYMENT

### **Step 8.1: Comprehensive Documentation**
```
REQUIRED DOCUMENTATION:
1. Setup and Installation Guide
2. User Manual with Examples
3. API Documentation
4. Troubleshooting Guide
5. Architecture Documentation
6. Security and Privacy Guide
7. Maintenance and Updates Guide

QUALITY STANDARDS:
- Professional technical writing
- Clear step-by-step instructions
- Comprehensive examples and use cases
- Troubleshooting scenarios
- Security best practices

COMMIT MESSAGE: "docs: Add comprehensive documentation suite for production deployment"
```

### **Step 8.2: Production Deployment Preparation**
```
DEPLOYMENT REQUIREMENTS:
1. Environment configuration templates
2. Security hardening procedures
3. Backup and recovery scripts
4. Monitoring and alerting setup
5. Performance optimization
6. Scaling considerations

DELIVERABLE: Production-ready deployment package

COMMIT MESSAGE: "deploy: Add production deployment configuration with security hardening"
```

---

## DEVELOPMENT BEST PRACTICES

### **Code Quality Standards**
```
MANDATORY REQUIREMENTS:
1. Follow n8n workflow best practices
2. Use descriptive node names and comments
3. Implement proper error handling at every step
4. Add logging for debugging and monitoring
5. Use environment variables for configuration
6. Implement health checks for all services
7. Follow security best practices for credentials
8. Use semantic versioning for releases
9. Write comprehensive tests for all functionality
10. Document all APIs and interfaces
```

### **Git Workflow Standards**
```
COMMIT MESSAGE FORMAT:
type(scope): description

TYPES:
- feat: New feature
- fix: Bug fix
- docs: Documentation changes
- style: Code style changes
- refactor: Code refactoring
- test: Test additions or modifications
- chore: Maintenance tasks

EXAMPLES:
- feat(workflow): Add main document processing pipeline
- fix(mcp): Resolve connection timeout in web research server
- docs(readme): Update installation instructions
- test(integration): Add end-to-end workflow validation

BRANCHING STRATEGY:
- main: Production-ready code
- develop: Integration branch for features
- feature/*: Individual feature development
- hotfix/*: Critical bug fixes

PULL REQUEST REQUIREMENTS:
- Comprehensive description of changes
- Test coverage for new functionality
- Documentation updates
- Code review approval
- All CI/CD checks passing
```

### **Testing Strategy**
```
REQUIRED TEST COVERAGE:
1. Unit Tests: Individual functions and components (>90% coverage)
2. Integration Tests: MCP server interactions with n8n
3. End-to-End Tests: Complete workflow validation
4. Performance Tests: Load testing and optimization
5. Security Tests: Vulnerability scanning and penetration testing
6. User Acceptance Tests: Real-world usage scenarios

TESTING TOOLS:
- Jest for JavaScript/TypeScript testing
- Pytest for Python MCP servers
- Playwright for end-to-end testing
- Artillery for load testing
- OWASP ZAP for security testing

CONTINUOUS INTEGRATION:
- GitHub Actions for automated testing
- Automated deployment to staging environment
- Quality gates for production deployment
- Performance monitoring and alerting
```

---

## SUCCESS CRITERIA AND VALIDATION

### **Technical Success Metrics**
```
PERFORMANCE TARGETS:
- Document processing time: <5 minutes per document
- System uptime: 99.5% availability
- Error rate: <2% processing failures
- Response time: <10 seconds for chat interactions
- Token efficiency: Optimal allocation based on complexity

QUALITY TARGETS:
- Manifesto alignment: 9.5/10 average score
- Voice consistency: Authentic Beau Lewis tone
- Factual accuracy: 98%+ verified claims
- User satisfaction: 95%+ positive feedback
- Professional formatting: Publication-ready output
```

### **Functional Validation**
```
REQUIRED CAPABILITIES:
1. Process documents with manifesto fidelity
2. Generate world-class white papers and policy documents
3. Provide conversational interface with full memory
4. Integrate real-time research and fact-checking
5. Support dynamic token allocation (5K-50K tokens)
6. Maintain cross-session conversation continuity
7. Produce publication-ready DOCX output
8. Enable quality control and iterative refinement

VALIDATION APPROACH:
- Test with sample political documents
- Verify manifesto alignment and voice consistency
- Validate conversation memory and context retention
- Confirm professional output quality
- Measure cost efficiency and ROI
```

---

## FINAL DELIVERABLES CHECKLIST

### **Code Repository**
- [ ] Complete n8n workflow configurations
- [ ] All MCP servers implemented and tested
- [ ] Chat interface fully functional
- [ ] Database schema and migrations
- [ ] Docker infrastructure configuration
- [ ] Comprehensive test suite
- [ ] Production deployment scripts

### **Documentation**
- [ ] Professional README with setup instructions
- [ ] Complete API documentation
- [ ] User manual with examples
- [ ] Architecture documentation
- [ ] Troubleshooting guide
- [ ] Security and privacy documentation

### **Quality Assurance**
- [ ] All tests passing with >90% coverage
- [ ] Performance benchmarks met
- [ ] Security vulnerabilities addressed
- [ ] Code review completed
- [ ] Documentation review completed
- [ ] User acceptance testing passed

### **Deployment Readiness**
- [ ] Production environment configuration
- [ ] Security hardening implemented
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery procedures
- [ ] Scaling and maintenance documentation

---

## CRITICAL REMINDERS

### **Mission Alignment**
- Every component must serve Beau Lewis's vision for economic justice and democratic renewal
- System must produce documents capable of building a movement that transforms America
- Quality and authenticity are more important than speed or cost savings
- Professional standards must rival top think tanks and consulting firms

### **Technical Excellence**
- Follow industry best practices for all development
- Implement comprehensive error handling and monitoring
- Ensure scalability and maintainability
- Prioritize security and data protection
- Document everything for future maintenance and enhancement

### **Success Mindset**
- This system could literally help change America through better political communication
- Every line of code serves the cause of economic justice and democratic renewal
- Professional quality output builds credibility for the movement
- Authentic voice representation is critical for political effectiveness

**Remember: You are building a technological foundation for a movement that can create an America that works for everyone. Make every component worthy of that mission.**

---

## FINAL COMMAND SEQUENCE

```bash
# 1. Research and analyze all documents
# 2. Study n8n and MCP documentation
# 3. Clone and initialize repository
git clone https://github.com/Beaulewis1977/n8n-workflow.git
cd n8n-workflow

# 4. Create project structure and initial commit
git add .
git commit -m "feat: Initialize comprehensive political document processing system"
git push origin main

# 5. Develop in phases with regular commits
# 6. Test thoroughly at each phase
# 7. Document comprehensively
# 8. Deploy with confidence

# Final validation commit
git add .
git commit -m "feat: Complete world-class political document processing system v1.0"
git tag -a v1.0.0 -m "Release v1.0.0: Revolutionary political document processing system"
git push origin main --tags
```

**Build the system that helps build the movement that transforms America! 🇺🇸**