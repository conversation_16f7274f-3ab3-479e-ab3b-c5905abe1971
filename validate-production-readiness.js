#!/usr/bin/env node

/**
 * MCP Ecosystem Production Readiness Validator
 * Comprehensive validation for 99.9% uptime requirements
 * Validates infrastructure, security, performance, and operational readiness
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import winston from 'winston';
import MCPHealthMonitor from './mcp-health-monitor.js';
import MCPIntegrationTester from './mcp-integration-tests.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} [${level}]: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'production-validation.log' })
  ]
});

class ProductionReadinessValidator {
  constructor() {
    this.validationResults = [];
    this.criticalIssues = [];
    this.warnings = [];
    this.healthMonitor = new MCPHealthMonitor();
    this.integrationTester = new MCPIntegrationTester();
  }

  async validateProductionReadiness() {
    logger.info('🚀 Starting Production Readiness Validation for MCP Ecosystem');
    logger.info('Target: 99.9% uptime with enterprise-grade reliability');
    
    const validationStart = Date.now();

    const validationSections = [
      'Infrastructure Validation',
      'Security Validation', 
      'Performance Validation',
      'Reliability Validation',
      'Operational Readiness',
      'Integration Validation',
      'Compliance Validation'
    ];

    for (const section of validationSections) {
      logger.info(`\n📋 ${section}...`);
      
      try {
        switch (section) {
          case 'Infrastructure Validation':
            await this.validateInfrastructure();
            break;
          case 'Security Validation':
            await this.validateSecurity();
            break;
          case 'Performance Validation':
            await this.validatePerformance();
            break;
          case 'Reliability Validation':
            await this.validateReliability();
            break;
          case 'Operational Readiness':
            await this.validateOperationalReadiness();
            break;
          case 'Integration Validation':
            await this.validateIntegrations();
            break;
          case 'Compliance Validation':
            await this.validateCompliance();
            break;
        }
      } catch (error) {
        this.criticalIssues.push(`${section} failed: ${error.message}`);
        logger.error(`❌ ${section} failed:`, error.message);
      }
    }

    const validationDuration = Date.now() - validationStart;
    const report = await this.generateValidationReport(validationDuration);
    
    logger.info(`\n✅ Production readiness validation completed in ${Math.round(validationDuration)}ms`);
    
    return report;
  }

  async validateInfrastructure() {
    logger.info('  🔧 Validating infrastructure components...');

    // Validate Docker Compose Configuration
    await this.validateDockerCompose();
    
    // Validate Environment Variables
    await this.validateEnvironmentVariables();
    
    // Validate File Structure
    await this.validateFileStructure();
    
    // Validate Database Setup
    await this.validateDatabaseSetup();
    
    // Validate Network Configuration
    await this.validateNetworkConfiguration();

    logger.info('  ✅ Infrastructure validation completed');
  }

  async validateDockerCompose() {
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    
    if (!(await fs.pathExists(dockerComposePath))) {
      this.criticalIssues.push('docker-compose.yml not found');
      return;
    }

    const composeContent = await fs.readFile(dockerComposePath, 'utf8');
    
    // Validate required services
    const requiredServices = [
      'n8n', 'postgresql', 'redis', 'chromadb',
      'mcp-main', 'mcp-vector-search', 'mcp-web-research',
      'mcp-economic-analysis', 'mcp-legal-analysis',
      'mcp-document-intelligence', 'mcp-memory-context',
      'mcp-fact-checking', 'mcp-social-monitoring',
      'mcp-analytics-secure', 'mcp-multimodal-chromadb',
      'mcp-voice-processing', 'mcp-autonomous-ensemble'
    ];

    const missingServices = requiredServices.filter(service => 
      !composeContent.includes(`${service}:`)
    );

    if (missingServices.length > 0) {
      this.criticalIssues.push(`Missing Docker services: ${missingServices.join(', ')}`);
    }

    // Validate health checks
    const servicesWithHealthChecks = (composeContent.match(/healthcheck:/g) || []).length;
    if (servicesWithHealthChecks < 10) {
      this.warnings.push(`Only ${servicesWithHealthChecks} services have health checks configured`);
    }

    // Validate resource limits
    if (!composeContent.includes('deploy:') && !composeContent.includes('mem_limit:')) {
      this.warnings.push('No resource limits configured - may cause memory issues in production');
    }

    this.recordValidation('docker_compose_validation', true, 
      `Found ${requiredServices.length - missingServices.length}/${requiredServices.length} required services`);
  }

  async validateEnvironmentVariables() {
    const requiredEnvVars = [
      'OPENAI_API_KEY',
      'ANTHROPIC_API_KEY', 
      'GOOGLE_AI_API_KEY',
      'POSTGRES_PASSWORD',
      'REDIS_PASSWORD',
      'N8N_ENCRYPTION_KEY',
      'CLOUDCONVERT_API_KEY'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      this.criticalIssues.push(`Missing environment variables: ${missingVars.join(', ')}`);
    }

    // Validate API key formats (basic validation)
    const apiKeys = {
      'OPENAI_API_KEY': /^sk-[a-zA-Z0-9]{48}$/,
      'ANTHROPIC_API_KEY': /^sk-ant-[a-zA-Z0-9-_]{95}$/
    };

    for (const [key, pattern] of Object.entries(apiKeys)) {
      const value = process.env[key];
      if (value && !pattern.test(value)) {
        this.warnings.push(`${key} format appears invalid`);
      }
    }

    this.recordValidation('environment_variables', missingVars.length === 0,
      `${requiredEnvVars.length - missingVars.length}/${requiredEnvVars.length} required variables present`);
  }

  async validateFileStructure() {
    const requiredPaths = [
      'workflows/enhanced-political-document-processor.json',
      'workflows/political-document-processor.json',
      'mcp-servers/manifesto-context/server.js',
      'mcp-servers/political-content/server.js',
      'mcp-servers/quality-control/server.js',
      'mcp-servers/research-integration/server.js',
      'mcp-servers/document-processing/server.js',
      'mcp-servers/shared/error-handling.js',
      'manifesto/', // Directory should exist
      'database/', // Directory should exist
    ];

    let missingPaths = [];
    
    for (const reqPath of requiredPaths) {
      const fullPath = path.join(__dirname, reqPath);
      if (!(await fs.pathExists(fullPath))) {
        missingPaths.push(reqPath);
      }
    }

    if (missingPaths.length > 0) {
      this.criticalIssues.push(`Missing required files/directories: ${missingPaths.join(', ')}`);
    }

    // Check for manifesto content
    const manifestoDir = path.join(__dirname, 'manifesto');
    if (await fs.pathExists(manifestoDir)) {
      const manifestoFiles = await fs.readdir(manifestoDir);
      if (manifestoFiles.length === 0) {
        this.warnings.push('Manifesto directory is empty - manifesto context server may not function properly');
      }
    }

    this.recordValidation('file_structure', missingPaths.length === 0,
      `${requiredPaths.length - missingPaths.length}/${requiredPaths.length} required paths present`);
  }

  async validateDatabaseSetup() {
    // Check for database initialization scripts
    const dbInitPath = path.join(__dirname, 'database/init-db.sql');
    const dbMigrationsPath = path.join(__dirname, 'database/migrations');
    
    let issues = [];
    
    if (!(await fs.pathExists(dbInitPath))) {
      issues.push('Database initialization script missing');
    }
    
    if (!(await fs.pathExists(dbMigrationsPath))) {
      issues.push('Database migrations directory missing');
    }

    // Validate connection string format
    const dbUrl = process.env.DATABASE_URL || 
      `postgresql://${process.env.POSTGRES_USER || 'n8n_user'}:${process.env.POSTGRES_PASSWORD}@${process.env.POSTGRES_HOST || 'postgresql'}:${process.env.POSTGRES_PORT || 5432}/${process.env.POSTGRES_DB || 'n8n'}`;
    
    try {
      new URL(dbUrl);
    } catch (error) {
      issues.push('Invalid database connection URL format');
    }

    if (issues.length > 0) {
      this.warnings.push(...issues);
    }

    this.recordValidation('database_setup', issues.length === 0,
      issues.length === 0 ? 'Database configuration validated' : `${issues.length} database issues found`);
  }

  async validateNetworkConfiguration() {
    // Validate Docker network configuration
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');
    
    let networkIssues = [];
    
    if (!composeContent.includes('networks:')) {
      networkIssues.push('No custom networks defined');
    }
    
    if (!composeContent.includes('political-network')) {
      networkIssues.push('Political network not defined');
    }

    // Check for port conflicts
    const portMappings = composeContent.match(/- "(\d+):\d+"/g) || [];
    const ports = portMappings.map(p => p.match(/- "(\d+):/)[1]);
    const duplicatePorts = ports.filter((port, index) => ports.indexOf(port) !== index);
    
    if (duplicatePorts.length > 0) {
      networkIssues.push(`Duplicate port mappings: ${duplicatePorts.join(', ')}`);
    }

    if (networkIssues.length > 0) {
      this.warnings.push(...networkIssues);
    }

    this.recordValidation('network_configuration', networkIssues.length === 0,
      networkIssues.length === 0 ? 'Network configuration validated' : `${networkIssues.length} network issues found`);
  }

  async validateSecurity() {
    logger.info('  🔒 Validating security configuration...');

    await this.validateAuthenticationSetup();
    await this.validateSecretManagement();
    await this.validateNetworkSecurity();
    await this.validateDataProtection();

    logger.info('  ✅ Security validation completed');
  }

  async validateAuthenticationSetup() {
    let authIssues = [];

    // Check N8N authentication
    if (!process.env.N8N_BASIC_AUTH_ACTIVE || process.env.N8N_BASIC_AUTH_ACTIVE !== 'true') {
      authIssues.push('N8N basic authentication not enabled');
    }

    if (!process.env.N8N_BASIC_AUTH_PASSWORD || process.env.N8N_BASIC_AUTH_PASSWORD === 'changeme123') {
      authIssues.push('N8N using default or weak password');
    }

    // Check encryption keys
    if (!process.env.N8N_ENCRYPTION_KEY || process.env.N8N_ENCRYPTION_KEY === 'your-encryption-key') {
      authIssues.push('N8N encryption key not set or using default');
    }

    if (authIssues.length > 0) {
      this.criticalIssues.push(...authIssues);
    }

    this.recordValidation('authentication_setup', authIssues.length === 0,
      authIssues.length === 0 ? 'Authentication properly configured' : `${authIssues.length} auth issues found`);
  }

  async validateSecretManagement() {
    let secretIssues = [];

    // Check for hardcoded secrets in docker-compose
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    const suspiciousPatterns = [
      /password.*[:=]\s*['"][^'"]*['"]/gi,
      /secret.*[:=]\s*['"][^'"]*['"]/gi,
      /key.*[:=]\s*['"][^'"]*['"]/gi
    ];

    suspiciousPatterns.forEach(pattern => {
      const matches = composeContent.match(pattern);
      if (matches && matches.some(match => !match.includes('${') && !match.includes('changeme'))) {
        secretIssues.push('Potential hardcoded secrets found in docker-compose.yml');
      }
    });

    // Check Redis password
    if (!process.env.REDIS_PASSWORD || process.env.REDIS_PASSWORD === 'redis_password') {
      secretIssues.push('Redis using default or weak password');
    }

    if (secretIssues.length > 0) {
      this.criticalIssues.push(...secretIssues);
    }

    this.recordValidation('secret_management', secretIssues.length === 0,
      secretIssues.length === 0 ? 'Secret management validated' : `${secretIssues.length} secret issues found`);
  }

  async validateNetworkSecurity() {
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    let networkSecurityIssues = [];

    // Check for unnecessary port exposures
    const exposedPorts = (composeContent.match(/- "\d+:\d+"/g) || []).length;
    if (exposedPorts > 10) {
      networkSecurityIssues.push(`Many ports exposed (${exposedPorts}) - consider reducing attack surface`);
    }

    // Check for production profiles
    if (!composeContent.includes('profiles:')) {
      networkSecurityIssues.push('No deployment profiles configured for production');
    }

    if (networkSecurityIssues.length > 0) {
      this.warnings.push(...networkSecurityIssues);
    }

    this.recordValidation('network_security', networkSecurityIssues.length === 0,
      `Network security configuration reviewed`);
  }

  async validateDataProtection() {
    let dataProtectionIssues = [];

    // Check for volume persistence
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    const persistentVolumes = [
      'postgres_data', 'chromadb_data', 'redis_data', 'n8n_data'
    ];

    const missingVolumes = persistentVolumes.filter(volume => 
      !composeContent.includes(volume)
    );

    if (missingVolumes.length > 0) {
      dataProtectionIssues.push(`Missing persistent volumes: ${missingVolumes.join(', ')}`);
    }

    if (dataProtectionIssues.length > 0) {
      this.warnings.push(...dataProtectionIssues);
    }

    this.recordValidation('data_protection', dataProtectionIssues.length === 0,
      `Data protection measures validated`);
  }

  async validatePerformance() {
    logger.info('  ⚡ Validating performance configuration...');

    await this.validateResourceAllocation();
    await this.validateCachingStrategy();
    await this.validateDatabaseOptimization();

    logger.info('  ✅ Performance validation completed');
  }

  async validateResourceAllocation() {
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    let resourceIssues = [];

    // Check for resource limits
    const servicesWithLimits = (composeContent.match(/deploy:/g) || []).length +
                              (composeContent.match(/mem_limit:/g) || []).length;
    
    if (servicesWithLimits < 5) {
      resourceIssues.push('Insufficient resource limits configured - may cause memory issues');
    }

    // Check for restart policies
    const restartPolicies = (composeContent.match(/restart:/g) || []).length;
    const totalServices = (composeContent.match(/^  \w+(-\w+)*:/gm) || []).length;
    
    if (restartPolicies < totalServices * 0.8) {
      resourceIssues.push('Not all services have restart policies configured');
    }

    if (resourceIssues.length > 0) {
      this.warnings.push(...resourceIssues);
    }

    this.recordValidation('resource_allocation', resourceIssues.length === 0,
      `Resource allocation configuration reviewed`);
  }

  async validateCachingStrategy() {
    let cachingIssues = [];

    // Check Redis configuration
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    if (!composeContent.includes('redis:')) {
      cachingIssues.push('Redis not configured - performance may be impacted');
    }

    // Check if MCP servers use Redis
    const mcpServerPaths = [
      'mcp-servers/manifesto-context/server.js',
      'mcp-servers/political-content/server.js',
      'mcp-servers/quality-control/server.js'
    ];

    let serversUsingRedis = 0;
    for (const serverPath of mcpServerPaths) {
      const fullPath = path.join(__dirname, serverPath);
      if (await fs.pathExists(fullPath)) {
        const serverContent = await fs.readFile(fullPath, 'utf8');
        if (serverContent.includes('redis') || serverContent.includes('createClient')) {
          serversUsingRedis++;
        }
      }
    }

    if (serversUsingRedis < mcpServerPaths.length * 0.8) {
      cachingIssues.push('Not all MCP servers are configured to use Redis caching');
    }

    if (cachingIssues.length > 0) {
      this.warnings.push(...cachingIssues);
    }

    this.recordValidation('caching_strategy', cachingIssues.length === 0,
      `${serversUsingRedis}/${mcpServerPaths.length} MCP servers use Redis caching`);
  }

  async validateDatabaseOptimization() {
    let dbOptimizationIssues = [];

    // Check PostgreSQL configuration
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    // Check for connection pooling
    if (!composeContent.includes('POSTGRES_MULTIPLE_DATABASES')) {
      dbOptimizationIssues.push('Multiple database setup not configured');
    }

    // Check for database initialization
    if (!composeContent.includes('init-db.sql')) {
      dbOptimizationIssues.push('Database initialization script not mounted');
    }

    if (dbOptimizationIssues.length > 0) {
      this.warnings.push(...dbOptimizationIssues);
    }

    this.recordValidation('database_optimization', dbOptimizationIssues.length === 0,
      'Database optimization configuration reviewed');
  }

  async validateReliability() {
    logger.info('  🛡️  Validating reliability and fault tolerance...');

    await this.validateHealthChecks();
    await this.validateErrorHandling();
    await this.validateCircuitBreakers();
    await this.validateMonitoring();

    logger.info('  ✅ Reliability validation completed');
  }

  async validateHealthChecks() {
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    const healthChecks = (composeContent.match(/healthcheck:/g) || []).length;
    const criticalServices = ['n8n', 'postgresql', 'redis', 'chromadb', 'mcp-main'].filter(service =>
      composeContent.includes(`${service}:`)
    );

    let healthCheckIssues = [];

    if (healthChecks < criticalServices.length) {
      healthCheckIssues.push(`Only ${healthChecks}/${criticalServices.length} critical services have health checks`);
    }

    // Validate health check intervals
    const healthCheckConfigs = composeContent.match(/interval:\s*\d+s/g) || [];
    const longIntervals = healthCheckConfigs.filter(config => {
      const seconds = parseInt(config.match(/\d+/)[0]);
      return seconds > 60;
    });

    if (longIntervals.length > 0) {
      healthCheckIssues.push('Some health check intervals are too long (>60s)');
    }

    if (healthCheckIssues.length > 0) {
      this.warnings.push(...healthCheckIssues);
    }

    this.recordValidation('health_checks', healthCheckIssues.length === 0,
      `${healthChecks} services have health checks configured`);
  }

  async validateErrorHandling() {
    // Check shared error handling implementation
    const errorHandlingPath = path.join(__dirname, 'mcp-servers/shared/error-handling.js');
    
    if (!(await fs.pathExists(errorHandlingPath))) {
      this.criticalIssues.push('Shared error handling module not found');
      return;
    }

    const errorHandlingContent = await fs.readFile(errorHandlingPath, 'utf8');
    
    let errorHandlingIssues = [];

    // Check for circuit breaker implementation
    if (!errorHandlingContent.includes('CircuitBreaker')) {
      errorHandlingIssues.push('Circuit breaker not implemented in error handling');
    }

    // Check for structured logging
    if (!errorHandlingContent.includes('winston') && !errorHandlingContent.includes('logger')) {
      errorHandlingIssues.push('Structured logging not implemented');
    }

    // Check for retry mechanisms
    if (!errorHandlingContent.includes('retry') && !errorHandlingContent.includes('exponential')) {
      errorHandlingIssues.push('Retry mechanisms not implemented');
    }

    if (errorHandlingIssues.length > 0) {
      this.warnings.push(...errorHandlingIssues);
    }

    this.recordValidation('error_handling', errorHandlingIssues.length === 0,
      'Error handling implementation validated');
  }

  async validateCircuitBreakers() {
    const errorHandlingPath = path.join(__dirname, 'mcp-servers/shared/error-handling.js');
    
    if (await fs.pathExists(errorHandlingPath)) {
      const content = await fs.readFile(errorHandlingPath, 'utf8');
      
      const hasCircuitBreaker = content.includes('circuit-breaker') || content.includes('CircuitBreaker');
      const hasConfiguration = content.includes('CIRCUIT_BREAKER_CONFIGS');
      const hasMetrics = content.includes('metrics') || content.includes('getHealthCheck');

      let score = 0;
      if (hasCircuitBreaker) score++;
      if (hasConfiguration) score++;
      if (hasMetrics) score++;

      this.recordValidation('circuit_breakers', score >= 2,
        `Circuit breaker implementation: ${score}/3 features present`);
    } else {
      this.recordValidation('circuit_breakers', false, 'Circuit breaker implementation not found');
    }
  }

  async validateMonitoring() {
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    let monitoringScore = 0;
    let monitoringFeatures = [];

    // Check for Prometheus
    if (composeContent.includes('prometheus')) {
      monitoringScore++;
      monitoringFeatures.push('Prometheus');
    }

    // Check for Grafana
    if (composeContent.includes('grafana')) {
      monitoringScore++;
      monitoringFeatures.push('Grafana');
    }

    // Check for custom monitoring profiles
    if (composeContent.includes('profiles:') && composeContent.includes('monitoring')) {
      monitoringScore++;
      monitoringFeatures.push('Monitoring profiles');
    }

    // Check for health monitor
    if (await fs.pathExists(path.join(__dirname, 'mcp-health-monitor.js'))) {
      monitoringScore++;
      monitoringFeatures.push('Custom health monitor');
    }

    this.recordValidation('monitoring_setup', monitoringScore >= 2,
      `Monitoring features: ${monitoringFeatures.join(', ')} (${monitoringScore}/4)`);
  }

  async validateOperationalReadiness() {
    logger.info('  🎯 Validating operational readiness...');

    await this.validateDocumentation();
    await this.validateLogging();
    await this.validateBackupStrategy();
    await this.validateDeploymentProcess();

    logger.info('  ✅ Operational readiness validation completed');
  }

  async validateDocumentation() {
    const requiredDocs = [
      'README.md',
      'CLAUDE.md',
      'docker-compose.yml'
    ];

    let missingDocs = [];
    let docQualityScore = 0;

    for (const doc of requiredDocs) {
      const docPath = path.join(__dirname, doc);
      if (await fs.pathExists(docPath)) {
        const content = await fs.readFile(docPath, 'utf8');
        if (content.length > 500) { // Basic quality check
          docQualityScore++;
        }
      } else {
        missingDocs.push(doc);
      }
    }

    if (missingDocs.length > 0) {
      this.warnings.push(`Missing documentation: ${missingDocs.join(', ')}`);
    }

    this.recordValidation('documentation', missingDocs.length === 0,
      `${docQualityScore}/${requiredDocs.length} documentation files present and substantial`);
  }

  async validateLogging() {
    let loggingIssues = [];

    // Check for log volume mounts
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    const logMounts = (composeContent.match(/logs:/g) || []).length;
    if (logMounts < 5) {
      loggingIssues.push('Insufficient log volume mounts configured');
    }

    // Check MCP servers for logging implementation
    const mcpServerPaths = [
      'mcp-servers/manifesto-context/server.js',
      'mcp-servers/political-content/server.js'
    ];

    let serversWithLogging = 0;
    for (const serverPath of mcpServerPaths) {
      const fullPath = path.join(__dirname, serverPath);
      if (await fs.pathExists(fullPath)) {
        const content = await fs.readFile(fullPath, 'utf8');
        if (content.includes('console.log') || content.includes('logger') || content.includes('winston')) {
          serversWithLogging++;
        }
      }
    }

    if (serversWithLogging < mcpServerPaths.length) {
      loggingIssues.push('Not all MCP servers implement logging');
    }

    if (loggingIssues.length > 0) {
      this.warnings.push(...loggingIssues);
    }

    this.recordValidation('logging_configuration', loggingIssues.length === 0,
      `${serversWithLogging}/${mcpServerPaths.length} MCP servers have logging configured`);
  }

  async validateBackupStrategy() {
    let backupIssues = [];

    // Check for persistent volumes
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    const persistentVolumes = ['postgres_data', 'chromadb_data', 'n8n_data'];
    const configuredVolumes = persistentVolumes.filter(volume => 
      composeContent.includes(volume)
    );

    if (configuredVolumes.length < persistentVolumes.length) {
      backupIssues.push(`Only ${configuredVolumes.length}/${persistentVolumes.length} data volumes configured for backup`);
    }

    if (backupIssues.length > 0) {
      this.warnings.push(...backupIssues);
    }

    this.recordValidation('backup_strategy', backupIssues.length === 0,
      `${configuredVolumes.length}/${persistentVolumes.length} data volumes configured`);
  }

  async validateDeploymentProcess() {
    let deploymentIssues = [];

    // Check for environment file template
    if (!(await fs.pathExists(path.join(__dirname, '.env.example')))) {
      deploymentIssues.push('No .env.example file for deployment configuration');
    }

    // Check for production profiles
    const dockerComposePath = path.join(__dirname, 'docker-compose.yml');
    const composeContent = await fs.readFile(dockerComposePath, 'utf8');

    if (!composeContent.includes('profiles:')) {
      deploymentIssues.push('No deployment profiles configured');
    }

    if (deploymentIssues.length > 0) {
      this.warnings.push(...deploymentIssues);
    }

    this.recordValidation('deployment_process', deploymentIssues.length === 0,
      'Deployment configuration reviewed');
  }

  async validateIntegrations() {
    logger.info('  🔗 Running integration tests...');

    try {
      await this.healthMonitor.initialize();
      const healthReport = await this.healthMonitor.performFullHealthScan();
      
      const healthyServers = Object.values(healthReport.summary).reduce((sum, count) => 
        sum + (count || 0), 0
      ) - (healthReport.summary.unhealthy || 0) - (healthReport.summary.critical_failures || 0);
      
      const totalServers = Object.values(healthReport.summary).reduce((sum, count) => sum + (count || 0), 0);
      
      this.recordValidation('health_monitoring', healthyServers >= totalServers * 0.8,
        `${healthyServers}/${totalServers} servers healthy`);

      // Run basic integration tests
      const integrationResults = await this.integrationTester.runAllTests();
      
      this.recordValidation('integration_tests', integrationResults.success_rate >= 80,
        `${integrationResults.success_rate}% integration tests passed`);

    } catch (error) {
      this.criticalIssues.push(`Integration validation failed: ${error.message}`);
      this.recordValidation('integration_tests', false, error.message);
    }

    logger.info('  ✅ Integration validation completed');
  }

  async validateCompliance() {
    logger.info('  📋 Validating compliance requirements...');

    await this.validateDataGovernance();
    await this.validateAuditTrails();
    await this.validateAccessControls();

    logger.info('  ✅ Compliance validation completed');
  }

  async validateDataGovernance() {
    let governanceIssues = [];

    // Check data retention policies
    const mcpServerPaths = [
      'mcp-servers/quality-control/server.js',
      'mcp-servers/research-integration/server.js'
    ];

    let serversWithRetention = 0;
    for (const serverPath of mcpServerPaths) {
      const fullPath = path.join(__dirname, serverPath);
      if (await fs.pathExists(fullPath)) {
        const content = await fs.readFile(fullPath, 'utf8');
        if (content.includes('cleanup') || content.includes('retention') || content.includes('DELETE')) {
          serversWithRetention++;
        }
      }
    }

    if (serversWithRetention < mcpServerPaths.length * 0.5) {
      governanceIssues.push('Data retention policies not implemented in most servers');
    }

    if (governanceIssues.length > 0) {
      this.warnings.push(...governanceIssues);
    }

    this.recordValidation('data_governance', governanceIssues.length === 0,
      `${serversWithRetention}/${mcpServerPaths.length} servers have data retention policies`);
  }

  async validateAuditTrails() {
    // Check for audit logging in database schema
    const dbInitPath = path.join(__dirname, 'database/init-db.sql');
    
    if (await fs.pathExists(dbInitPath)) {
      const dbContent = await fs.readFile(dbInitPath, 'utf8');
      
      const hasAuditTables = dbContent.includes('audit') || dbContent.includes('log');
      const hasTimestamps = dbContent.includes('timestamp') || dbContent.includes('created_at');
      
      this.recordValidation('audit_trails', hasAuditTables && hasTimestamps,
        `Audit trail implementation: ${hasAuditTables ? 'tables' : 'no tables'}, ${hasTimestamps ? 'timestamps' : 'no timestamps'}`);
    } else {
      this.recordValidation('audit_trails', false, 'Database initialization script not found');
    }
  }

  async validateAccessControls() {
    let accessControlIssues = [];

    // Check authentication configuration
    if (!process.env.N8N_BASIC_AUTH_ACTIVE) {
      accessControlIssues.push('N8N authentication not configured');
    }

    // Check API key protection
    const requiredApiKeys = ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY'];
    const missingApiKeys = requiredApiKeys.filter(key => !process.env[key]);
    
    if (missingApiKeys.length > 0) {
      accessControlIssues.push(`Missing API keys: ${missingApiKeys.join(', ')}`);
    }

    if (accessControlIssues.length > 0) {
      this.warnings.push(...accessControlIssues);
    }

    this.recordValidation('access_controls', accessControlIssues.length === 0,
      `Access control configuration reviewed`);
  }

  recordValidation(testName, success, message, metadata = {}) {
    this.validationResults.push({
      test: testName,
      success,
      message,
      timestamp: new Date().toISOString(),
      metadata
    });

    const status = success ? '✅' : '❌';
    logger.info(`    ${status} ${testName}: ${message}`);
  }

  async generateValidationReport(validationDuration) {
    const totalTests = this.validationResults.length;
    const passedTests = this.validationResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = Math.round((passedTests / totalTests) * 100);

    const productionReady = this.criticalIssues.length === 0 && successRate >= 90;

    const report = {
      timestamp: new Date().toISOString(),
      validation_duration: Math.round(validationDuration),
      production_ready: productionReady,
      readiness_score: this.calculateReadinessScore(),
      summary: {
        total_tests: totalTests,
        passed_tests: passedTests,
        failed_tests: failedTests,
        success_rate: successRate,
        critical_issues: this.criticalIssues.length,
        warnings: this.warnings.length
      },
      critical_issues: this.criticalIssues,
      warnings: this.warnings,
      validation_results: this.validationResults,
      recommendations: this.generateRecommendations(productionReady),
      next_steps: this.generateNextSteps(productionReady)
    };

    // Save detailed report
    const reportPath = path.join(__dirname, 'validation-reports', `production-readiness-${Date.now()}.json`);
    await fs.ensureDir(path.dirname(reportPath));
    await fs.writeJson(reportPath, report, { spaces: 2 });

    // Generate summary report
    const summaryPath = path.join(__dirname, 'validation-reports', `production-summary-${Date.now()}.md`);
    const summaryMarkdown = this.generateSummaryMarkdown(report);
    await fs.writeFile(summaryPath, summaryMarkdown);

    logger.info(`\n📊 Validation reports generated:`);
    logger.info(`  Detailed: ${reportPath}`);
    logger.info(`  Summary: ${summaryPath}`);

    return report;
  }

  calculateReadinessScore() {
    const weights = {
      critical_multiplier: 0.8, // Critical issues heavily impact score
      warning_multiplier: 0.95, // Warnings have moderate impact
      success_rate_multiplier: 1.0
    };

    let baseScore = 100;
    
    // Deduct for critical issues
    baseScore *= Math.pow(weights.critical_multiplier, this.criticalIssues.length);
    
    // Deduct for warnings
    baseScore *= Math.pow(weights.warning_multiplier, this.warnings.length);
    
    // Factor in success rate
    const totalTests = this.validationResults.length;
    const passedTests = this.validationResults.filter(r => r.success).length;
    const successRate = passedTests / totalTests;
    
    baseScore *= successRate * weights.success_rate_multiplier;

    return Math.round(Math.max(0, baseScore));
  }

  generateRecommendations(productionReady) {
    const recommendations = [];

    if (!productionReady) {
      recommendations.push({
        priority: 'CRITICAL',
        category: 'Blocking Issues',
        title: 'Address Critical Issues Before Deployment',
        description: 'Critical issues must be resolved before production deployment',
        actions: this.criticalIssues
      });
    }

    if (this.warnings.length > 0) {
      recommendations.push({
        priority: 'HIGH', 
        category: 'Performance & Reliability',
        title: 'Address Configuration Warnings',
        description: 'Resolve warnings to improve system reliability',
        actions: this.warnings.slice(0, 5) // Top 5 warnings
      });
    }

    // Performance recommendations
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Performance Optimization',
      title: 'Optimize for 99.9% Uptime',
      description: 'Implement additional reliability measures',
      actions: [
        'Set up automated health monitoring with alerts',
        'Configure load balancing for critical services',
        'Implement automated backup strategies',
        'Set up performance monitoring dashboards',
        'Create incident response procedures'
      ]
    });

    // Security recommendations
    recommendations.push({
      priority: 'MEDIUM',
      category: 'Security Hardening',
      title: 'Enhance Security Posture',
      description: 'Additional security measures for production',
      actions: [
        'Enable TLS/SSL for all external communications',
        'Implement API rate limiting',
        'Set up intrusion detection systems',
        'Regular security audits and updates',
        'Implement secrets rotation policies'
      ]
    });

    return recommendations;
  }

  generateNextSteps(productionReady) {
    if (productionReady) {
      return [
        '🚀 System is production ready!',
        '📊 Deploy monitoring and alerting systems',
        '🔄 Set up automated deployment pipeline',
        '📋 Create operational runbooks',
        '🎯 Begin production deployment'
      ];
    } else {
      return [
        '🔧 Resolve all critical issues identified',
        '⚠️ Address high-priority warnings',
        '🧪 Re-run validation tests',
        '📝 Update configuration based on recommendations',
        '🔄 Repeat validation until production ready'
      ];
    }
  }

  generateSummaryMarkdown(report) {
    const statusIcon = report.production_ready ? '✅' : '❌';
    const scoreColor = report.readiness_score >= 90 ? '🟢' : report.readiness_score >= 70 ? '🟡' : '🔴';

    let markdown = `# MCP Ecosystem Production Readiness Report\n\n`;
    markdown += `**Status:** ${statusIcon} ${report.production_ready ? 'PRODUCTION READY' : 'NOT READY'}\n`;
    markdown += `**Readiness Score:** ${scoreColor} ${report.readiness_score}/100\n`;
    markdown += `**Generated:** ${report.timestamp}\n\n`;

    markdown += `## Summary\n\n`;
    markdown += `- **Total Tests:** ${report.summary.total_tests}\n`;
    markdown += `- **Success Rate:** ${report.summary.success_rate}%\n`;
    markdown += `- **Critical Issues:** ${report.summary.critical_issues}\n`;
    markdown += `- **Warnings:** ${report.summary.warnings}\n\n`;

    if (report.critical_issues.length > 0) {
      markdown += `## 🚨 Critical Issues\n\n`;
      report.critical_issues.forEach(issue => {
        markdown += `- ${issue}\n`;
      });
      markdown += '\n';
    }

    if (report.warnings.length > 0) {
      markdown += `## ⚠️ Warnings\n\n`;
      report.warnings.slice(0, 10).forEach(warning => {
        markdown += `- ${warning}\n`;
      });
      markdown += '\n';
    }

    markdown += `## 📋 Next Steps\n\n`;
    report.next_steps.forEach(step => {
      markdown += `- ${step}\n`;
    });

    return markdown;
  }
}

// Command line interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new ProductionReadinessValidator();
  
  validator.validateProductionReadiness()
    .then(report => {
      console.log('\n' + '='.repeat(60));
      console.log(`🎯 PRODUCTION READINESS: ${report.production_ready ? 'READY ✅' : 'NOT READY ❌'}`);
      console.log(`📊 READINESS SCORE: ${report.readiness_score}/100`);
      console.log(`📈 SUCCESS RATE: ${report.summary.success_rate}%`);
      console.log(`🚨 CRITICAL ISSUES: ${report.summary.critical_issues}`);
      console.log(`⚠️  WARNINGS: ${report.summary.warnings}`);
      console.log('='.repeat(60));
      
      if (!report.production_ready) {
        console.log('\n🔧 CRITICAL ISSUES TO RESOLVE:');
        report.critical_issues.forEach(issue => console.log(`  - ${issue}`));
      }
      
      process.exit(report.production_ready ? 0 : 1);
    })
    .catch(error => {
      logger.error('Production readiness validation failed:', error);
      process.exit(1);
    });
}

export default ProductionReadinessValidator;