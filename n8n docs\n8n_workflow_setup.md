n8n_workflow_setup.md

Hello there! I've put together a comprehensive plan for your n8n workflow, designed to intelligently process your documents using your AI agent via the MCP server. This document is structured to be a clear guide for your workflow-building AI agent, detailing every step from initial setup to the final output and notification.

This plan focuses on creating a robust, intelligent pipeline that will:

Automatically detect new Markdown files you place in a Google Drive input folder.

Leverage your Gemini/Claude AI agent (via your MCP server) to understand the "spirit and intention" of each document within the context of your manifesto.

Enable the AI to decide on specific actions: editing, combining with other documents, generating white papers, or creating entirely new documents.

Utilize a vector database (ChromaDB) for Retrieval Augmented Generation (RAG), allowing your AI to intelligently find and use relevant information from your entire document collection when performing complex tasks.

Convert the AI-generated Markdown output into polished Word DOCX files using CloudConvert.

Save these DOCX files to a designated Google Drive output folder.

Send you an email notification upon completion.

Let's dive into the architecture and detailed steps!

Overall Workflow Architecture
At a high level, your n8n workflow will operate in several distinct stages, with your AI agent and the MCP server acting as the central intelligence hub:

Prerequisites & Initial Setup: One-time setup of external services (Google Drive folders, CloudConvert, ChromaDB, AI models, n8n MCP server).

File Ingestion: Detects new Markdown files in your "political_in" Google Drive folder.

AI Analysis & Task Delegation: Your AI agent reads the document, understands its intent (guided by your manifesto), and suggests a specific action (edit, combine, whitepaper, create new).

AI-Driven Document Manipulation: Based on the AI's suggestion, the workflow branches to perform the actual content generation or modification, often leveraging RAG to pull in other relevant documents.

DOCX Conversion & Output: Converts the final Markdown content into a Word DOCX file and saves it to your "political_out" Google Drive folder.

Notification: Sends you an email confirmation.

There will also be a separate, periodic workflow for RAG Indexing to keep your vector database updated with all your documents.

Detailed Workflow Plan
Stage 0: Prerequisites & Initial Setup (Before Building the Workflow)
This stage outlines what needs to be in place before your AI agent can successfully build and run the n8n workflow.

Google Drive Setup:

Input Folder: Create a Google Drive folder named political_in. This is where you will manually place all your pre-converted Markdown (.md) files. Get its Folder ID.

Output Folder: Create another Google Drive folder named political_out. This is where the final DOCX files will be saved. Get its Folder ID.

Manifesto File: Create your manifesto as a Markdown (.md) or plain text (.txt) file. Make it as clear and comprehensive as possible, outlining your core beliefs, policy goals, mission, and desired tone. Upload this file to a stable, permanent location in your Google Drive (e.g., directly in "My Drive" or a dedicated "Manifesto" folder). Get its File ID.

N8n Google Drive Credential: Ensure you have an OAuth2 credential configured in n8n that grants access to your Google Drive.

CloudConvert Setup:

Account & API Key: Sign up for a CloudConvert account and obtain your API Key. CloudConvert offers a free tier for testing.

N8n CloudConvert Credential: You'll need to create a credential in n8n for CloudConvert using your API Key.

Community Node: Your workflow-building AI agent should install the @cloudconvert/n8n-nodes-cloudconvert community node.

AI Models (Gemini/Claude) & MCP Server Setup:

API Keys: Ensure you have API keys for your chosen AI models (Gemini 2.5 Flash, Claude 4 Sonnet).

MCP Server Running: Confirm your n8n MCP server is set up and running, accessible via a URL (e.g., http://localhost:8000/sse if self-hosted, or a public URL if deployed).

N8n MCP Server Credential: You'll need an n8n credential for connecting to your MCP server.

MCP Tools Definition: This is crucial. On your MCP server, you will need to define the following tools that your AI agent will call. These tools will correspond to specific n8n workflows that perform the actual AI interactions.

document_intent_analyzer: For initial analysis and action suggestion.

document_editor: For editing document content.

document_combiner: For combining documents.

whitepaper_generator: For generating white papers.

manifesto_doc_creator: For creating new documents based on your manifesto.

retrieve_relevant_documents: For querying the vector database (RAG).

How to define these tools: Each of these will likely be a separate n8n workflow that starts with an MCP Server Trigger node. The MCP Server Trigger node allows you to define the Tool Name, Description, and Parameters (input schema) that your AI agent will see. The rest of that n8n workflow then implements the logic for that tool.

Vector Database (ChromaDB) Setup for RAG:

ChromaDB Instance: Set up a ChromaDB instance. The easiest way is via Docker: docker run -p 8000:8000 chromadb/chroma. Note its host and port (e.g., http://localhost:8000).

Collection Creation: You'll need to create a collection in ChromaDB to store your document embeddings. You can do this via ChromaDB's API (e.g., POST http://your-chromadb-host:8000/api/v1/collections with body {"name": "political_documents", "embedding_function": "default"}).

Embedding Service API Key: You'll need an API key for an embedding model (e.g., OpenAI's text-embedding-ada-002, Google's Vertex AI embeddings, or Cohere Embed).

Email Service Credential:

N8n Email Credential: Configure an email credential in n8n (e.g., Gmail OAuth2, SMTP) to send notifications.

Main n8n Workflow: Intelligent Document Processing
This is the primary workflow that will run when a new document is added.

Workflow Name: AI Document Processor

1. Google Drive Trigger (Start Node)
* Purpose: Initiates the workflow when a new Markdown file is placed in your input folder.
* Node: Google Drive Trigger
* Configuration:
* Authentication: Your Google Drive credential.
* Watch Folder ID: [ID of your 'political_in' folder]
* Trigger On: New File
* Binary Data: True (checked)
* Output: Binary data of the .md file, plus metadata (fileName, id, mimeType).

2. Code Node (Read Markdown from Binary)
* Purpose: Converts the incoming binary Markdown file into a usable string.
* Node: Code
* Code:
javascript const binaryData = $input.item.binary; if (binaryData && binaryData.data) { const markdownContent = Buffer.from(binaryData.data, 'base64').toString('utf8'); return [{ json: { markdownContent: markdownContent, originalFilename: $input.item.json.fileName, originalFileId: $input.item.json.id, originalMimeType: $input.item.json.mimeType } }]; } return []; 
* Output: An item with markdownContent (string), originalFilename, originalFileId, etc.

3. Google Drive Download Node (Load Manifesto)
* Purpose: Fetches your manifesto content to provide context to the AI.
* Node: Google Drive
* Operation: File: Download
* Authentication: Your Google Drive credential.
* File: By ID
* File ID: [ID of your Manifesto file]
* Binary Data: True (checked)
* Output: Binary data of the manifesto file.

4. Code Node (Read Manifesto Text)
* Purpose: Converts the manifesto's binary data into a string.
* Node: Code
* Code:
javascript const manifestoBinaryData = $input.item.binary; if (manifestoBinaryData && manifestoBinaryData.data) { const manifestoContent = Buffer.from(manifestoBinaryData.data, 'base64').toString('utf8'); return [{ json: { manifestoContent: manifestoContent } }]; } return []; 
* Output: An item containing manifestoContent as a string.

5. Set Node (Combine Document Data and Manifesto for AI Prompt)
* Purpose: Prepares a single item containing both the current document's Markdown and the manifesto for the AI prompt.
* Node: Set
* Mode: Merge
* Input 1: Output from "Code Node (Read Markdown from Binary)" (current document).
* Input 2: Output from "Code Node (Read Manifesto Text)" (manifesto).
* Configuration: Create a new item that combines the fields.
* markdownContent: {{ $node["Code Node (Read Markdown from Binary)"].json.markdownContent }}
* originalFilename: {{ $node["Code Node (Read Markdown from Binary)"].json.originalFilename }}
* originalFileId: {{ $node["Code Node (Read Markdown from Binary)"].json.originalFileId }}
* manifestoContent: {{ $node["Code Node (Read Manifesto Text)"].json.manifestoContent }}
* Output: A single item with all necessary data for the AI prompt.

6. Code Node (Prepare Input for AI Agent)
* Purpose: Constructs the detailed prompt for your Gemini/Claude agent based on the combined data.
* Node: Code
* Code:
```javascript
const item = $input.item.json;
const agentQuery = `You are an expert policy analyst, focused on systemic improvements for society. Your goal is to critically analyze the provided document, understand its core arguments, and identify its 'spirit and intention' in alignment with the overarching mission of improving societal systems (across all relevant domains like healthcare, education, social justice, economy, environment), as articulated in the following manifesto:

Manifesto:
${item.manifestoContent}

Based on your analysis and strict alignment with the manifesto, choose one of the following actions for this document: 'edit', 'combine', 'generate_whitepaper', or 'create_new_document'. If no specific action is clearly beneficial, suggest 'store_original'.

Your response must be a JSON object with the following structure:
{
"suggested_action": "edit" | "combine" | "generate_whitepaper" | "create_new_document" | "store_original",
"action_details": {
// Details specific to the action:
// If 'edit': { "edit_instructions": "Detailed instructions for modification." }
// If 'combine': { "combination_purpose": "Purpose of combining", "keywords_for_retrieval": ["keyword1", "keyword2"] }
// If 'generate_whitepaper': { "whitepaper_topic": "Topic", "target_audience": "Audience", "key_themes": ["theme1", "theme2"] }
// If 'create_new_document': { "new_document_topic": "Topic", "new_document_type": "Type (e.g., 'policy brief')", "creation_focus": "Specific focus area" }
// If 'store_original': { "reason": "Reason for no specific action." }
},
"analysis_summary": "Brief summary of document's content and its alignment with manifesto.",
"confidence_score": 0.0-1.0 // AI's confidence in its suggestion
}

Document Title: ${item.originalFilename}

Document Content:
${item.markdownContent}
`;

    return [{
        json: {
            agent_query: agentQuery,
            document_context: {
                originalFilename: item.originalFilename,
                fileId: item.originalFileId
            }
        }
    }];
    ```
* **Output:** A JSON object with `agent_query` and `document_context`.

7. MCP Client Node (Call AI Agent for Analysis)
* Purpose: Sends the prepared prompt to your AI agent (Gemini/Claude) via your MCP server.
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: document_intent_analyzer (This tool must be defined on your MCP server, backed by an n8n workflow that calls Gemini/Claude).
* Parameters:
* query: {{ $json.agent_query }}
* Output: The structured JSON response from your AI agent (e.g., suggested_action, action_details, analysis_summary, confidence_score).

8. IF Node (Route by AI Action)
* Purpose: Directs the workflow based on the AI's suggested_action.
* Node: IF
* Conditions (add separate branches for each):
* Branch 1 (Edit): {{ $json.suggested_action === 'edit' }}
* Branch 2 (Combine): {{ $json.suggested_action === 'combine' }}
* Branch 3 (Generate Whitepaper): {{ $json.suggested_action === 'generate_whitepaper' }}
* Branch 4 (Create New Document): {{ $json.suggested_action === 'create_new_document' }}
* Branch 5 (Store Original / Review): {{ $json.suggested_action === 'store_original' || $json.confidence_score < 0.7 }} (This is your human review fallback).
* Output: Routes the current item down the appropriate branch.

Stage 3: AI-Driven Document Manipulation (Separate Sub-Workflows via MCP)
Each of these branches will call other n8n workflows exposed as MCP tools.

A. RAG Indexing Workflow (Separate n8n Workflow - Runs Periodically)
Workflow Name: RAG Document Indexer

Purpose: To build and maintain your vector database (ChromaDB) with embeddings of all your Markdown documents. This workflow runs independently of the main processing workflow.

Node 1: Manual Trigger / Schedule Trigger

Node: Manual Trigger (for initial run) or Schedule Trigger (e.g., Every Day or Every Week to keep index updated).

Node 2: Google Drive List Files

Node: Google Drive

Operation: Folder: List

Authentication: Your Google Drive credential.

Folder ID: [ID of your 'political_in' folder]

Mime Type (Optional): text/markdown

Output: List of file metadata (ID, name, etc.).

Node 3: Split In Batches

Node: Split In Batches

Batch Size: 10 (or adjust based on embedding service rate limits).

Node 4: Google Drive Download

Node: Google Drive

Operation: File: Download

Authentication: Your Google Drive credential.

File ID: {{ $json.id }}

Binary Data: True

Node 5: Code Node (Read Markdown for Embedding)

Node: Code

Code: (Similar to main workflow's read markdown code, but also preserving originalFilename and originalFileId for metadata).

Node 6: HTTP Request Node (Call Embedding Service)

Purpose: Converts Markdown text into vector embeddings.

Node: HTTP Request

Method: POST

URL: https://api.openai.com/v1/embeddings (or Google Vertex AI, Cohere, etc.)

Headers: Authorization: Bearer YOUR_EMBEDDING_API_KEY

Body (JSON): {"input": "{{ $json.markdownContent }}", "model": "text-embedding-ada-002"} (adjust model as needed).

Error Handling: Retry On Fail for robustness.

Output: The embedding vector.

Node 7: HTTP Request Node (Add to ChromaDB)

Purpose: Stores the document content, its embedding, and metadata in ChromaDB.

Node: HTTP Request

Method: POST

URL: http://your_chromadb_host:8000/api/v1/collections/political_documents/add

Body (JSON):

{
  "embeddings": [{{ $json.data[0].embedding }}],
  "documents": ["{{ $json.markdownContent }}"],
  "metadatas": [{
    "originalFilename": "{{ $json.originalFilename }}",
    "fileId": "{{ $json.originalFileId }}"
  }],
  "ids": ["{{ $json.originalFileId }}"]
}

Error Handling: Implement robust error handling (e.g., Continue On Fail and log errors).

B. RAG Retrieval Tool Workflow (Separate n8n Workflow - Exposed as MCP Tool)
Workflow Name: Retrieve Relevant Documents Tool

Purpose: This workflow is called by your main AI agent when it needs to find relevant documents from your ChromaDB.

Node 1: MCP Server Trigger

Node: MCP Server Trigger

Tool Name: retrieve_relevant_documents

Description: "Retrieves relevant documents from the knowledge base based on a query or keywords."

Parameters (JSON Schema):

{
  "type": "object",
  "properties": {
    "query": { "type": "string", "description": "The search query or keywords to find relevant documents." },
    "limit": { "type": "number", "description": "Maximum number of documents to retrieve.", "default": 5 }
  },
  "required": ["query"]
}

Node 2: HTTP Request Node (Embed Query)

Purpose: Gets the vector embedding for the incoming query from the AI.

Node: HTTP Request

Method: POST

URL: https://api.openai.com/v1/embeddings (same embedding service as indexing).

Headers: Authorization: Bearer YOUR_EMBEDDING_API_KEY.

Body (JSON): {"input": "{{ $json.query }}", "model": "text-embedding-ada-002"}.

Output: The embedding vector.

Node 3: HTTP Request Node (Query ChromaDB)

Purpose: Queries your ChromaDB collection for similar documents.

Node: HTTP Request

Method: POST

URL: http://your_chromadb_host:8000/api/v1/collections/political_documents/query

Body (JSON):

{
  "query_embeddings": [{{ $json.data[0].embedding }}],
  "n_results": {{ $json.limit || 5 }},
  "include": ["documents", "metadatas"]
}

Output: Returns an array of matching documents (content and metadata).

Node 4: Code Node (Format Retrieved Docs)

Purpose: Extracts and formats the markdownContent from the ChromaDB response into a clean array.

Node: Code

Code:

const chromaResults = $input.item.json.data.results; // Adjust path if needed
const relevantDocuments = [];
if (chromaResults && chromaResults.documents && chromaResults.documents.length > 0) {
    for (let i = 0; i < chromaResults.documents[0].length; i++) {
        relevantDocuments.push({
            content: chromaResults.documents[0][i],
            filename: chromaResults.metadatas[0][i].originalFilename || 'unknown_filename.md',
            fileId: chromaResults.metadatas[0][i].fileId || 'unknown_file_id'
        });
    }
}
return [{ json: { relevantDocuments: relevantDocuments } }];

Output: { json: { relevantDocuments: [{ content: "...", filename: "..." }, ...] } }

Node 5: Respond to MCP

Node: Respond to MCP

Purpose: Sends the retrieved documents back to the calling workflow.

Configuration: Select the output of the "Code Node (Format Retrieved Docs)".

C. Main Workflow Branches (Stage 3 Continued)
These branches are part of your main AI Document Processor workflow.

Branch 1: Edit Document
* Node 1: MCP Client Node (Call AI Editor Tool)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: document_editor (This tool on your MCP server would call Gemini/Claude with the document and edit instructions).
* Parameters:
* document_content: {{ $json.markdownContent }} (the original document content).
* edit_instructions: {{ $json.action_details.edit_instructions }} (from AI's initial analysis).
* manifesto_content: {{ $json.manifestoContent }} (for context).
* Output: Edited Markdown content (finalMarkdownContent).

Branch 2: Combine Documents
* Node 1: MCP Client Node (Call RAG Retrieval Tool)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: retrieve_relevant_documents
* Parameters:
* query: {{ $json.action_details.combination_purpose }} (or keywords_for_retrieval).
* limit: 10 (or a dynamic value from AI's action_details).
* Output: relevantDocuments from the RAG tool.
* Node 2: Code Node (Consolidate Content for Combiner AI)
* Node: Code
* Purpose: Gathers all content to be combined (original document + retrieved documents).
* Code:
javascript const originalDoc = $json.markdownContent; const retrievedDocs = $json.relevantDocuments.map(doc => doc.content); const allContent = [originalDoc, ...retrievedDocs].join('\n\n---\n\n'); // Join with a separator return [{ json: { allContent: allContent, combinationPurpose: $json.action_details.combination_purpose } }]; 
* Node 3: MCP Client Node (Call AI Combiner Tool)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: document_combiner
* Parameters:
* documents_to_combine_content: {{ $json.allContent }}.
* combination_purpose: {{ $json.combinationPurpose }}.
* manifesto_content: {{ $json.manifestoContent }}.
* Output: Combined Markdown content (finalMarkdownContent).

Branch 3: Generate White Paper
* Node 1: MCP Client Node (Call RAG Retrieval Tool - Optional)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: retrieve_relevant_documents
* Parameters: query: {{ $json.action_details.whitepaper_topic }} (or key_themes).
* Output: relevantDocuments.
* Node 2: Code Node (Consolidate Content for Whitepaper AI)
* Node: Code
* Purpose: Gathers content for the white paper.
* Code: Similar to combine, but might include original document and retrieved.
* Node 3: MCP Client Node (Call AI Whitepaper Generator Tool)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: whitepaper_generator
* Parameters:
* source_documents_content: {{ $json.allContent }}.
* whitepaper_topic: {{ $json.action_details.whitepaper_topic }}.
* target_audience: {{ $json.action_details.target_audience }}.
* key_themes: {{ $json.action_details.key_themes }}.
* manifesto_content: {{ $json.manifestoContent }}.
* Output: White paper content in Markdown (finalMarkdownContent).

Branch 4: Create New Documents
* Node 1: MCP Client Node (Call RAG Retrieval Tool)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: retrieve_relevant_documents
* Parameters: query: {{ $json.action_details.new_document_topic }}.
* Output: relevantDocuments.
* Node 2: Code Node (Consolidate Content for New Document AI)
* Node: Code
* Purpose: Gathers content for the new document.
* Code: Similar to combine.
* Node 3: MCP Client Node (Call AI New Document Creator Tool)
* Node: MCP Client
* Credential: Your MCP server credential.
* Tool/Action: Execute Tool
* Tool: manifesto_doc_creator
* Parameters:
* source_documents_content: {{ $json.allContent }}.
* new_document_topic: {{ $json.action_details.new_document_topic }}.
* new_document_type: {{ $json.action_details.new_document_type }}.
* creation_focus: {{ $json.action_details.creation_focus }}.
* manifesto_content: {{ $json.manifestoContent }}.
* Output: Newly generated Markdown content (finalMarkdownContent).

Branch 5: Store Original / Human Review
* Node 1: Code Node (Prepare for Review)
* Node: Code
* Purpose: Format details for a human review notification.
* Code: Gather originalFilename, analysis_summary, suggested_action, reason (if store_original), confidence_score.
* Node 2: Email Send Node (Human Review Alert)
* Node: Email Send
* Authentication: Your email credential.
* To: <EMAIL>
* Subject: n8n AI Document Review Needed: {{ $json.originalFilename }}
* Body: Provide details from the previous code node, including why it needs review.

Stage 4: Markdown to DOCX Conversion & Output (CloudConvert)
This stage follows all AI manipulation branches. You'll connect the output of each "final Markdown content" node (from Stage 3 branches) to this stage.

1. Code Node (Prepare for CloudConvert)
* Purpose: Takes the finalMarkdownContent and constructs the outputFilename.
* Node: Code
* Code:
```javascript
const item = input.item.json;
// Example: Dynamically create a filename based on AI action and original name
let outputFilename = `AI_Processed_${item.originalFilename.replace('.md', '')}.docx`;
if (item.suggested\_action === 'edit') {
outputFilename = \`Edited\_{item.originalFilename.replace('.md', '')}.docx`;
} else if (item.suggested_action === 'combine') {
outputFilename = `Combined_Docs_{Date.now()}.docx\`; // Or derive from combination\_purpose
} else if (item.suggested\_action === 'generate\_whitepaper') {
outputFilename = \`Whitepaper\_{item.action_details.whitepaper_topic.replace(/\s/g, '')}.docx`;
} else if (item.suggested_action === 'create_new_document') {
outputFilename = `New_Doc${item.action_details.new_document_topic.replace(/\s/g, '_')}.docx`;
}

    return [{
        json: {
            finalMarkdownContent: item.finalMarkdownContent, // This comes from the AI manipulation branch
            outputFilename: outputFilename,
            conversionJobTitle: `AI Processed: ${item.originalFilename}`
        }
    }];
    ```
* **Output:** `finalMarkdownContent`, `outputFilename`, `conversionJobTitle`.

2. CloudConvert Node
* Purpose: Converts the Markdown content to a DOCX file.
* Node: CloudConvert (Community Node: @cloudconvert/n8n-nodes-cloudconvert)
* Authentication: Your CloudConvert API Key credential.
* Configuration:
* Resource: Files
* Operation: Convert File
* Input File: Select the field containing your Markdown content, e.g., {{ $json.finalMarkdownContent }}.
* Input Format: md
* Output Format: docx
* Filename: {{ $json.outputFilename }}
* Title: {{ $json.conversionJobTitle }}
* Wait for Conversion: True (checked).
* Output: Binary data of the converted .docx file.

3. Google Drive Node (Upload DOCX)
* Purpose: Uploads the newly created DOCX file to your output folder.
* Node: Google Drive
* Operation: File: Upload
* Authentication: Your Google Drive credential.
* File Name: {{ $json.outputFilename }}.
* Parent Folder ID: [ID of your 'political_out' folder]
* Input Data Field: The binary output from the CloudConvert node.
* Output: Metadata of the uploaded file, including its id and webViewLink (the shareable URL).

Stage 5: Notification
1. Email Send Node
* Purpose: Notifies you of successful completion and provides a link to the new document.
* Node: Email Send
* Authentication: Your email credential.
* To: <EMAIL>
* Subject: n8n Document Processing Complete: {{ $json.outputFilename }}
* Body (HTML or Text):
html <p>Hello,</p> <p>The document "<strong>{{ $json.originalFilename }}</strong>" has been processed by the AI agent.</p> <p><strong>AI Action:</strong> {{ $json.suggested_action }}</p> <p><strong>Output File:</strong> {{ $json.outputFilename }}</p> <p>You can find the processed document here: <a href="{{ $json.webViewLink }}">View Document in Google Drive</a></p> <p><strong>AI Analysis Summary:</strong></p> <p>{{ $json.analysis_summary }}</p> <p>Best regards,<br>Your n8n AI Workflow</p> 
Note: The webViewLink is directly available from the Google Drive Upload node's output.

Error Handling Strategy
Implementing robust error handling is critical for a complex workflow like this.

Global Error Workflow:

Create a separate n8n workflow named Global Error Handler.

Start this workflow with an Error Trigger node.

Add an Email Send node in this workflow <NAME_EMAIL> with the details of any workflow failure (e.g., {{ $json.error.message }}, {{ $json.workflow.name }}, {{ $json.lastNodeExecuted }}).

In the Settings of your AI Document Processor workflow, set the Error workflow option to Global Error Handler.

Node-Level Retries & Fallbacks:

HTTP Request Nodes (for Embeddings, ChromaDB):

Enable Retry On Fail with a few retries (e.g., 3 retries, 5-10 second interval) for transient network issues or API rate limits.

CloudConvert Node: The community node often handles retries and waiting for conversion completion internally.

MCP Client Nodes:

Ensure the underlying MCP tools (your AI agent workflows) have their own error handling.

In the main workflow, consider adding Continue On Fail to the MCP Client nodes. Then, after the MCP Client, use an IF node to check if the AI's response was valid or if an error occurred. If an error, route to a "Human Review" branch or a specific error notification.

Human Review Branch: The "Store Original / Human Review" branch from the main IF node (Stage 2, Branch 5) acts as a critical fallback for AI uncertainty or unexpected outputs.

Key Considerations for Your Workflow-Building AI Agent
When instructing your AI agent to build this workflow, emphasize the following:

Credential Management: The AI agent must understand how to create and reference n8n credentials securely (Google Drive, CloudConvert, MCP Server, Email, Embedding Service). These should be stored as n8n credentials, not hardcoded.

Folder/File IDs: The AI agent will need to be provided with the specific Google Drive Folder IDs (political_in, political_out) and the Manifesto File ID. These can be passed as environment variables or explicit parameters.

Community Node Installation: The AI agent needs to know to install the @cloudconvert/n8n-nodes-cloudconvert community node.

MCP Tool Definitions: The AI agent needs to understand that the "AI Document Analysis" and "AI Document Manipulation" steps rely on separate n8n workflows that are exposed as tools via the MCP Server Trigger node. It must define these tools with the correct names, descriptions, and parameter schemas.

ChromaDB Host: The AI agent needs the URL/IP of your running ChromaDB instance.

Prompt Engineering: The AI agent must carefully construct the prompts for Gemini/Claude, ensuring the manifesto is included and the expected JSON output format is strictly adhered to.

Data Flow: Emphasize the importance of correctly mapping data between nodes, especially when dealing with binary data, string conversions, and JSON structures.

Error Handling: Instruct the AI agent to implement the global error handler and node-level retries as described.

Testing: Stress the importance of testing each stage of the workflow thoroughly, especially the AI interactions and conversions.

This comprehensive plan provides all the necessary details for your AI agent to build a powerful and intelligent document processing workflow in n8n. Good luck with your project!