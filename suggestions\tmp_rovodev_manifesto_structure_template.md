# Political Manifesto Structure Template

## Purpose
This manifesto serves as the core reference document for AI agents processing your political documents. It should capture your voice, values, and policy positions to ensure consistency across all generated content.

## Recommended Structure

### 1. Core Philosophy & Values
```markdown
# My Political Philosophy

## Fundamental Beliefs
- [Your core political beliefs]
- [Key principles that guide decisions]
- [Non-negotiable values]

## Vision for America
- [Long-term societal goals]
- [Ideal outcomes you're working toward]
- [Generational impact vision]
```

### 2. Policy Positions
```markdown
# Policy Framework

## Healthcare
- Universal healthcare principles
- Implementation approach
- Funding philosophy
- Quality standards

## Education
- Free education through graduate degrees
- Workforce development priorities
- Educational equity principles

## Economic Policy
- Wealth distribution philosophy
- Tax policy principles
- Business regulation approach
- Worker rights priorities

## [Additional Policy Areas]
- Environmental policy
- Foreign policy
- Criminal justice
- Technology and privacy
```

### 3. Communication Style Guide
```markdown
# Voice and Tone Guidelines

## Writing Style
- Tone: [Professional, passionate, accessible, etc.]
- Complexity: [Academic, general public, policy experts]
- Length preferences: [Detailed vs concise]
- Evidence standards: [Data-driven, anecdotal, mixed]

## Language Preferences
- Avoid: [Terms or phrases to avoid]
- Prefer: [Preferred terminology]
- Audience: [Primary audience considerations]

## Argumentation Style
- [How you prefer to structure arguments]
- [Evidence hierarchy - data, expert opinion, case studies]
- [How to handle counterarguments]
```

### 4. Strategic Priorities
```markdown
# Implementation Strategy

## Short-term Goals (1-2 years)
- [Immediate policy priorities]
- [Quick wins and momentum builders]

## Medium-term Goals (3-5 years)
- [Major policy implementations]
- [Systemic changes]

## Long-term Vision (10+ years)
- [Transformational goals]
- [Generational impact]

## Political Strategy
- [Coalition building approach]
- [Compromise philosophy]
- [Opposition engagement style]
```

### 5. AI Agent Instructions
```markdown
# Instructions for AI Document Processing

## Content Generation Rules
1. Always reference this manifesto for consistency
2. Maintain [specified tone] throughout all documents
3. Use evidence-based arguments when possible
4. Cross-reference related policies for coherence
5. Ensure feasibility considerations in all proposals

## Document Types and Approaches
- **White Papers**: Comprehensive, well-sourced, policy-focused
- **Position Papers**: Clear stance, supporting arguments, implementation outline
- **Talking Points**: Concise, memorable, action-oriented
- **Policy Briefs**: Executive summary style, decision-maker focused

## Quality Standards
- Fact-check all claims against reliable sources
- Ensure internal consistency across documents
- Maintain professional tone while preserving passion
- Include implementation considerations
- Address potential criticisms proactively
```

## Example Sections

### Sample Core Philosophy
```markdown
# My Political Philosophy

## Fundamental Beliefs
- Healthcare is a human right, not a privilege
- Education should be accessible to all, regardless of economic status
- Economic systems should serve people, not the other way around
- Government's role is to ensure equal opportunity and protect the vulnerable

## Vision for America
- A society where basic needs (healthcare, education, housing) are guaranteed
- An economy that rewards work and innovation while preventing extreme inequality
- A democracy that truly represents all citizens, not just the wealthy
- A sustainable future that preserves opportunities for coming generations
```

## Usage in Workflow
1. **Upload to Google Drive**: Place in permanent, accessible location
2. **Reference in Instructions**: AI agents will consult this for every document
3. **Update Regularly**: Keep manifesto current with evolving positions
4. **Version Control**: Track changes and maintain consistency

Would you like me to help you develop any specific sections of your manifesto, or shall we continue with the workflow design?