global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'production'
    system: 'political-document-processing'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # n8n Workflow Engine Metrics
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    scheme: http

  # MCP Servers Health and Performance
  - job_name: 'mcp-main'
    static_configs:
      - targets: ['mcp-main:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  - job_name: 'mcp-analytics-secure'
    static_configs:
      - targets: ['mcp-analytics-secure:8090']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  - job_name: 'mcp-vector-search'
    static_configs:
      - targets: ['mcp-vector-search:8088']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  - job_name: 'mcp-web-research'
    static_configs:
      - targets: ['mcp-web-research:8081']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-economic-analysis'
    static_configs:
      - targets: ['mcp-economic-analysis:8082']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-legal-analysis'
    static_configs:
      - targets: ['mcp-legal-analysis:8083']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-international-research'
    static_configs:
      - targets: ['mcp-international-research:8084']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-document-intelligence'
    static_configs:
      - targets: ['mcp-document-intelligence:8085']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-memory-context'
    static_configs:
      - targets: ['mcp-memory-context:8086']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-fact-checking'
    static_configs:
      - targets: ['mcp-fact-checking:8087']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-social-monitoring'
    static_configs:
      - targets: ['mcp-social-monitoring:8088']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-multimodal-chromadb'
    static_configs:
      - targets: ['mcp-multimodal-chromadb:8091']
    scrape_interval: 30s
    metrics_path: /metrics

  - job_name: 'mcp-voice-processing'
    static_configs:
      - targets: ['mcp-voice-processing:8092']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  - job_name: 'mcp-autonomous-ensemble'
    static_configs:
      - targets: ['mcp-autonomous-ensemble:8093']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  - job_name: 'mcp-autonomous-fact-checking'
    static_configs:
      - targets: ['mcp-fact-checking:8094']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Database Monitoring
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgresql:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    scheme: http

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # ChromaDB Vector Database
  - job_name: 'chromadb'
    static_configs:
      - targets: ['chromadb:8000']
    scrape_interval: 30s
    metrics_path: /api/v1/heartbeat
    scheme: http

  # Chat Interface Application
  - job_name: 'chat-interface'
    static_configs:
      - targets: ['chat-interface:3000']
    scrape_interval: 30s
    metrics_path: /metrics

  # System-level monitoring (Node Exporter)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Docker Container Monitoring
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Custom Analytics Endpoints
  - job_name: 'political-document-analytics'
    scrape_interval: 15s
    static_configs:
      - targets: ['mcp-analytics-secure:8090']
    metrics_path: /api/metrics/prometheus
    scheme: http
    params:
      format: ['prometheus']
    scrape_timeout: 30s
    honor_labels: true

  # High-frequency real-time metrics for critical components
  - job_name: 'realtime-document-processing'
    scrape_interval: 5s
    static_configs:
      - targets: ['mcp-analytics-secure:8090']
    metrics_path: /api/metrics/realtime
    scheme: http
    scrape_timeout: 5s