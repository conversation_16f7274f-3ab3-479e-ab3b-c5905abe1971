#!/usr/bin/env node

/**
 * Phase 3 System Testing Script
 * Tests all new MCP servers and autonomous features
 */

const axios = require('axios');
const colors = require('colors');
const Table = require('cli-table3');

// Configuration
const PHASE3_SERVERS = [
  {
    name: 'Analytics Secure',
    port: 8090,
    healthEndpoint: '/health',
    testEndpoints: [
      { path: '/api/v1/dashboard/analytics', method: 'GET', requiresAuth: true },
      { path: '/metrics', method: 'GET' }
    ],
    mcpTools: [
      'get_dashboard_analytics',
      'get_document_metrics',
      'get_system_performance',
      'get_user_behavior_analytics',
      'generate_analytics_report',
      'track_real_time_event'
    ]
  },
  {
    name: 'Multimodal ChromaDB',
    port: 8091,
    healthEndpoint: '/health',
    testEndpoints: [
      { path: '/api/v1/multimodal/stats', method: 'GET' },
      { path: '/metrics', method: 'GET' }
    ],
    mcpTools: [
      'upload_multimodal_content',
      'search_multimodal',
      'analyze_political_image',
      'get_multimodal_stats'
    ]
  },
  {
    name: 'Voice Processing',
    port: 8092,
    healthEndpoint: '/health',
    testEndpoints: [
      { path: '/api/v1/voice/analytics', method: 'GET', requiresAuth: true },
      { path: '/metrics', method: 'GET' }
    ],
    mcpTools: [
      'transcribe_political_audio',
      'start_real_time_session',
      'analyze_voice_sentiment',
      'get_transcription_history',
      'get_voice_analytics'
    ]
  },
  {
    name: 'Autonomous Ensemble',
    port: 8093,
    healthEndpoint: '/health',
    testEndpoints: [
      { path: '/api/v1/agents/status', method: 'GET' },
      { path: '/api/v1/performance', method: 'GET' },
      { path: '/metrics', method: 'GET' }
    ],
    mcpTools: [
      'submit_autonomous_task',
      'get_task_status',
      'cancel_task',
      'get_performance_metrics',
      'get_agent_status'
    ]
  },
  {
    name: 'Autonomous Fact-Checking',
    port: 8094,
    healthEndpoint: '/health',
    testEndpoints: [
      { path: '/api/v1/fact-check/sources', method: 'GET' },
      { path: '/api/v1/analytics', method: 'GET' },
      { path: '/metrics', method: 'GET' }
    ],
    mcpTools: [
      'fact_check_claim',
      'batch_fact_check',
      'verify_source_credibility',
      'get_fact_check_history',
      'monitor_real_time_claims'
    ]
  }
];

// Helper functions
async function testHealthEndpoint(server) {
  try {
    const response = await axios.get(`http://localhost:${server.port}${server.healthEndpoint}`);
    return {
      status: 'PASS',
      message: `HTTP ${response.status}`,
      details: response.data
    };
  } catch (error) {
    return {
      status: 'FAIL',
      message: error.message,
      details: error.response?.data || null
    };
  }
}

async function testMCPTool(server, toolName) {
  try {
    const response = await axios.post(`http://localhost:${server.port}/mcp/call`, {
      tool: toolName,
      arguments: getMockArguments(toolName)
    });
    return {
      status: 'PASS',
      message: 'Tool callable',
      response: response.data
    };
  } catch (error) {
    // Some tools require specific arguments or auth, so 400 errors are expected
    if (error.response?.status === 400) {
      return {
        status: 'WARN',
        message: 'Tool requires specific arguments',
        error: error.response.data
      };
    }
    return {
      status: 'FAIL',
      message: error.message,
      error: error.response?.data || null
    };
  }
}

function getMockArguments(toolName) {
  // Return appropriate mock arguments based on tool name
  const mockArgs = {
    'get_dashboard_analytics': { timeRange: '24h' },
    'get_document_metrics': { documentId: 'test-doc-1' },
    'track_real_time_event': { event: 'test_event', data: {} },
    'upload_multimodal_content': { type: 'image', description: 'test' },
    'search_multimodal': { query: 'political speech', type: 'all' },
    'transcribe_political_audio': { audioData: 'base64_mock' },
    'analyze_voice_sentiment': { audioUrl: 'http://example.com/audio.mp3' },
    'submit_autonomous_task': { type: 'test', content: 'test content' },
    'get_task_status': { taskId: 'test-task-1' },
    'fact_check_claim': { claim: 'Test claim for verification' },
    'verify_source_credibility': { url: 'http://example.com' }
  };
  
  return mockArgs[toolName] || {};
}

async function testServer(server) {
  console.log(`\n${'='.repeat(60)}`.cyan);
  console.log(`Testing ${server.name} (Port ${server.port})`.cyan.bold);
  console.log('='.repeat(60).cyan);

  const results = {
    name: server.name,
    port: server.port,
    health: null,
    endpoints: [],
    mcpTools: []
  };

  // Test health endpoint
  console.log('\n📊 Testing Health Endpoint...'.yellow);
  results.health = await testHealthEndpoint(server);
  console.log(`   Status: ${results.health.status === 'PASS' ? '✅ PASS'.green : '❌ FAIL'.red}`);
  console.log(`   Message: ${results.health.message}`);

  // Test API endpoints
  if (server.testEndpoints && server.testEndpoints.length > 0) {
    console.log('\n🔌 Testing API Endpoints...'.yellow);
    for (const endpoint of server.testEndpoints) {
      try {
        const response = await axios({
          method: endpoint.method,
          url: `http://localhost:${server.port}${endpoint.path}`
        });
        results.endpoints.push({
          endpoint: endpoint.path,
          status: 'PASS',
          statusCode: response.status
        });
        console.log(`   ${endpoint.path}: ✅ HTTP ${response.status}`.green);
      } catch (error) {
        results.endpoints.push({
          endpoint: endpoint.path,
          status: error.response?.status === 401 && endpoint.requiresAuth ? 'WARN' : 'FAIL',
          statusCode: error.response?.status || 'ERROR'
        });
        console.log(`   ${endpoint.path}: ${error.response?.status === 401 ? '⚠️  401 (Auth Required)'.yellow : '❌ FAIL'.red}`);
      }
    }
  }

  // Test MCP tools
  if (server.mcpTools && server.mcpTools.length > 0) {
    console.log('\n🛠️  Testing MCP Tools...'.yellow);
    for (const tool of server.mcpTools) {
      const result = await testMCPTool(server, tool);
      results.mcpTools.push({
        tool,
        status: result.status
      });
      
      const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️ ' : '❌';
      const statusColor = result.status === 'PASS' ? 'green' : result.status === 'WARN' ? 'yellow' : 'red';
      console.log(`   ${tool}: ${statusIcon} ${result.status}`[statusColor]);
    }
  }

  return results;
}

async function testMonitoringInfrastructure() {
  console.log(`\n${'='.repeat(60)}`.cyan);
  console.log('Testing Monitoring Infrastructure'.cyan.bold);
  console.log('='.repeat(60).cyan);

  const monitoringServices = [
    { name: 'Prometheus', port: 9090, endpoint: '/-/healthy' },
    { name: 'Grafana', port: 3001, endpoint: '/api/health' }
  ];

  const results = [];
  
  for (const service of monitoringServices) {
    try {
      const response = await axios.get(`http://localhost:${service.port}${service.endpoint}`);
      results.push({
        service: service.name,
        status: 'PASS',
        message: `Running on port ${service.port}`
      });
      console.log(`   ${service.name}: ✅ PASS (Port ${service.port})`.green);
    } catch (error) {
      results.push({
        service: service.name,
        status: 'FAIL',
        message: error.message
      });
      console.log(`   ${service.name}: ❌ FAIL - ${error.message}`.red);
    }
  }

  return results;
}

async function generateSummaryReport(testResults, monitoringResults) {
  console.log(`\n${'='.repeat(60)}`.cyan);
  console.log('PHASE 3 SYSTEM TEST SUMMARY'.cyan.bold);
  console.log('='.repeat(60).cyan);

  // Create summary table
  const table = new Table({
    head: ['Service', 'Port', 'Health', 'Endpoints', 'MCP Tools'],
    colWidths: [25, 10, 10, 15, 15]
  });

  let totalPass = 0;
  let totalFail = 0;
  let totalWarn = 0;

  testResults.forEach(result => {
    const endpointStats = result.endpoints.reduce((acc, e) => {
      acc[e.status] = (acc[e.status] || 0) + 1;
      return acc;
    }, {});

    const mcpStats = result.mcpTools.reduce((acc, t) => {
      acc[t.status] = (acc[t.status] || 0) + 1;
      return acc;
    }, {});

    totalPass += (result.health?.status === 'PASS' ? 1 : 0) + (endpointStats.PASS || 0) + (mcpStats.PASS || 0);
    totalFail += (result.health?.status === 'FAIL' ? 1 : 0) + (endpointStats.FAIL || 0) + (mcpStats.FAIL || 0);
    totalWarn += (endpointStats.WARN || 0) + (mcpStats.WARN || 0);

    table.push([
      result.name,
      result.port,
      result.health?.status === 'PASS' ? '✅'.green : '❌'.red,
      `✅ ${endpointStats.PASS || 0} ⚠️  ${endpointStats.WARN || 0} ❌ ${endpointStats.FAIL || 0}`,
      `✅ ${mcpStats.PASS || 0} ⚠️  ${mcpStats.WARN || 0} ❌ ${mcpStats.FAIL || 0}`
    ]);
  });

  console.log(table.toString());

  // Monitoring summary
  console.log('\n📊 Monitoring Infrastructure:'.yellow);
  monitoringResults.forEach(result => {
    console.log(`   ${result.service}: ${result.status === 'PASS' ? '✅ PASS'.green : '❌ FAIL'.red} - ${result.message}`);
  });

  // Overall summary
  console.log('\n📈 Overall Statistics:'.yellow);
  console.log(`   Total Tests: ${totalPass + totalFail + totalWarn}`);
  console.log(`   ✅ Passed: ${totalPass}`.green);
  console.log(`   ⚠️  Warnings: ${totalWarn}`.yellow);
  console.log(`   ❌ Failed: ${totalFail}`.red);
  console.log(`   Success Rate: ${((totalPass / (totalPass + totalFail + totalWarn)) * 100).toFixed(1)}%`);

  // Recommendations
  console.log('\n💡 Recommendations:'.yellow);
  if (totalFail > 0) {
    console.log('   - Some services are not running. Run: docker-compose up -d'.red);
    console.log('   - Check logs: docker-compose logs [service-name]'.red);
  }
  if (totalWarn > 0) {
    console.log('   - Some endpoints require authentication or specific arguments'.yellow);
    console.log('   - This is expected behavior for secured endpoints'.yellow);
  }
  if (totalPass > totalFail) {
    console.log('   - Phase 3 systems are mostly operational ✅'.green);
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Phase 3 System Tests...'.cyan.bold);
  console.log('Testing autonomous MCP servers and monitoring infrastructure\n'.gray);

  const testResults = [];
  const failures = [];

  // Test each Phase 3 server
  for (const server of PHASE3_SERVERS) {
    try {
      const result = await testServer(server);
      testResults.push(result);
    } catch (error) {
      console.error(`\n❌ Critical error testing ${server.name}: ${error.message}`.red);
      failures.push({ server: server.name, error: error.message });
    }
  }

  // Test monitoring infrastructure
  const monitoringResults = await testMonitoringInfrastructure();

  // Generate summary report
  await generateSummaryReport(testResults, monitoringResults);

  // Exit with appropriate code
  const hasFailures = failures.length > 0 || testResults.some(r => r.health?.status === 'FAIL');
  process.exit(hasFailures ? 1 : 0);
}

// Run tests
main().catch(error => {
  console.error('❌ Fatal error:'.red, error);
  process.exit(1);
});