const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const winston = require('winston');
const multer = require('multer');
const sharp = require('sharp');
const Jimp = require('jimp');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('ffmpeg-static');
const fs = require('fs').promises;
const path = require('path');
const uuid = require('uuid').v4;
const mime = require('mime');
const imageSize = require('image-size');
const OpenAI = require('openai');
const { ChromaApi, OpenAIEmbeddingFunction } = require('chromadb');
const client = require('prom-client');
const promMiddleware = require('express-prometheus-middleware');

// Configure FFmpeg
ffmpeg.setFfmpegPath(ffmpegPath);

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Configure Winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

// Prometheus metrics
const register = new client.Registry();
client.collectDefaultMetrics({ register });

const multimodalProcessingCounter = new client.Counter({
  name: 'multimodal_processing_total',
  help: 'Total number of multimodal content processed',
  labelNames: ['type', 'status']
});

const multimodalProcessingDuration = new client.Histogram({
  name: 'multimodal_processing_duration_seconds',
  help: 'Duration of multimodal content processing',
  labelNames: ['type'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
});

const vectorSearchCounter = new client.Counter({
  name: 'multimodal_vector_search_total',
  help: 'Total number of multimodal vector searches',
  labelNames: ['collection', 'modality']
});

register.registerMetric(multimodalProcessingCounter);
register.registerMetric(multimodalProcessingDuration);
register.registerMetric(vectorSearchCounter);

// Initialize Express app
const app = express();
const port = process.env.MCP_SERVER_PORT || 8091;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "blob:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "blob:"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5678'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  message: {
    error: 'Too many requests',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use(limiter);

// File upload rate limiting (stricter)
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 50, // limit file uploads
  message: {
    error: 'Too many file uploads',
    retryAfter: '15 minutes'
  }
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined', {
  stream: { write: (message) => logger.info(message.trim()) }
}));

// Prometheus metrics middleware
app.use(promMiddleware({
  metricsPath: '/metrics',
  collectDefaultMetrics: true,
  requestDurationBuckets: [0.1, 0.5, 1, 1.5, 2, 3, 5, 10],
  requestLengthBuckets: [512, 1024, 5120, 10240, 51200, 102400],
  responseLengthBuckets: [512, 1024, 5120, 10240, 51200, 102400]
}));

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
    files: 10
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'video/mp4', 'video/avi', 'video/quicktime', 'video/webm',
      'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'), false);
    }
  }
});

// Initialize ChromaDB
let chromaClient;
let embeddingFunction;

async function initializeChromaDB() {
  try {
    chromaClient = new ChromaApi({
      path: process.env.CHROMADB_URL || 'http://chromadb:8000'
    });
    
    embeddingFunction = new OpenAIEmbeddingFunction({
      openai_api_key: process.env.OPENAI_API_KEY,
      openai_model: "text-embedding-3-small"
    });
    
    // Test connection
    await chromaClient.heartbeat();
    logger.info('ChromaDB connection established successfully');
    
    // Initialize collections
    await initializeCollections();
    
  } catch (error) {
    logger.error('Failed to initialize ChromaDB:', error);
    throw error;
  }
}

async function initializeCollections() {
  try {
    // Image collection
    try {
      await chromaClient.getCollection({ name: 'political_images' });
    } catch {
      await chromaClient.createCollection({
        name: 'political_images',
        embeddingFunction,
        metadata: { 
          description: 'Political document images with CLIP embeddings',
          modality: 'image'
        }
      });
    }
    
    // Video collection
    try {
      await chromaClient.getCollection({ name: 'political_videos' });
    } catch {
      await chromaClient.createCollection({
        name: 'political_videos',
        embeddingFunction,
        metadata: { 
          description: 'Political video content with frame-based embeddings',
          modality: 'video'
        }
      });
    }
    
    // Audio/Voice collection
    try {
      await chromaClient.getCollection({ name: 'political_audio' });
    } catch {
      await chromaClient.createCollection({
        name: 'political_audio',
        embeddingFunction,
        metadata: { 
          description: 'Political audio/voice content with transcription embeddings',
          modality: 'audio'
        }
      });
    }
    
    logger.info('All multimodal collections initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize collections:', error);
    throw error;
  }
}

// Image processing functions
async function processImage(buffer, filename, metadata = {}) {
  const timer = multimodalProcessingDuration.startTimer({ type: 'image' });
  
  try {
    // Get image info
    const dimensions = imageSize(buffer);
    
    // Process with Sharp for optimization
    const processedBuffer = await sharp(buffer)
      .resize(1024, 1024, { fit: 'inside', withoutEnlargement: true })
      .jpeg({ quality: 90 })
      .toBuffer();
    
    // Generate description using OpenAI Vision
    const base64Image = processedBuffer.toString('base64');
    const visionResponse = await openai.chat.completions.create({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text", 
              text: "Analyze this political document image. Describe its content, any text visible, charts, graphs, or political symbols. Focus on factual content that would be useful for political analysis."
            },
            {
              type: "image_url",
              image_url: {
                url: `data:image/jpeg;base64,${base64Image}`
              }
            }
          ]
        }
      ],
      max_tokens: 500
    });
    
    const description = visionResponse.choices[0].message.content;
    
    // Store in ChromaDB
    const collection = await chromaClient.getCollection({ name: 'political_images' });
    const documentId = uuid();
    
    await collection.add({
      ids: [documentId],
      documents: [description],
      metadatas: [{
        filename,
        width: dimensions.width,
        height: dimensions.height,
        size: buffer.length,
        mimeType: mime.getType(filename),
        processedAt: new Date().toISOString(),
        ...metadata
      }]
    });
    
    multimodalProcessingCounter.inc({ type: 'image', status: 'success' });
    timer();
    
    return {
      id: documentId,
      description,
      dimensions,
      size: buffer.length,
      processedSize: processedBuffer.length
    };
    
  } catch (error) {
    multimodalProcessingCounter.inc({ type: 'image', status: 'error' });
    timer();
    throw error;
  }
}

// Video processing functions
async function processVideo(buffer, filename, metadata = {}) {
  const timer = multimodalProcessingDuration.startTimer({ type: 'video' });
  
  try {
    const tempVideoPath = path.join('/tmp', `${uuid()}_${filename}`);
    const tempFramesDir = path.join('/tmp', `frames_${uuid()}`);
    
    // Write video to temp file
    await fs.writeFile(tempVideoPath, buffer);
    await fs.mkdir(tempFramesDir, { recursive: true });
    
    // Extract frames using FFmpeg
    await new Promise((resolve, reject) => {
      ffmpeg(tempVideoPath)
        .screenshots({
          count: 10, // Extract 10 frames
          folder: tempFramesDir,
          filename: 'frame_%i.png',
          size: '640x480'
        })
        .on('end', resolve)
        .on('error', reject);
    });
    
    // Process extracted frames
    const frameFiles = await fs.readdir(tempFramesDir);
    const frameDescriptions = [];
    
    for (const frameFile of frameFiles.slice(0, 5)) { // Process first 5 frames
      const framePath = path.join(tempFramesDir, frameFile);
      const frameBuffer = await fs.readFile(framePath);
      const base64Frame = frameBuffer.toString('base64');
      
      const visionResponse = await openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Describe this video frame from a political document or presentation. Focus on text, charts, speakers, or political content visible."
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/png;base64,${base64Frame}`
                }
              }
            ]
          }
        ],
        max_tokens: 300
      });
      
      frameDescriptions.push(visionResponse.choices[0].message.content);
    }
    
    // Combine frame descriptions
    const videoDescription = `Video analysis: ${frameDescriptions.join(' | Frame: ')}`;
    
    // Store in ChromaDB
    const collection = await chromaClient.getCollection({ name: 'political_videos' });
    const documentId = uuid();
    
    await collection.add({
      ids: [documentId],
      documents: [videoDescription],
      metadatas: [{
        filename,
        size: buffer.length,
        mimeType: mime.getType(filename),
        framesAnalyzed: frameDescriptions.length,
        processedAt: new Date().toISOString(),
        ...metadata
      }]
    });
    
    // Cleanup temp files
    await fs.unlink(tempVideoPath);
    await fs.rmdir(tempFramesDir, { recursive: true });
    
    multimodalProcessingCounter.inc({ type: 'video', status: 'success' });
    timer();
    
    return {
      id: documentId,
      description: videoDescription,
      framesAnalyzed: frameDescriptions.length,
      size: buffer.length
    };
    
  } catch (error) {
    multimodalProcessingCounter.inc({ type: 'video', status: 'error' });
    timer();
    throw error;
  }
}

// Audio processing functions
async function processAudio(buffer, filename, metadata = {}) {
  const timer = multimodalProcessingDuration.startTimer({ type: 'audio' });
  
  try {
    const tempAudioPath = path.join('/tmp', `${uuid()}_${filename}`);
    const tempWavPath = path.join('/tmp', `${uuid()}_converted.wav`);
    
    // Write audio to temp file
    await fs.writeFile(tempAudioPath, buffer);
    
    // Convert to WAV using FFmpeg for better compatibility
    await new Promise((resolve, reject) => {
      ffmpeg(tempAudioPath)
        .toFormat('wav')
        .audioChannels(1)
        .audioFrequency(16000)
        .on('end', resolve)
        .on('error', reject)
        .save(tempWavPath);
    });
    
    // Read converted audio
    const wavBuffer = await fs.readFile(tempWavPath);
    
    // Transcribe using OpenAI Whisper
    const transcription = await openai.audio.transcriptions.create({
      file: new File([wavBuffer], 'audio.wav', { type: 'audio/wav' }),
      model: 'whisper-1',
      language: 'en',
      response_format: 'verbose_json'
    });
    
    const transcriptText = transcription.text;
    const confidence = transcription.segments?.reduce((acc, seg) => acc + (seg.avg_logprob || 0), 0) / (transcription.segments?.length || 1);
    
    // Enhance transcription with political analysis
    const analysisResponse = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a political analyst. Analyze the following transcription for political content, key topics, sentiment, and important statements."
        },
        {
          role: "user",
          content: `Transcription: "${transcriptText}"`
        }
      ],
      max_tokens: 400
    });
    
    const politicalAnalysis = analysisResponse.choices[0].message.content;
    const combinedText = `${transcriptText} | Political Analysis: ${politicalAnalysis}`;
    
    // Store in ChromaDB
    const collection = await chromaClient.getCollection({ name: 'political_audio' });
    const documentId = uuid();
    
    await collection.add({
      ids: [documentId],
      documents: [combinedText],
      metadatas: [{
        filename,
        size: buffer.length,
        mimeType: mime.getType(filename),
        duration: transcription.duration,
        confidence: confidence,
        language: transcription.language,
        processedAt: new Date().toISOString(),
        ...metadata
      }]
    });
    
    // Cleanup temp files
    await fs.unlink(tempAudioPath);
    await fs.unlink(tempWavPath);
    
    multimodalProcessingCounter.inc({ type: 'audio', status: 'success' });
    timer();
    
    return {
      id: documentId,
      transcription: transcriptText,
      politicalAnalysis,
      duration: transcription.duration,
      confidence,
      size: buffer.length
    };
    
  } catch (error) {
    multimodalProcessingCounter.inc({ type: 'audio', status: 'error' });
    timer();
    throw error;
  }
}

// MCP Tools
const tools = [
  {
    name: "upload_multimodal_content",
    description: "Upload and process images, videos, or audio files for political document analysis",
    inputSchema: {
      type: "object",
      properties: {
        files: {
          type: "array",
          description: "Array of files to process"
        },
        metadata: {
          type: "object",
          description: "Additional metadata for the content"
        }
      },
      required: ["files"]
    }
  },
  {
    name: "search_multimodal_content",
    description: "Search across images, videos, and audio content using semantic similarity",
    inputSchema: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query text"
        },
        modality: {
          type: "string",
          enum: ["image", "video", "audio", "all"],
          description: "Content type to search"
        },
        limit: {
          type: "number",
          description: "Number of results to return",
          default: 10
        }
      },
      required: ["query"]
    }
  },
  {
    name: "analyze_image_content",
    description: "Deep analysis of political image content including text extraction and visual elements",
    inputSchema: {
      type: "object",
      properties: {
        imageId: {
          type: "string",
          description: "ID of the stored image"
        },
        analysisType: {
          type: "string",
          enum: ["text_extraction", "visual_analysis", "political_symbols", "comprehensive"],
          description: "Type of analysis to perform"
        }
      },
      required: ["imageId"]
    }
  },
  {
    name: "get_multimodal_stats",
    description: "Get statistics about stored multimodal content",
    inputSchema: {
      type: "object",
      properties: {
        collection: {
          type: "string",
          enum: ["political_images", "political_videos", "political_audio", "all"],
          description: "Collection to get stats for"
        }
      }
    }
  }
];

// Routes
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    service: 'multimodal-chromadb-mcp'
  });
});

app.get('/metrics', (req, res) => {
  res.set('Content-Type', register.contentType);
  res.end(register.metrics());
});

// MCP tool endpoint
app.post('/mcp/call', upload.array('files'), async (req, res) => {
  try {
    const { tool, arguments: args } = req.body;
    
    if (!tools.find(t => t.name === tool)) {
      return res.status(400).json({ error: 'Unknown tool' });
    }
    
    let result;
    
    switch (tool) {
      case 'upload_multimodal_content':
        result = await handleUploadMultimodalContent(req.files, args);
        break;
      case 'search_multimodal_content':
        result = await handleSearchMultimodalContent(args);
        break;
      case 'analyze_image_content':
        result = await handleAnalyzeImageContent(args);
        break;
      case 'get_multimodal_stats':
        result = await handleGetMultimodalStats(args);
        break;
      default:
        return res.status(400).json({ error: 'Tool not implemented' });
    }
    
    res.json({ result });
    
  } catch (error) {
    logger.error('MCP tool error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Tool handlers
async function handleUploadMultimodalContent(files, args) {
  if (!files || files.length === 0) {
    throw new Error('No files provided');
  }
  
  const results = [];
  
  for (const file of files) {
    const { buffer, originalname, mimetype } = file;
    const metadata = args.metadata || {};
    
    let result;
    
    if (mimetype.startsWith('image/')) {
      result = await processImage(buffer, originalname, metadata);
      result.type = 'image';
    } else if (mimetype.startsWith('video/')) {
      result = await processVideo(buffer, originalname, metadata);
      result.type = 'video';
    } else if (mimetype.startsWith('audio/')) {
      result = await processAudio(buffer, originalname, metadata);
      result.type = 'audio';
    } else {
      throw new Error(`Unsupported file type: ${mimetype}`);
    }
    
    results.push({
      filename: originalname,
      ...result
    });
  }
  
  return {
    message: `Successfully processed ${results.length} files`,
    results
  };
}

async function handleSearchMultimodalContent(args) {
  const { query, modality = 'all', limit = 10 } = args;
  
  const collections = [];
  if (modality === 'all' || modality === 'image') {
    collections.push('political_images');
  }
  if (modality === 'all' || modality === 'video') {
    collections.push('political_videos');
  }
  if (modality === 'all' || modality === 'audio') {
    collections.push('political_audio');
  }
  
  const allResults = [];
  
  for (const collectionName of collections) {
    try {
      const collection = await chromaClient.getCollection({ name: collectionName });
      const searchResults = await collection.query({
        queryTexts: [query],
        nResults: Math.ceil(limit / collections.length)
      });
      
      vectorSearchCounter.inc({ collection: collectionName, modality: modality });
      
      if (searchResults.documents[0]) {
        searchResults.documents[0].forEach((doc, index) => {
          allResults.push({
            id: searchResults.ids[0][index],
            document: doc,
            distance: searchResults.distances[0][index],
            metadata: searchResults.metadatas[0][index],
            collection: collectionName
          });
        });
      }
    } catch (error) {
      logger.error(`Error searching collection ${collectionName}:`, error);
    }
  }
  
  // Sort by relevance (distance) and limit results
  allResults.sort((a, b) => a.distance - b.distance);
  
  return {
    query,
    totalResults: allResults.length,
    results: allResults.slice(0, limit)
  };
}

async function handleAnalyzeImageContent(args) {
  const { imageId, analysisType = 'comprehensive' } = args;
  
  // This would require storing the actual image data or implementing
  // more sophisticated image analysis. For now, return metadata.
  const collection = await chromaClient.getCollection({ name: 'political_images' });
  const result = await collection.get({ ids: [imageId] });
  
  if (!result.documents[0]) {
    throw new Error('Image not found');
  }
  
  return {
    imageId,
    analysisType,
    metadata: result.metadatas[0],
    description: result.documents[0],
    analysis: "Deep image analysis would be implemented here with additional AI models"
  };
}

async function handleGetMultimodalStats(args) {
  const { collection = 'all' } = args;
  
  const stats = {};
  
  const collectionsToCheck = collection === 'all' 
    ? ['political_images', 'political_videos', 'political_audio']
    : [collection];
  
  for (const collectionName of collectionsToCheck) {
    try {
      const coll = await chromaClient.getCollection({ name: collectionName });
      const count = await coll.count();
      stats[collectionName] = {
        totalDocuments: count,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      stats[collectionName] = { error: error.message };
    }
  }
  
  return stats;
}

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Too many files' });
    }
  }
  
  logger.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
async function startServer() {
  try {
    await initializeChromaDB();
    
    app.listen(port, '0.0.0.0', () => {
      logger.info(`Multimodal ChromaDB MCP Server running on port ${port}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();